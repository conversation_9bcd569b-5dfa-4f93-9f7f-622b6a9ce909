import "../src/i18n";

import { ThemeProvider } from 'styled-components';

import { withThemeFromJSXProvider } from '@storybook/addon-styling';
import type { Preview } from '@storybook/react';

import { GlobalStyle } from '../src/style/global';
import theme from '../src/style/theme';

export const decorators = [
  withThemeFromJSXProvider({
    themes: {
      light: theme,
    },
    defaultTheme: 'light',
    Provider: ThemeProvider,
    GlobalStyles: GlobalStyle,
  }),
];

const preview: Preview = {
  parameters: {
    decorators,
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    docs: {
      source: {
        language: 'tsx',
        type: 'dynamic',
      },
    },
  },
};

export default preview;
