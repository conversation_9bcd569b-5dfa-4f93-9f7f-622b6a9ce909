/* eslint-disable @typescript-eslint/no-var-requires,no-undef */

const os = require('os');
const fs = require('fs');
const path = require('path');
const unzipper = require('unzipper');
const uuid = require('uuid');
const { exec } = require('child_process');

const TEMPLATE_FILE = path.join(__dirname, 'icon.template');
const TMP_DIRECTORY = os.tmpdir();
const OUTPUT_DIRECTORY = path.resolve(path.join(__dirname, '../../src/components/common/Icon/presets'));
const ESLINT_COMMAND = 'eslint --fix ./src/components/common/Icon/presets';

main().catch((error) => log(`ERROR: ${error.message}`));

async function main() {
  log('\nImporting SVG Icons...');

  const inputFileName = getInputFileName();
  log(`Processing file: ${inputFileName}`);

  const unzippedDirectory = await unzip(inputFileName);
  log(`Temporarly extracted to: ${unzippedDirectory}`);

  const iconFileNames = getIconFileNames(unzippedDirectory);
  iconFileNames.forEach(processIcon);

  log('Applying ESLint...');
  await applyESLint();

  log('Done.\n');
}

function getInputFileName() {
  const inputFileName = import.meta.env.argv[2];
  if (!inputFileName) {
    throw new Error('Missing input file.');
  }
  return inputFileName;
}

async function unzip(inputFileName) {
  const inputFilePath = path.resolve(inputFileName);
  const outputFolder = path.join(TMP_DIRECTORY, uuid.v4());

  if (!fs.existsSync(inputFilePath)) {
    throw new Error(`File not found: ${inputFileName}`);
  }

  const stream = fs.createReadStream(inputFilePath).pipe(unzipper.Extract({ path: outputFolder }));
  await new Promise((resolve, reject) => stream.on('close', resolve).on('error', reject));

  return outputFolder;
}

function getIconFileNames(directory) {
  const iconFileNames = [];

  for (const fileName of fs.readdirSync(directory)) {
    const fileOrDirPath = path.resolve(path.join(directory, fileName));

    if (fs.lstatSync(fileOrDirPath).isDirectory()) {
      iconFileNames.push(...getIconFileNames(fileOrDirPath));
    } else if (fileName.endsWith('.svg')) {
      iconFileNames.push(fileOrDirPath);
    }
  }

  return iconFileNames;
}

function processIcon(iconFileName) {
  const iconName = toCamelCase(iconFileName.match(/^.*?\/([^/]+)\.svg$/)[1]);

  log(` - Processing icon ${iconName}`);

  const template = fs.readFileSync(TEMPLATE_FILE).toString();
  const iconContent = fs.readFileSync(iconFileName).toString();
  const viewBox = iconContent.match(/viewBox="(\d+ \d+ \d+ \d+)"/)[1];
  const iconPaths = iconContent
    .match(/<(path|rect).*?\/>/g)
    .map((p) =>
      p
        .replace(/(\s)([\w-]+)(="[^"]+")/g, (_, a, b, c) => a + toCamelCase(b, false) + c)
        .replace(/\sfill="[^"]+"/, '')
        .replace(/<(path|rect)/, '<$1 fill={useColor(color)}')
    )
    .join('');

  const iconAttrs = { iconName, viewBox, iconPaths };
  const iconOutput = template.replace(/%\{(\w+)}%/g, (_, attr) => iconAttrs[attr]);
  const iconOutputFile = path.join(OUTPUT_DIRECTORY, `${iconName}.tsx`);

  fs.writeFileSync(iconOutputFile, iconOutput);
}

function toCamelCase(str, capitalizeFirst = true) {
  return str
    .replace(/^(\w)/, (_, a) => (capitalizeFirst ? a.toUpperCase() : a))
    .replace(/(\w)(-|_|\s)(\w)/g, (_, a, __, b) => a + b.toUpperCase());
}

async function applyESLint() {
  await new Promise((resolve, reject) => {
    exec(ESLINT_COMMAND, (error) => {
      error ? reject(error) : resolve();
    });
  });
}

function log(...args) {
  console.log(...args);
}
