#!/bin/sh

echo "Start Renew Certificates"

if [ -d "/etc/letsencrypt/archive/${NGINX_HOST}" ]; then
    echo "Check if actual certificates need to be renewed"

    # will automatically renew both certificate registered
    certbot renew --webroot -w /usr/share/nginx/html --deploy-hook /root/push_cert_to_azure.sh

    isFailedCertbot=$?
    if [ $isFailedCertbot -ne 0 ]; then
        echo "Error during Certbot"
        cat /var/log/letsencrypt/letsencrypt.log
    else
        echo "Script push_cert_to_azure.sh will backup and update eventual renewed certificates."
    fi
fi

echo "End Renew Certificates"
