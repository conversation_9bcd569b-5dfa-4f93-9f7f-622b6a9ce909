#!/bin/sh

# cron does not read env, save it here
env > /root/env

# Get environment variables to show up in SSH session
eval $(printenv | sed -n "s/^\([^=]\+\)=\(.*\)$/export \1=\2/p" | sed 's/"/\\\"/g' | sed '/=/s//="/' | sed 's/$/"/' >> /etc/profile)

# Start SSH Server (for remote login)
/usr/sbin/sshd &

echo "Wait Container to be ready to resolve API DNS"
DNS_IP=""
while [ -z $DNS_IP ]; do
  DNS_IP=$(nslookup ${API_ROOT_URL:-nectar.buzz} | awk '/^Address: / { print $2 }')
  [ -z "$DNS_IP" ] && echo -ne "." && sleep 1
done

# Start Cron
crond -l 2 -f >> /var/log/cron.log 2>&1 &

# setup the proper API Url in FrontEnd
env_type=${ENV_TYPE:-PROD}
if [ $env_type != "PROD" ]; then
    find /usr/share/nginx/html/assets -type f -exec sed -i "s/__API_URL_PLACEHOLDER__/https:\/\/$NGINX_HOST\/api/g" {} \;
else
    find /usr/share/nginx/html/assets -type f -exec sed -i "s/__API_URL_PLACEHOLDER__/https:\/\/$API_ROOT_URL\/api/g" {} \;
fi

# setup the proper Google Map API Key
find /usr/share/nginx/html/assets -type f -exec sed -i "s/__GMAP_API_KEY_PLACEHOLDER__/$GMAP_API_KEY/g" {} \;

# setup the proper Google Tag Manager ID
find /usr/share/nginx/html/assets -type f -exec sed -i "s/__GTM_ID_PLACEHOLDER__/$GTM_ID/g" {} \;

# setup the proper Intercom App ID
find /usr/share/nginx/html/assets -type f -exec sed -i "s/__INTERCOM_APP_ID_PLACEHOLDER__/$INTERCOM_APP_ID/g" {} \;

for CERT_HOST in ${NGINX_HOST} ${ALT_HOST}; do
    export CERT_HOST
    if [ -f "/containersstorage/${NGINX_HOST}/letsencrypt.tar.gz" ]; then
        echo "Get LetsEncrypt backed up certificates"
        mkdir -p /etc/letsencrypt
        cp -rf /containersstorage/${NGINX_HOST}/letsencrypt.tar.gz /tmp
        tar -zxvf /tmp/letsencrypt.tar.gz -C /
        rm /tmp/letsencrypt.tar.gz

        echo "Renew HTTPS Certificates"
        certbot renew --deploy-hook /root/push_cert_to_azure.sh

        if [ $isFailedCertbot -ne 0 ]; then
            echo "Error during Renewing certificates using Certbot"
            cat /var/log/letsencrypt/letsencrypt.log
        fi

    else

        echo "Generate new HTTPS Certificates"
        # Give some time to Azure to update DNS
        sleep 15
        certbot certonly --standalone --preferred-challenges http -d ${CERT_HOST} --cert-name ${CERT_HOST} --email <EMAIL> --non-interactive --agree-tos --keep-until-expiring --deploy-hook /root/push_cert_to_azure.sh
        isFailedCertbot=$?
        echo "isFailedCertbot: "$isFailedCertbot

        if [ $isFailedCertbot -ne 0 ]; then
            echo "Error during Generation of new certificates using Certbot"
            cat /var/log/letsencrypt/letsencrypt.log
        fi

    fi

done

ROOT_LOG_PATH=${ROOT_LOG_PATH:-/var/log}
export ROOT_LOG_PATH
echo "ROOT_LOG_PATH=$ROOT_LOG_PATH"

env_type=${ENV_TYPE:-PROD}
if [ $env_type != "PROD" ]; then
    # Setup Nginx to use HTTPS for self hosted container (dev, staging...)
    export NGINX_HOST
    export API_ROOT_URL
    envsubst '${NGINX_HOST},${API_ROOT_URL},${ROOT_LOG_PATH}' < /root/nginx_dev.template.conf > /etc/nginx/conf.d/default.conf

else
    # Azure 'App Service' will manage HTTPS, no need to setup it here
    envsubst '${NGINX_HOST},${ROOT_LOG_PATH}' < /root/nginx.template.conf > /etc/nginx/conf.d/default.conf
    # sed -e "s/\${NGINX_HOST}/$NGINX_HOST/" /root/nginx.template.conf > /etc/nginx/conf.d/default.conf
fi

mkdir -p ${ROOT_LOG_PATH}/${NGINX_HOST}/logs
touch ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_access.log
touch ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_error.log

# if the file containing the timestamp of the start of logs do not exist yet, create it (cf nginx_log_rotate.sh)
if [ ! -f "${ROOT_LOG_PATH}/${NGINX_HOST}/log_start_timestamp" ]; then
    date +"%Y-%m-%d_%Hh%Mm%Ss" > ${ROOT_LOG_PATH}/${NGINX_HOST}/log_start_timestamp
fi

# Supervisor start Nginx & Filebeat
supervisord -n -c /supervisor.conf &

tail -F -q ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_*.log

# DEBUG backup process if network share have issues
echo "tail following files hosted on network share returned, something wrong is happening..."
tail -f /dev/null
echo "We should never ever get here!"
