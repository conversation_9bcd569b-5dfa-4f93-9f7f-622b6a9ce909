#!/bin/sh

echo "[START] Push new SSL Certificates to Azure"

echo "Backup new certificates on Azure Storage"
mkdir -p /containersstorage/${NGINX_HOST}/letsencrypt
tar -zcvf /tmp/letsencrypt.tar.gz /etc/letsencrypt/
cp -rf /tmp/letsencrypt.tar.gz /containersstorage/${NGINX_HOST} 2>/dev/null
rm /tmp/letsencrypt.tar.gz

echo "...Generate Random Password"
GEN_PASS=$(openssl rand -base64 32)

echo "...Convert new certificate to pfx file"
openssl pkcs12 -export -password pass:$GEN_PASS -out /containersstorage/${NGINX_HOST}/certificate.pfx -inkey /etc/letsencrypt/live/${NGINX_HOST}/privkey.pem -in /etc/letsencrypt/live/${NGINX_HOST}/cert.pem -certfile /etc/letsencrypt/live/${NGINX_HOST}/chain.pem -legacy

if [ -f /etc/letsencrypt/live/${ALT_HOST}/privkey.pem ]; then
    openssl pkcs12 -export -password pass:$GEN_PASS -out /containersstorage/${NGINX_HOST}/certificate-alt.pfx -inkey /etc/letsencrypt/live/${ALT_HOST}/privkey.pem -in /etc/letsencrypt/live/${ALT_HOST}/cert.pem -certfile /etc/letsencrypt/live/${ALT_HOST}/chain.pem -legacy
fi

echo "...Create 'update_pfx' flag file"
touch /containersstorage/${NGINX_HOST}/update_pfx
echo $GEN_PASS > /containersstorage/${NGINX_HOST}/update_pfx

echo "[END] new SSL Certificates to Azure Wep App"
