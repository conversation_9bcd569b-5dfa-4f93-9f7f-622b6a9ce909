#!/bin/sh
# read previous log start timestamp
log_start_timestamp=`cat ${ROOT_LOG_PATH}/${NGINX_HOST}/log_start_timestamp`

# rename current log file
mv ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_access.log ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_access_${log_start_timestamp}.log
mv ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_error.log ${ROOT_LOG_PATH}/${NGINX_HOST}/logs/nginx_error_${log_start_timestamp}.log

# sends nginx a signal causing it to reload its log files
kill -USR1 `cat /var/run/nginx.pid`

# save the timestamp of the start of the new log file
date +"%Y-%m-%d_%Hh%Mm%Ss" > ${ROOT_LOG_PATH}/${NGINX_HOST}/log_start_timestamp
