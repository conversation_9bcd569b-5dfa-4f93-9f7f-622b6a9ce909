
.icon-add:before { content: '\e800'; } /* '' */
.icon-addblocks:before { content: '\e801'; } /* '' */
.icon-addpoi:before { content: '\e802'; } /* '' */
.icon-addyard:before { content: '\e803'; } /* '' */
.icon-alert:before { content: '\e804'; } /* '' */
.icon-archive:before { content: '\e805'; } /* '' */
.icon-arrowdown:before { content: '\e806'; } /* '' */
.icon-arrowleft:before { content: '\e807'; } /* '' */
.icon-arrownegative:before { content: '\e808'; } /* '' */
.icon-arrowpositive:before { content: '\e809'; } /* '' */
.icon-arrowright:before { content: '\e80a'; } /* '' */
.icon-arrowskipleft:before { content: '\e80b'; } /* '' */
.icon-arrowskipright:before { content: '\e80c'; } /* '' */
.icon-arrowsmalldown:before { content: '\e80d'; } /* '' */
.icon-arrowsmallup:before { content: '\e80e'; } /* '' */
.icon-arrowup:before { content: '\e80f'; } /* '' */
.icon-barrel:before { content: '\e810'; } /* '' */
.icon-best:before { content: '\e811'; } /* '' */
.icon-bluetooth:before { content: '\e812'; } /* '' */
.icon-bridge:before { content: '\e813'; } /* '' */
.icon-brood:before { content: '\e814'; } /* '' */
.icon-calendar:before { content: '\e815'; } /* '' */
.icon-center:before { content: '\e816'; } /* '' */
.icon-check:before { content: '\e817'; } /* '' */
.icon-chevrondown:before { content: '\e818'; } /* '' */
.icon-chevronleft:before { content: '\e819'; } /* '' */
.icon-chevronright:before { content: '\e81a'; } /* '' */
.icon-chevronup:before { content: '\e81b'; } /* '' */
.icon-close:before { content: '\e81c'; } /* '' */
.icon-collapse:before { content: '\e81d'; } /* '' */
.icon-contact:before { content: '\e81e'; } /* '' */
.icon-contract:before { content: '\e81f'; } /* '' */
.icon-copy:before { content: '\e820'; } /* '' */
.icon-columns:before { content: '\e821'; } /* '' */
.icon-clock:before { content: '\e822'; } /* '' */
.icon-cues:before { content: '\e823'; } /* '' */
.icon-crown:before { content: '\e824'; } /* '' */
.icon-deadout:before { content: '\e825'; } /* '' */
.icon-delete:before { content: '\e826'; } /* '' */
.icon-dog:before { content: '\e827'; } /* '' */
.icon-download:before { content: '\e828'; } /* '' */
.icon-doorcode:before { content: '\e829'; } /* '' */
.icon-edit:before { content: '\e82a'; } /* '' */
.icon-emptyyard:before { content: '\e82b'; } /* '' */
.icon-exit:before { content: '\e82c'; } /* '' */
.icon-expand:before { content: '\e82d'; } /* '' */
.icon-feeding:before { content: '\e82e'; } /* '' */
.icon-filter:before { content: '\e82f'; } /* '' */
.icon-flashoff:before { content: '\e830'; } /* '' */
.icon-flashon:before { content: '\e831'; } /* '' */
.icon-flood:before { content: '\e832'; } /* '' */
.icon-four:before { content: '\e833'; } /* '' */
.icon-frame:before { content: '\e834'; } /* '' */
.icon-fullscreen:before { content: '\e835'; } /* '' */
.icon-fullscreenexit:before { content: '\e836'; } /* '' */
.icon-gate:before { content: '\e837'; } /* '' */
.icon-groupassign:before { content: '\e838'; } /* '' */
.icon-groupdot:before { content: '\e839'; } /* '' */
.icon-groups:before { content: '\e83a'; } /* '' */
.icon-hazard:before { content: '\e83b'; } /* '' */
.icon-history:before { content: '\e83c'; } /* '' */
.icon-hide:before { content: '\e83d'; } /* '' */
.icon-help:before { content: '\e83e'; } /* '' */
.icon-hivegrading:before { content: '\e83f'; } /* '' */
.icon-hive:before { content: '\e840'; } /* '' */
.icon-hiveadded:before { content: '\e841'; } /* '' */
.icon-hivelost:before { content: '\e842'; } /* '' */
.icon-honey:before { content: '\e843'; } /* '' */
.icon-hiveretired:before { content: '\e844'; } /* '' */
.icon-info:before { content: '\e845'; } /* '' */
.icon-holding:before { content: '\e846'; } /* '' */
.icon-id:before { content: '\e847'; } /* '' */
.icon-inspection:before { content: '\e848'; } /* '' */
.icon-introduced:before { content: '\e849'; } /* '' */
.icon-hivemoved:before { content: '\e84a'; } /* '' */
.icon-indoorwintering:before { content: '\e84b'; } /* '' */
.icon-loading:before { content: '\e84c'; } /* '' */
.icon-mud:before { content: '\e84d'; } /* '' */
.icon-more-vert:before { content: '\e84e'; } /* '' */
.icon-mating:before { content: '\e84f'; } /* '' */
.icon-language:before { content: '\e850'; } /* '' */
.icon-more-hori:before { content: '\e851'; } /* '' */
.icon-mainentrance:before { content: '\e852'; } /* '' */
.icon-logout:before { content: '\e853'; } /* '' */
.icon-notruck:before { content: '\e854'; } /* '' */
.icon-layers:before { content: '\e855'; } /* '' */
.icon-mite:before { content: '\e856'; } /* '' */
.icon-offline:before { content: '\e857'; } /* '' */
.icon-link:before { content: '\e858'; } /* '' */
.icon-one:before { content: '\e859'; } /* '' */
.icon-orchard:before { content: '\e85a'; } /* '' */
.icon-outdoorwintering:before { content: '\e85b'; } /* '' */
.icon-minus:before { content: '\e85c'; } /* '' */
.icon-notes:before { content: '\e85d'; } /* '' */
.icon-pallet:before { content: '\e85e'; } /* '' */
.icon-operation:before { content: '\e85f'; } /* '' */
.icon-pin:before { content: '\e860'; } /* '' */
.icon-pill:before { content: '\e861'; } /* '' */
.icon-pipe:before { content: '\e862'; } /* '' */
.icon-pollination:before { content: '\e863'; } /* '' */
.icon-qr:before { content: '\e864'; } /* '' */
.icon-queenless:before { content: '\e865'; } /* '' */
.icon-queenright:before { content: '\e866'; } /* '' */
.icon-reasons:before { content: '\e867'; } /* '' */
.icon-replacetag:before { content: '\e868'; } /* '' */
.icon-requeened:before { content: '\e869'; } /* '' */
.icon-road:before { content: '\e86a'; } /* '' */
.icon-scan:before { content: '\e86b'; } /* '' */
.icon-scanner:before { content: '\e86c'; } /* '' */
.icon-search:before { content: '\e86d'; } /* '' */
.icon-security:before { content: '\e86e'; } /* '' */
.icon-settings:before { content: '\e86f'; } /* '' */
.icon-show:before { content: '\e870'; } /* '' */
.icon-sideentrance:before { content: '\e871'; } /* '' */
.icon-slider:before { content: '\e872'; } /* '' */
.icon-sortingalphaatoz:before { content: '\e873'; } /* '' */
.icon-sortingalphaztoa:before { content: '\e874'; } /* '' */
.icon-sortingcalendarnewtoold:before { content: '\e875'; } /* '' */
.icon-sortingcalendaroldtonew:before { content: '\e876'; } /* '' */
.icon-sortingnum1to2:before { content: '\e877'; } /* '' */
.icon-sortingnum2to1:before { content: '\e878'; } /* '' */
.icon-sortingtimenewtoold:before { content: '\e879'; } /* '' */
.icon-sortingtimeoldtonew:before { content: '\e87a'; } /* '' */
.icon-steephill:before { content: '\e87b'; } /* '' */
.icon-storage:before { content: '\e87c'; } /* '' */
.icon-supers:before { content: '\e87d'; } /* '' */
.icon-tag:before { content: '\e87e'; } /* '' */
.icon-three:before { content: '\e87f'; } /* '' */
.icon-treatment:before { content: '\e880'; } /* '' */
.icon-turnleft:before { content: '\e881'; } /* '' */
.icon-turnright:before { content: '\e882'; } /* '' */
.icon-two:before { content: '\e883'; } /* '' */
.icon-unarchive:before { content: '\e884'; } /* '' */
.icon-unloading:before { content: '\e885'; } /* '' */
.icon-uturn:before { content: '\e886'; } /* '' */
.icon-viewlist:before { content: '\e887'; } /* '' */
.icon-viewnumber:before { content: '\e888'; } /* '' */
.icon-views:before { content: '\e889'; } /* '' */
.icon-warning:before { content: '\e88a'; } /* '' */
.icon-yard:before { content: '\e88b'; } /* '' */
.icon-zoomin:before { content: '\e88c'; } /* '' */
.icon-zoomout:before { content: '\e88d'; } /* '' */
.icon-hivefixed:before { content: '\e88e'; } /* '' */
