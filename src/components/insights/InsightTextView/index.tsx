import { FC, useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { debounce } from 'lodash';

import { InsightMenu } from '@components/insights/InsightMenu';
import {
  StyledEditableContent,
  StyledEditableContentWrapper,
  StyledFloatingMenuWrapper,
} from '@components/insights/InsightTextView/styles.ts';
import { InsightTextViewProps } from '@components/insights/InsightTextView/types.ts';
import { useDispatch } from '@helpers/Thunk/hooks.ts';
import { useTranslation } from '@hooks/useTranslation.ts';
import { makeInsightSaveThunk } from '@redux/Insights/actions.ts';
import { useCurrentDashboardId } from '@redux/Insights/hooks.ts';

const AUTO_PATCH_INTERVAL = 1000;

export const InsightTextView: FC<InsightTextViewProps> = ({ insight, enableMenu, onLoad, ...boxProps }) => {
  const [content, setContent] = useState('');
  const [focused, setFocused] = useState(false);

  const t = useTranslation();
  const dispatch = useDispatch();
  const dashboardId = useCurrentDashboardId();
  const isFetching = useSelector((state) => !state.insightsReducer.didFetchInsights);

  const patchInsightDebounced = useMemo(() => {
    return debounce((patch: Partial<BeeInsightText>) => {
      dispatch(makeInsightSaveThunk(dashboardId, { ...insight, ...patch } as BeeInsight));
    }, AUTO_PATCH_INTERVAL);
  }, [dashboardId, dispatch, insight]);

  const onContentChanged = useCallback(
    (content: string) => {
      patchInsightDebounced({ content });
      setContent(content);
    },
    [patchInsightDebounced]
  );

  // Updates the local content only once.
  useEffect(() => {
    if (!isFetching) {
      setContent(insight.content);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFetching]);

  useLayoutEffect(() => {
    onLoad?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StyledEditableContentWrapper {...boxProps}>
      <StyledEditableContent
        data-scrollable
        disabled={isFetching}
        value={content}
        placeholder={t('insights:insight_click_to_add_text')}
        onChange={({ target }) => onContentChanged(target.value)}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        $type={insight.type}
      />

      {enableMenu && (
        <StyledFloatingMenuWrapper $visible={!focused}>
          <InsightMenu insight={insight} />
        </StyledFloatingMenuWrapper>
      )}
    </StyledEditableContentWrapper>
  );
};
