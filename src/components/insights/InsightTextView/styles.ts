import styled, { css } from 'styled-components';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';

export const StyledEditableContentWrapper = styled(Box)(
  ({ theme }) => css`
    flex: 1;
    transition: ${theme.animations.transitionMedium('padding')};
    max-width: 100%;
  `
);

export const StyledEditableContent = styled.textarea<{ $type: BeeInsightTextType }>(
  ({ theme, $type }) => css`
    flex: 1;
    outline: none;
    border: transparent 1px solid;
    border-radius: ${theme.borderRadius._050};
    resize: none;
    transition: ${theme.animations.transitionMedium('border')};
    max-width: 100%;

    font-size: ${$type === 'text' ? theme.fontSize._100 : theme.fontSize._150};
    font-weight: ${$type === 'text' ? '400' : '700'};
    margin: ${$type === 'text' ? theme.spacing._025 : 0} 0;

    &:hover {
      border: ${theme.colors.borderSecondary} 1px solid;
    }

    &:focus-visible {
      border: ${theme.colors.borderPrimary} 1px solid;
    }

    &::placeholder {
      color: ${theme.colors.contentTertiary};
      opacity: 0.4;
    }
  `
);

export const StyledFloatingMenuWrapper = styled(Box)<{ $visible: boolean }>(
  ({ theme, $visible }) => css`
    transition: ${theme.animations.transitionMedium('opacity')};
    opacity: ${$visible ? 1 : 0};
  `
);

export const StyledFloatingToolbar = styled(Box)(
  ({ theme }) => css`
    position: absolute;
    flex-direction: column;
    top: ${theme.spacing._300};
    right: ${theme.spacing._050};
    transition: ${theme.animations.transitionMedium('opacity')};
    opacity: 0;
  `
);

export const StyledFloatingToolbarButton = styled(Button)<{ $selected: boolean }>(
  ({ theme, $selected }) => css`
    writing-mode: vertical-lr;
    ${$selected &&
    css`
      background-color: ${theme.colors.surfaceDefault};
    `}
  `
);
