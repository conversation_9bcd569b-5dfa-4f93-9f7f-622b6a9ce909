import { InsightDataView } from '@components/insights/InsightDataView/InsightDataView.tsx';
import { InsightTextView } from '@components/insights/InsightTextView';
import { isTextInsight } from '@components/insights/types.ts';

import { InsightViewProps } from './types.ts';

export const InsightView = ({ insight, ...props }: InsightViewProps) => {
  return isTextInsight(insight) ? (
    <InsightTextView insight={insight} {...props} />
  ) : (
    <InsightDataView insight={insight} {...props} />
  );
};
