import moment from 'moment-timezone';

export const INSIGHT_TEXT_TEMPLATE = (): Omit<BeeInsightText, 'id'> => ({
  title: 'Untitled',
  content: '',
  type: 'text',
});

export const INSIGHT_DATA_VISUALIZATION_TEMPLATE = (): Omit<BeeInsightDataVisualization, 'id'> => ({
  title: 'Untitled',
  description: '',
  type: 'chart-bar',
  typeForComparison: 'chart-line',
  datasetKey: '',
  timeframe: {
    mode: 'relative',
    fixed: { start: moment().subtract(7, 'days').toISOString(), end: moment().toISOString() },
    relative: { amount: 1, unit: 'season' },
    toNow: { start: moment().subtract(7, 'days').toISOString(), maxEnd: moment().add(7, 'days').toISOString() },
  },
  horizontalAxisPropertyKey: '',
  verticalAxisPropertyKey: '',
  verticalAxisAggregation: 'count',
  verticalAxisComparisonPropertyKey: null,
  verticalAxisComparisonAggregation: null,
  horizontalAxisDateGranularity: 'month',
  breakdownPropertyKey: null,
  sorting: null,
  filters: [],
  pagination: null,
  visibleLabels: null,
  visibleComparisonLabels: null,
});

export const INSIGHT_TEMPLATE = <T extends BeeInsightType>(type: T) =>
  ({
    ...INSIGHT_TEXT_TEMPLATE(),
    ...INSIGHT_DATA_VISUALIZATION_TEMPLATE(),
    type,
  } as unknown as T extends 'title' | 'text' ? Omit<BeeInsightText, 'id'> : Omit<BeeInsightDataVisualization, 'id'>);
