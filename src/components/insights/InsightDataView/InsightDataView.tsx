import React from 'react';

import { Box } from '@components/common/Box';
import { useTimeframeFormatted } from '@components/insights/InsightDataView/hooks/formatting.tsx';
import { useDatasetFetcher, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { InsightChartLegendPie } from '@components/insights/InsightDataView/InsightChartLegendPie';
import { InsightChartTitleHorizontal } from '@components/insights/InsightDataView/InsightChartTitleHorizontal';
import { InsightChartTitleVertical } from '@components/insights/InsightDataView/InsightChartTitleVertical';
import { InsightLegend } from '@components/insights/InsightDataView/InsightLegend';
import { InsightPagination } from '@components/insights/InsightDataView/InsightPagination';
import { InsightViewLoading } from '@components/insights/InsightDataView/InsightViewLoading';

import { InsightViewContainer } from './InsightViewContainer';
import { InsightViewHeading } from './InsightViewHeading';
import { StyledInsightBox } from './styles';
import { InsightDataViewProps } from './types';

export const InsightDataView: React.FC<InsightDataViewProps> = ({
  insight,
  enableMenu,
  enableLegendConfig,
  enablePagination,
  staticQuery,
  onLoad,
  ...boxProps
}) => {
  const { isLoading } = useDatasetQuery(insight, { staticQuery });

  useDatasetFetcher(insight, { disabled: !!staticQuery });
  const insightViewsProps = { insight, staticQuery };

  return (
    <StyledInsightBox id={`insight-view-${insight.id}`} relative column stretch {...boxProps}>
      <InsightViewHeading enableMenu={enableMenu} {...insightViewsProps} />

      <Box justifyContent={'space-between'}>
        <Box>{useTimeframeFormatted(insight)}</Box>
        {enablePagination && <InsightPagination {...insightViewsProps} />}
      </Box>

      <Box fit gap_050 marginTop_050 borderRadius_075>
        <InsightChartTitleVertical {...insightViewsProps} />
        <Box column fit>
          <InsightViewContainer onLoad={onLoad} {...insightViewsProps} />
          <InsightChartTitleHorizontal {...insightViewsProps} />
        </Box>
        <InsightChartTitleVertical comparison {...insightViewsProps} />
      </Box>

      <InsightLegend enableLegendConfig={enableLegendConfig} {...insightViewsProps} />
      <InsightChartLegendPie {...insightViewsProps} />
      <InsightViewLoading visible={isLoading} />
    </StyledInsightBox>
  );
};
