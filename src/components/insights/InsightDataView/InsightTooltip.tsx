import { FC, useCallback, useRef, useState } from 'react';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { HiveDefault } from '@components/common/Icon/presets/HiveDefault.tsx';
import { Yard } from '@components/common/Icon/presets/Yard.tsx';
import { Text } from '@components/common/Text';
import { Tooltip } from '@components/common/Tooltip';
import { TooltipRef } from '@components/common/Tooltip/types.ts';
import { useHiveModalOpener } from '@components/hive/HiveModal/hooks.ts';
import { INCLUDED_HIVES_MAX_VISIBLE_ITEMS } from '@components/insights/InsightDataView/constants.ts';
import { useAggregationFormatter, useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting.tsx';
import { useAxisReadyDataset, useDatasetMeta, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query.ts';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling.tsx';
import { StyledTooltipHiveTagList } from '@components/insights/InsightDataView/styles.ts';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types.ts';
import APP from '@config/constants.ts';
import { Children } from '@helpers/Children';
import { HiveUtil } from '@helpers/Hive/HiveUtil.ts';
import { URLUtil } from '@helpers/URL';
import { useTranslation } from '@hooks/useTranslation.ts';

export const InsightTooltip: FC<
  InsightDataViewProps & { index: number; aggregationPropertyKey: string; isComparison?: boolean }
> = ({ insight, staticQuery, index, aggregationPropertyKey, isComparison }) => {
  const [seeingMoreMap, setSeeingMoreMap] = useState<Record<string, boolean>>({});

  const tooltipRef = useRef<TooltipRef>(null);
  const formatAggregation = useAggregationFormatter(insight, { staticQuery });
  const formatValue = useValueFormatter(insight, { staticQuery });
  const getColor = useColorGetter();

  const t = useTranslation();
  const datasetMeta = useDatasetMeta(insight);
  const { query } = useDatasetQuery(insight, { staticQuery });
  const { entries, aggregatedEntries } = useAxisReadyDataset(insight, { staticQuery, skipPaginationSlicing: false });
  const entry = entries[index];

  const hiveModal = useHiveModalOpener();

  const openYard = useCallback((yardId: number) => {
    tooltipRef.current?.hide();
    window.open(
      URLUtil.buildPagePath(APP.routes.whiteboardYardsDetailDashboard, { pathParams: { uid: yardId } }),
      '_blank'
    );
  }, []);

  const openHive = useCallback(
    (hiveId: number) => {
      tooltipRef.current?.hide();
      hiveModal.openHiveModal(hiveId);
    },
    [hiveModal]
  );

  if (!query) return null;

  const aggregations = isComparison ? query?.verticalAxisComparisonAggregations : query?.verticalAxisAggregations;
  const indexOffset = isComparison ? query?.verticalAxisAggregations.length ?? 0 : 0;

  return (
    <Tooltip
      ref={tooltipRef}
      light
      trigger={'mouseenter'}
      hideOnClick={false}
      interactive
      offset={[0, 24]}
      placement={'bottom'}
      renderContent={(children) => (
        <Box padding_100 borderRadius_050 backgroundColor={'surfaceSecondary'} shadow={'boxShadow03'}>
          {children}
        </Box>
      )}
    >
      <Box column stretch gap_100>
        {aggregations.map((aggregation, aggregationIndex) => {
          if (aggregationPropertyKey !== aggregation.aggregatedPropertyKey) return;

          const xValue = formatValue(insight.horizontalAxisPropertyKey, entry[insight.horizontalAxisPropertyKey]);
          const yValue = formatValue(aggregation.aggregatedPropertyKey, entry[aggregation.aggregatedPropertyKey]);
          const color = getColor(aggregationIndex + indexOffset);

          const idProp = datasetMeta?.idPropertyKey;
          const labelProp = datasetMeta ? datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey] : null;

          const aggEntries = aggregation.aggregatedEntriesPropertyKey
            ? aggregatedEntries![index]?.[aggregation.aggregatedEntriesPropertyKey] ?? []
            : [];

          const aggEntriesWithLabel =
            idProp && labelProp
              ? aggEntries.map((entry) => {
                  const id = entry[idProp] as number;
                  return {
                    label: formatValue(labelProp, HiveUtil.formatHiveTag(entry[labelProp] as string)),
                    onClick:
                      id && idProp === 'hive_identity_id'
                        ? () => openHive(id)
                        : idProp === 'yard_id'
                        ? () => openYard(id)
                        : undefined,
                  };
                })
              : [];

          const seeingMore = seeingMoreMap[aggregationIndex];
          const aggEntriesWithLabelSliced = seeingMore
            ? aggEntriesWithLabel
            : aggEntriesWithLabel.slice(0, INCLUDED_HIVES_MAX_VISIBLE_ITEMS);

          const hasSurplusEntries = aggEntriesWithLabel.length > INCLUDED_HIVES_MAX_VISIBLE_ITEMS;
          const surplusAggEntries = Math.max(0, aggEntriesWithLabel.length - aggEntriesWithLabelSliced.length);

          return (
            <Box key={aggregationIndex} column stretch gap_050>
              <Box alignItems={'center'} gap_050>
                <Box paddingLeft_075 paddingTop_075 borderRadius_050 style={{ backgroundColor: color }} />

                <Box alignItems={'center'} gap_050>
                  <Text typography={'Heading3'} weight={'700'}>
                    {formatAggregation(aggregation)}
                  </Text>
                  <Text typography={'Heading3'}>{t('for')}</Text>
                  <Text typography={'Heading3'} weight={'700'}>
                    {xValue}
                  </Text>
                  <Text typography={'Heading3'} weight={'700'}>
                    {'='}
                  </Text>
                  <Text typography={'Heading3'} weight={'700'}>
                    {yValue}
                  </Text>
                </Box>
              </Box>

              {aggEntriesWithLabel.length > 0 && (
                <StyledTooltipHiveTagList>
                  <Box fit alignItems={'center'} gap_050>
                    {datasetMeta?.key === 'yards' ? (
                      <>
                        <Yard />
                        <Text typography={'SmallParagraph'} weight={'600'}>
                          {t('yards_capitalized')}
                        </Text>
                      </>
                    ) : datasetMeta?.key === 'hives' ? (
                      <>
                        <HiveDefault />
                        <Text typography={'SmallParagraph'} weight={'600'}>
                          {t('hives_included')}
                        </Text>
                      </>
                    ) : null}
                  </Box>

                  <Box wrap>
                    {Children.createWithSeparators(aggEntriesWithLabelSliced, {
                      separator: ',',
                      itemWrapper: (item, separator) => (
                        <Button key={item.label} suppressPadding tertiary onClick={item.onClick}>
                          <Text typography={'CTA'} weight={'400'}>
                            {item.label}
                            {separator}
                          </Text>
                        </Button>
                      ),
                    })}
                  </Box>

                  {hasSurplusEntries && (
                    <Button
                      suppressPadding
                      tertiary
                      onClick={() =>
                        setSeeingMoreMap((curr) => ({ ...curr, [aggregationIndex]: !curr[aggregationIndex] }))
                      }
                    >
                      {surplusAggEntries ? t('see_more') : t('see_less')}
                    </Button>
                  )}
                </StyledTooltipHiveTagList>
              )}
            </Box>
          );
        })}
      </Box>
    </Tooltip>
  );
};
