import React, { Fragment, useCallback, useLayoutEffect } from 'react';
import { Bar, ComposedChart, Dot, Line, Rectangle, Text } from 'recharts';
import { useTheme } from 'styled-components';

import {
  BAR_PROPS,
  LINE_PROPS,
  X_AXIS_ID,
  Y_AXIS_ID,
  Y_COMPARISON_AXIS_ID,
} from '@components/insights/InsightDataView/constants';
import {
  useCartesianGrid,
  useXAxis,
  useXYearAxis,
  useYAxis,
} from '@components/insights/InsightDataView/hooks/cartesian';
import { useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import { InsightTooltip } from '@components/insights/InsightDataView/InsightTooltip.tsx';
import { StyledLineChartDotWrapper } from '@components/insights/InsightDataView/styles.ts';
import { InsightChartProps } from '@components/insights/InsightDataView/types';
import { LabelsUtil } from '@components/insights/InsightDataView/util/labels';

const MIN_BAR_HEIGHT = 4;

export const InsightChartCartesian: React.FCWithChildren<InsightChartProps> = ({
  insight,
  staticQuery,
  width,
  height,
  onLoad,
  children,
}) => {
  const theme = useTheme();
  const getAxisColor = useColorGetter();
  const formatValue = useValueFormatter(insight, { staticQuery });
  const { query } = useDatasetQuery(insight, { staticQuery });
  const { entries } = useAxisReadyDataset(insight, { staticQuery, skipPaginationSlicing: false });

  const renderBar = useCallback(
    ({ ...props }, isComparison?: boolean) => {
      if (props.value === null) return <></>;

      if (props.height < MIN_BAR_HEIGHT) {
        props.y = props.y - (MIN_BAR_HEIGHT - props.height);
        props.height = MIN_BAR_HEIGHT;
      }

      if (props.width > theme.primitives.px._600) {
        props.x += (props.width - theme.primitives.px._600) / 2;
        props.width = theme.primitives.px._600;
      }

      const formatted = formatValue(props.dataKey, props.value);

      return (
        <g>
          <Text
            x={props.x + props.width / 2}
            y={props.y - theme.primitives.px._050}
            textAnchor={'middle'}
            fontWeight={'500'}
            fontSize={12}
            fill={props.fill}
          >
            {formatted}
          </Text>
          <Rectangle
            x={props.x}
            y={props.y}
            radius={theme.primitives.px._025}
            width={props.width}
            height={props.height}
            fill={props.fill}
            stroke={props.fill}
            strokeWidth={0}
          />
          <rect x={props.x} y={props.y} width={props.width} height={props.height} fill={'transparent'}>
            <InsightTooltip
              insight={insight}
              staticQuery={staticQuery}
              index={props.index}
              aggregationPropertyKey={props.dataKey}
              isComparison={isComparison}
            />
          </rect>
        </g>
      );
    },
    [formatValue, insight, staticQuery, theme.primitives.px._025, theme.primitives.px._050, theme.primitives.px._600]
  );

  const renderDot = useCallback(
    (props, isComparison?: boolean) => {
      const size = theme.primitives.px._075;
      return props.cx && props.cy ? (
        <StyledLineChartDotWrapper key={props.index} transform={`translate(${props.cx}, ${props.cy})`}>
          <Dot cx={0} cy={0} width={size} height={size} r={size} fill={props.fill} />
          <Dot cx={0} cy={0} width={size} height={size} r={size} fill={'transparent'} />
          <InsightTooltip
            insight={insight}
            staticQuery={staticQuery}
            index={props.index}
            aggregationPropertyKey={props.dataKey}
            isComparison={isComparison}
          />
        </StyledLineChartDotWrapper>
      ) : (
        <Fragment key={props.index} />
      );
    },
    [insight, staticQuery, theme.primitives.px._075]
  );

  const chartType: BeeInsightType = insight.type;
  const chartComparisonType: BeeInsightType = insight.typeForComparison || insight.type;

  const grid = useCartesianGrid();
  const xAxis = useXAxis(insight, { staticQuery });
  const xYearAxis = useXYearAxis(insight, { staticQuery });
  const yAxis = useYAxis(insight, { staticQuery });

  useLayoutEffect(() => {
    onLoad?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ComposedChart data={entries} width={width} height={height}>
      {children}

      {grid}

      {chartType === 'chart-bar' &&
        query?.verticalAxisAggregations.map((agg, index) => {
          if (!LabelsUtil.isAggregationVisible(insight.visibleLabels, agg)) return;
          const color = getAxisColor(index);
          return (
            <Bar
              key={`v${index}`}
              fill={color}
              shape={(props) => renderBar(props)}
              dataKey={agg.aggregatedPropertyKey}
              xAxisId={X_AXIS_ID}
              yAxisId={Y_AXIS_ID}
              {...BAR_PROPS}
            />
          );
        })}

      {chartType === 'chart-line' &&
        query?.verticalAxisAggregations.map((agg, index) => {
          if (!LabelsUtil.isAggregationVisible(insight.visibleLabels, agg)) return;
          const color = getAxisColor(index);
          return (
            <Line
              key={`v${index}`}
              dataKey={agg.aggregatedPropertyKey}
              dot={(props) => renderDot(props)}
              xAxisId={X_AXIS_ID}
              yAxisId={Y_AXIS_ID}
              stroke={color}
              fill={color}
              {...LINE_PROPS}
            />
          );
        })}

      {chartComparisonType === 'chart-bar' &&
        query?.verticalAxisComparisonAggregations.map((agg, index) => {
          if (!LabelsUtil.isComparisonAggregationVisible(insight.visibleComparisonLabels, agg)) return;
          const color = getAxisColor(index + query?.verticalAxisAggregations.length);
          return (
            <Bar
              key={`vc${index}`}
              fill={color}
              shape={(props) => renderBar(props, true)}
              dataKey={agg.aggregatedPropertyKey}
              xAxisId={X_AXIS_ID}
              yAxisId={Y_COMPARISON_AXIS_ID}
              {...BAR_PROPS}
            />
          );
        })}

      {chartComparisonType === 'chart-line' &&
        query?.verticalAxisComparisonAggregations.map((agg, index) => {
          if (!LabelsUtil.isComparisonAggregationVisible(insight.visibleComparisonLabels, agg)) return;
          const color = getAxisColor(index + query?.verticalAxisAggregations.length);
          return (
            <Line
              key={`vc${index}`}
              dataKey={agg.aggregatedPropertyKey}
              dot={(props) => renderDot(props, true)}
              xAxisId={X_AXIS_ID}
              yAxisId={Y_COMPARISON_AXIS_ID}
              stroke={color}
              fill={color}
              {...LINE_PROPS}
            />
          );
        })}

      {xAxis}
      {xYearAxis}
      {yAxis}
    </ComposedChart>
  );
};
