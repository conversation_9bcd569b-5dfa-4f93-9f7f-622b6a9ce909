import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { ChevronLeft } from '@components/common/Icon/presets/ChevronLeft';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Menu } from '@components/common/Menu';
import { Text } from '@components/common/Text';
import { PAGE_SIZES } from '@components/insights/InsightDataView/constants';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useInsightDataVisualizationUpdater } from '@components/insights/InsightDataView/hooks/update.ts';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';
import { Analytics } from '@helpers/Analytics';
import { AnalyticsEventType } from '@helpers/Analytics/types';
import { useTranslation } from '@hooks/useTranslation';

export const InsightPagination: React.FC<InsightDataViewProps> = ({ insight, staticQuery }) => {
  const t = useTranslation();

  const { query } = useDatasetQuery(insight, { staticQuery });

  const [pageIndex, setPageIndex] = useState(insight.pagination?.pageIndex ?? 0);
  const [pageSize, setPageSize] = useState(insight.pagination?.pageSize ?? PAGE_SIZES[1]);
  const pageCount = Math.max(query ? Math.ceil(query.dataset.entries.length / pageSize) : 1, 1);
  const hasManyPages = pageCount > 1;

  const updateInsight = useInsightDataVisualizationUpdater(insight);

  const updateInsightPagination = useCallback(
    (pageSize: number, pageIndex: number) => {
      const pagination = { pageSize, pageIndex };

      updateInsight({ pagination });
    },
    [updateInsight]
  );

  const updatePageSize = useCallback(
    (pageSize: number) => {
      setPageSize(pageSize);
      updateInsightPagination(pageSize, pageIndex);
      Analytics.sendEvent({
        event: AnalyticsEventType.INSIGHTS_INSIGHT_PAGE_SIZE_CHANGE,
      });
    },
    [pageIndex, updateInsightPagination]
  );

  const goToPageIndex = useCallback(
    (pageIndex: number) => {
      setPageIndex(pageIndex);
      updateInsightPagination(pageSize, pageIndex);
      Analytics.sendEvent({
        event: AnalyticsEventType.INSIGHTS_INSIGHT_PAGE_CHANGE,
      });
    },
    [pageSize, updateInsightPagination]
  );

  const pageForward = useCallback(() => {
    const nextPageIndex = (pageIndex + 1) % pageCount;
    goToPageIndex(nextPageIndex);
    updateInsightPagination(pageSize, nextPageIndex);
  }, [goToPageIndex, pageCount, pageIndex, pageSize, updateInsightPagination]);

  const pageBackward = useCallback(() => {
    const previousPageIndex = (pageIndex + pageCount - 1) % pageCount;
    goToPageIndex(previousPageIndex);
    updateInsightPagination(pageSize, previousPageIndex);
  }, [goToPageIndex, pageCount, pageIndex, pageSize, updateInsightPagination]);

  const paginationItems = useMemo(
    () => PAGE_SIZES.map((size) => ({ title: String(size), onClick: () => updatePageSize(size) })),
    [updatePageSize]
  );

  const pageSizeMenuTriggerId = `page-size-${insight.id}`;

  useEffect(() => {
    if (!!query && pageIndex >= pageCount) {
      goToPageIndex(Math.max(pageCount - 1, 0));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageCount, pageIndex]);

  if (['metric', 'map-with-dots'].includes(insight.type)) return null;
  if (!query || query.dataset.entries.length <= PAGE_SIZES[0]) return null;

  return (
    <>
      {hasManyPages && (
        <Box alignItems={'center'} gap_025>
          <Button onClick={pageBackward} suppressPadding data-image-ignore>
            <ChevronLeft size={22} />
          </Button>

          <Text typography={'CaptionSmall'}>
            {t('insights:insight_pagination_label_right', { index: pageIndex + 1, total: pageCount })}
          </Text>

          <Button onClick={pageForward} suppressPadding data-image-ignore>
            <ChevronRight size={22} />
          </Button>
        </Box>
      )}

      <Box alignItems={'center'} gap_050>
        <Text typography={'CaptionSmall'} color={'contentTertiary'}>
          {t('insights:insight_pagination_size')}:
        </Text>
        <Button suppressPadding tertiary id={pageSizeMenuTriggerId}>
          {pageSize}
        </Button>
        <Menu target={pageSizeMenuTriggerId} items={paginationItems} />
      </Box>
    </>
  );
};
