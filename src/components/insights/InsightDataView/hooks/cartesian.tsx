import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { debounce } from 'lodash';
import { CartesianGrid, XAxis, YA<PERSON>s, YAxisProps } from 'recharts';
import { HorizontalCoordinatesGenerator } from 'recharts/types/cartesian/CartesianGrid';
import { Props as XAxisProps } from 'recharts/types/cartesian/XAxis';
import { CSSProperties, useTheme } from 'styled-components';

import { Text } from '@components/common/Text';
import {
  AXIS_PROPS,
  CHART_UPDATE_DEBOUNCE,
  X_AXIS_ID,
  X_AXIS_PROPS,
  X_SECONDARY_AXIS_ID,
  Y_AXIS_ID,
  Y_AXIS_PROPS,
  Y_COMPARISON_AXIS_ID,
} from '@components/insights/InsightDataView/constants';
import { useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetMeta, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { StyledTruncatedTextBox, StyledXTick, StyledYTick } from '@components/insights/InsightDataView/styles';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties';

export function useXAxisDynamicProps(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const theme = useTheme();
  const { query } = useDatasetQuery(insight, { staticQuery: options?.staticQuery });

  return useMemo(() => {
    if (!query) return {};

    const { horizontalAxisPropertyKey } = query;
    const xPropertyMeta = query.properties[horizontalAxisPropertyKey];
    const padding = { left: theme.primitives.px._100, right: theme.primitives.px._100 };
    const type =
      PropertiesUtil.isTemporalProp(xPropertyMeta.type) || PropertiesUtil.isCategoricalProp(xPropertyMeta.type)
        ? 'category'
        : 'number';
    const domain = type === 'number' ? ['dataMin', 'dataMax'] : undefined;

    return {
      padding,
      domain,
    } as XAxisProps;
  }, [query, theme.primitives.px._100]);
}

export function useXAxis(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const theme = useTheme();
  const formatValue = useValueFormatter(insight, { staticQuery: options.staticQuery });
  const { query } = useDatasetQuery(insight, options);
  const { entries } = useAxisReadyDataset(insight, { staticQuery: options.staticQuery, skipPaginationSlicing: false });

  const format = useCallback(
    (value: any) => (query ? formatValue(query.horizontalAxisPropertyKey, value) : ''),
    [formatValue, query]
  );

  const [axisHeight, setAxisHeight] = useState(theme.primitives.px._125);
  const updateAxisHeight = useMemo(() => debounce((height) => setAxisHeight(height), CHART_UPDATE_DEBOUNCE), []);
  const xAxisDynamicProps = useXAxisDynamicProps(insight);

  const xAxisLargerHeight = useMemo(() => {
    if (!query) return 0;
    const formattedEntries = entries?.map((e) => format(e[query.horizontalAxisPropertyKey])) ?? [];
    const formattedEntriesMaxLength = Math.max(2, ...formattedEntries.map((v) => v.length));
    return Math.min(80, formattedEntriesMaxLength * 10);
  }, [entries, format, query]);

  const tick = useCallback(
    (props: any) => {
      const width = props.width / props.visibleTicksCount;
      const shouldRotate = !xAxisDynamicProps.domain && width <= 64;
      const paddedWidth = shouldRotate ? width : Math.max(width - theme.primitives.px._100, 0.0);
      const axisHeight = shouldRotate ? xAxisLargerHeight : theme.primitives.px._125;

      const value = query ? entries[props.index]?.[query.horizontalAxisPropertyKey] : null;
      const formatted = format(value);

      updateAxisHeight(axisHeight);

      const textWrapperStyle: CSSProperties = shouldRotate
        ? {
            width: props.height - 4,
            maxWidth: props.height - 4,
            height: paddedWidth,
            transform: `translateY(${props.height - 4}px) rotate(-90deg)`,
            transformOrigin: 'top left',
            textAlign: 'right',
          }
        : { width: paddedWidth, textAlign: 'center' };
      const textStyle: CSSProperties = shouldRotate ? { lineHeight: `${paddedWidth}px` } : {};

      return (
        <StyledXTick x={props.x - paddedWidth / 2} y={props.y} width={paddedWidth} height={props.height}>
          <StyledTruncatedTextBox style={textWrapperStyle}>
            <Text style={textStyle} typography={'CaptionSmall'} weight={'600'} data-pdf-unselectable={true}>
              {formatted}
            </Text>
          </StyledTruncatedTextBox>
        </StyledXTick>
      );
    },
    [
      entries,
      format,
      query,
      theme.primitives.px._100,
      theme.primitives.px._125,
      updateAxisHeight,
      xAxisDynamicProps.domain,
      xAxisLargerHeight,
    ]
  );

  if (!query || options.staticQuery) return;

  return (
    <XAxis
      dataKey={query.horizontalAxisPropertyKey}
      axisLine={false}
      tickLine={false}
      height={axisHeight}
      xAxisId={X_AXIS_ID}
      tick={tick}
      {...xAxisDynamicProps}
      {...X_AXIS_PROPS}
      {...AXIS_PROPS}
    />
  );
}

export function useXYearAxis(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const formatValue = useValueFormatter(insight, { staticQuery: options.staticQuery });
  const { query } = useDatasetQuery(insight, options);
  const xAxisDynamicProps = useXAxisDynamicProps(insight, options.staticQuery);

  const format = useCallback(
    (value: any) => {
      if (!query) return null;
      return formatValue(query.horizontalAxisPropertyKey, value, { dateformat: 'YYYY' });
    },
    [formatValue, query]
  );

  const tick = useCallback(
    (props: any) => {
      if (!query) return <StyledXTick />;

      const width = props.width / props.visibleTicksCount;
      const formatted = format(props.payload.value);

      return (
        <StyledXTick x={props.x - width / 2.0} y={props.y} width={width} height={props.height}>
          <Text typography={'CaptionSmall'} weight={'600'} data-pdf-unselectable={true}>
            {formatted}
          </Text>
        </StyledXTick>
      );
    },
    [format, query]
  );

  if (!query || options.staticQuery) return null;

  const isDatetimeAxis = PropertiesUtil.isTemporalProp(query.properties[query.horizontalAxisPropertyKey].type);
  const isRequiredGranularity = ['day', 'month', 'week'].includes(insight.horizontalAxisDateGranularity);
  if (!isDatetimeAxis || !isRequiredGranularity) return null;

  return (
    <XAxis
      dataKey={query.horizontalAxisPropertyKey}
      axisLine={false}
      tickLine={false}
      xAxisId={X_SECONDARY_AXIS_ID}
      tick={tick}
      {...xAxisDynamicProps}
      {...X_AXIS_PROPS}
      {...AXIS_PROPS}
    />
  );
}

export function useYAxis(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const theme = useTheme();
  const formatValue = useValueFormatter(insight, { staticQuery: options.staticQuery });
  const datasetMeta = useDatasetMeta(insight);
  const { query, isQueryEmpty } = useDatasetQuery(insight, options);

  const getYAxisDynamicProps = useCallback(
    (orientation: YAxisProps['orientation']) => {
      if (!query || isQueryEmpty || !datasetMeta) return {};

      const tick = (props) => {
        let x;
        let propertyKey;
        if (orientation === 'left') {
          x = Math.max(props.x - props.width, 0.0);
          propertyKey = query.verticalAxisAggregations[0].aggregatedPropertyKey;
        } else {
          x = props.x - theme.primitives.px._050;
          propertyKey = query.verticalAxisComparisonAggregations[0].aggregatedPropertyKey;
        }

        const formatted = formatValue(propertyKey, props.payload.value);
        const height = 18;
        const backgroundTopOffset = 4;

        return (
          <>
            {props.index === 0 && (
              <rect
                x={x}
                y={backgroundTopOffset}
                width={props.width + theme.primitives.px._050}
                height={'100%'}
                fill={theme.colors.surfaceSecondary}
              />
            )}
            <StyledYTick
              x={x}
              y={props.y - height / 2}
              width={props.width + theme.primitives.px._050}
              height={height}
              $orientation={orientation}
            >
              <Text typography={'CaptionSmall'} weight={'600'} data-pdf-unselectable={true}>
                {formatted}
              </Text>
            </StyledYTick>
          </>
        );
      };

      const isPercentageAxis =
        (orientation === 'left' &&
          PropertiesUtil.isPercentageProp(datasetMeta.properties[insight.verticalAxisPropertyKey]?.type)) ||
        (orientation === 'right' &&
          PropertiesUtil.isPercentageProp(
            datasetMeta.properties[insight.verticalAxisComparisonPropertyKey as string].type
          )) ||
        (orientation === 'left' && query.verticalAxisAggregations[0].aggregationType === 'percent') ||
        (orientation === 'right' && query.verticalAxisComparisonAggregations[0].aggregationType === 'percent');

      let maxNumericValue = -1;
      let widthInCharts = 0;
      if (isPercentageAxis) {
        widthInCharts = '100.0%'.length;
      }

      query.dataset.entries.forEach((entry) => {
        const props =
          orientation === 'left' ? query.verticalAxisAggregations : query.verticalAxisComparisonAggregations;
        props.forEach(({ aggregatedPropertyKey }) => {
          const value = entry[aggregatedPropertyKey];
          const formatted = formatValue(aggregatedPropertyKey, value);
          const truncated = formatted.split('.')[0];
          widthInCharts = Math.max(truncated.length, widthInCharts);

          if (typeof value === 'number') {
            maxNumericValue = Math.max(maxNumericValue, value);
          }
        });
      });
      const width = widthInCharts * 6 + theme.primitives.px._050;
      const padding = { top: theme.primitives.px._125, bottom: theme.primitives.px._050 };
      const domain = [0, 'auto'];

      if (isPercentageAxis) {
        domain[1] = 1.0;
      } else if (maxNumericValue <= 4) {
        domain[1] = maxNumericValue * 1.2;
      }

      const allowDecimals = isPercentageAxis;

      return { width, padding, tick, domain, allowDecimals } as YAxisProps;
    },
    [
      query,
      isQueryEmpty,
      datasetMeta,
      insight.verticalAxisPropertyKey,
      insight.verticalAxisComparisonPropertyKey,
      theme.primitives.px._050,
      theme.primitives.px._125,
      theme.colors.surfaceSecondary,
      formatValue,
    ]
  );

  return (
    <>
      {!!query?.verticalAxisAggregations.length && (
        <YAxis
          key={Y_AXIS_ID}
          yAxisId={Y_AXIS_ID}
          orientation={'left'}
          stroke={theme.primitives.colors.grey06}
          {...AXIS_PROPS}
          {...Y_AXIS_PROPS}
          {...getYAxisDynamicProps('left')}
        />
      )}
      {!!query?.verticalAxisComparisonAggregations.length && (
        <YAxis
          key={Y_COMPARISON_AXIS_ID}
          yAxisId={Y_COMPARISON_AXIS_ID}
          orientation={'right'}
          stroke={theme.primitives.colors.grey06}
          {...AXIS_PROPS}
          {...Y_AXIS_PROPS}
          {...getYAxisDynamicProps('right')}
        />
      )}
    </>
  );
}

export function useCartesianGrid(options = { disabled: false }) {
  const theme = useTheme();
  const horizontalCoordinatesGenerator = useCallback<HorizontalCoordinatesGenerator>((props) => {
    const { yAxis } = props;
    if (!yAxis) return [];
    const { niceTicks, scale } = yAxis;
    return niceTicks.map(scale);
  }, []);

  if (options.disabled) return null;

  return (
    <CartesianGrid
      vertical={false}
      stroke={theme.primitives.colors.grey04}
      horizontalCoordinatesGenerator={horizontalCoordinatesGenerator}
    />
  );
}
