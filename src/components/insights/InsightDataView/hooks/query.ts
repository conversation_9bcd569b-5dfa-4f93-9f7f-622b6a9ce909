import { useCallback, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { debounce, sum, unzip, zip } from 'lodash';

import { PAGE_SIZES } from '@components/insights/InsightDataView/constants';
import { Caching } from '@components/insights/InsightDataView/util/caching';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties';
import { DatabaseQueryUtil } from '@components/insights/InsightDataView/util/query';
import { useDispatch } from '@helpers/Thunk/hooks';
import { makeDatasetFetchThunk } from '@redux/Insights/actions';

const UPDATE_DEBOUNCE = 80;
const ERROR_LOG_DEBOUNCE = 80;

const debouncedErrorLog = debounce(console.error, ERROR_LOG_DEBOUNCE);

/**
 * This is a way to know if we're currently under the builder page.
 * It's not perfect, but I couldn't find a more straightforward way to
 * do it.
 * */
export function useInsightsEnv() {
  const history = useHistory();
  const isUnderInsightBuilder = /insights\/dashboard\/.*?\/builder.*?/.test(history.location.pathname);
  return { isUnderInsightBuilder };
}

export function usePropertyList(insight: BeeInsightDataVisualization | null) {
  const datasetMeta = useDatasetMeta(insight);
  const isFetchingDatabaseMeta = useSelector((state) => state.insightsReducer.isFetchingDatasetMeta);
  const { isUnderInsightBuilder } = useInsightsEnv();

  return useMemo(() => {
    // If there isn't an insights object, or if the current
    // page is the insights builder, the property list is empty,
    // meaning all props are retrieved.
    if (isFetchingDatabaseMeta || !insight || isUnderInsightBuilder) {
      return [];
    }

    const properties = [
      insight.horizontalAxisPropertyKey,
      insight.verticalAxisPropertyKey,
      insight.verticalAxisComparisonPropertyKey,
      ...insight.filters.map((f) => f.propertyKey),
    ].filter(Boolean) as Array<string>;

    Object.entries(datasetMeta?.labelPropertyKeys ?? {}).forEach(([k, v]) => {
      if (properties.includes(v)) {
        properties.push(k);
      }
    });

    if (datasetMeta?.properties.geolocation) {
      properties.push('geolocation'); // Always include geolocation.
    }

    return properties;
  }, [
    insight,
    datasetMeta?.labelPropertyKeys,
    datasetMeta?.properties.geolocation,
    isFetchingDatabaseMeta,
    isUnderInsightBuilder,
  ]);
}

export function useDatasetMeta(insight: BeeInsightDataVisualization | null): BeeInsightsDatasetMeta | null {
  return useSelector((state) => (insight ? state.insightsReducer.datasetsMeta[insight.datasetKey] ?? null : null));
}

export function useDataset(insight: BeeInsightDataVisualization | null): BeeInsightsDataset | null {
  const datasetRequestKey = useDatasetRequestKey(insight);
  return useSelector((state) => (datasetRequestKey ? state.insightsReducer.datasets[datasetRequestKey] : null));
}

export function useIsFetchingDataset(insight: BeeInsightDataVisualization | null): boolean {
  const datasetRequestKey = useDatasetRequestKey(insight);
  return useSelector((state) => !!datasetRequestKey && state.insightsReducer.isFetchingDataset[datasetRequestKey]);
}

export function useDatasetRequestKey(insight: BeeInsightDataVisualization | null) {
  const propertyList = usePropertyList(insight);
  const seasonStart = useSelector((state) => state.operationReducer.operation?.seasonStart) ?? null;
  return insight ? Caching.getDatasetRequestKey(insight, propertyList, seasonStart) : null;
}
/**
 * Fetches the dataset required for the given insight.
 * */
export function useDatasetFetcher(insight: BeeInsightDataVisualization | null, options = { disabled: false }) {
  const propertyList = usePropertyList(insight);
  const dispatch = useDispatch();
  const datasetRequestKey = useDatasetRequestKey(insight);
  const datasetMeta = useDatasetMeta(insight);
  const operation = useSelector((state) => state.operationReducer.operation);

  useEffect(() => {
    if (options.disabled || !datasetMeta || !insight || !datasetRequestKey || !operation) return;

    const tid = setTimeout(() => {
      dispatch(makeDatasetFetchThunk(insight, datasetRequestKey, propertyList));
    }, UPDATE_DEBOUNCE);

    return () => clearTimeout(tid);
  }, [datasetMeta, datasetRequestKey, dispatch, insight, operation, options.disabled, propertyList]);
}

/**
 * Returns the database query for the given insight.
 * */
export function useDatasetQuery(
  insight: BeeInsightDataVisualization | null,
  options = { staticQuery: null as any }
): {
  query: BeeInsightsDatasetQuery | null;
  isQueryEmpty: boolean;
  isQueryZeroed: boolean;
  isLoading: boolean;
} {
  const datasetRequestKey = useDatasetRequestKey(insight);
  const datasetMeta = useDatasetMeta(insight);
  const dataset = useDataset(insight);
  const seasonStart = useSelector((state) => state.operationReducer.operation?.seasonStart) ?? null;

  let isLoading = useSelector((state) =>
    datasetRequestKey ? state.insightsReducer.isFetchingDataset[datasetRequestKey] : false
  );
  if (options.staticQuery) {
    isLoading = false;
  }

  const query = useMemo(() => {
    if (options.staticQuery) return options.staticQuery;

    if (!insight || !datasetMeta || !dataset) return null;

    try {
      const { output } = DatabaseQueryUtil.query({
        dataset,
        datasetMeta,
        insight,
        seasonStart,
      });
      return output;
    } catch (error) {
      debouncedErrorLog("Can't perform query:", error);
      return null;
    }
  }, [options.staticQuery, datasetMeta, dataset, insight, seasonStart]);

  const isQueryEmpty = useMemo(() => {
    return !isLoading && !query?.dataset.entries.length;
  }, [isLoading, query?.dataset.entries.length]);

  const isQueryZeroed = useMemo(() => {
    if (!query || !query.dataset.entries.length) return false;
    return query.dataset.entries.every((entry) =>
      query.verticalAxisAggregations.every(({ propertyKey }) => {
        return entry[propertyKey] === 0;
      })
    );
  }, [query]);

  return { query, isQueryEmpty, isQueryZeroed, isLoading };
}

/**
 * Returns the dataset entries of the given insight sorted.
 * */
export function useDatabaseEntriesSorter(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const { query } = useDatasetQuery(insight, { staticQuery: options.staticQuery });

  return useCallback(
    (
      entries: Array<[BeeInsightsDatasetEntry | undefined, Record<string, Array<BeeInsightsDatasetEntry>> | undefined]>,
      propertyKeys: Array<BeeInsightsDatasetPropertyKey>
    ) => {
      if (!query) return [];

      return [...entries].sort(([a], [b]) => {
        const aValues = propertyKeys.map((p) => a?.[p] ?? 0);
        const bValue = propertyKeys.map((p) => b?.[p] ?? 0);

        const propertyType = query.properties[propertyKeys[0]]?.type;
        const isNumericProp = PropertiesUtil.isNumericProp(propertyType);

        if (isNumericProp) {
          return sum(aValues) - sum(bValue);
        }

        return aValues.join('-').localeCompare(bValue.join('-'));
      });
    },
    [query]
  );
}

export function useAxisReadyDataset(
  insight: BeeInsightDataVisualization,
  options = { skipPaginationSlicing: false, staticQuery: null as any }
) {
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery: options.staticQuery });
  const sort = useDatabaseEntriesSorter(insight, { staticQuery: options.staticQuery });

  return useMemo(() => {
    const pagination = insight.pagination || { pageIndex: 0, pageSize: PAGE_SIZES[1] };
    if (!query || isQueryEmpty) return { entries: [], pagination };

    let zippedEntries = zip(query.dataset.entries, query.dataset.aggregatedEntries);

    if (insight.sorting) {
      zippedEntries = sort(zippedEntries, [insight.sorting.propertyKey]);
      if (insight.sorting.direction === 'desc') {
        zippedEntries = zippedEntries.reverse();
      }
    } else if (PropertiesUtil.isSequentialProp(query.properties[query.horizontalAxisPropertyKey].type)) {
      zippedEntries = sort(zippedEntries, [query.horizontalAxisPropertyKey]);
    } else {
      zippedEntries = sort(
        zippedEntries,
        query.verticalAxisAggregations.map((p) => p.aggregatedPropertyKey)
      ).reverse();
    }

    if (!options.skipPaginationSlicing) {
      zippedEntries = zippedEntries.slice(
        pagination.pageIndex * pagination.pageSize,
        (pagination.pageIndex + 1) * pagination.pageSize
      );
    }

    const [entries, aggregatedEntries] = unzip(zippedEntries) as [
      Array<BeeInsightsDatasetEntry>,
      Array<Record<string, Array<BeeInsightsDatasetEntry>>>
    ];
    return { entries, aggregatedEntries, pagination };
  }, [insight, isQueryEmpty, options.skipPaginationSlicing, query, sort]);
}
