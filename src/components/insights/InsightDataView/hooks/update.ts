import { useCallback, useContext } from 'react';

import { InsightBuilderContext } from '@components/insights/InsightBuilder/context.ts';
import { useDispatch } from '@helpers/Thunk/hooks.ts';
import { makeInsightSaveThunk } from '@redux/Insights/actions.ts';
import { useCurrentDashboardId } from '@redux/Insights/hooks.ts';

export function useInsightDataVisualizationUpdater(insight: BeeInsightDataVisualization) {
  const context = useContext(InsightBuilderContext);
  const dispatch = useDispatch();
  const currentDashboardId = useCurrentDashboardId();

  return useCallback(
    (changes: Partial<BeeInsightDataVisualization>) => {
      if (!currentDashboardId) return;

      const isUnderInsightBuilder = !!context;

      if (isUnderInsightBuilder) {
        context.updateInsight({ ...changes });
      } else {
        dispatch(makeInsightSaveThunk(currentDashboardId, { ...insight, ...changes }));
      }
    },
    [context, currentDashboardId, dispatch, insight]
  );
}
