import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { truncate } from 'lodash';
import moment from 'moment-timezone';

import { Box } from '@components/common/Box';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Text } from '@components/common/Text';
import { TextProps } from '@components/common/Text/types.ts';
import { CHART_GRANULARITY_READABLE_FORMATS } from '@components/insights/InsightDataView/constants';
import { useDatasetMeta, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { StyledDivider, StyledFormattedLabel } from '@components/insights/InsightDataView/styles';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties';
import { useTranslation } from '@hooks/useTranslation';

export function useTimeframeFormatted(insight: BeeInsightDataVisualization, options = { bold: false }) {
  const t = useTranslation();
  const { timeframe } = insight;

  const textStyle = useMemo<Omit<TextProps, 'typography'>>(
    () => (options.bold ? { weight: '600' } : { color: 'contentTertiary' }),
    [options.bold]
  );

  return useMemo(() => {
    if (timeframe.mode === 'fixed') {
      return (
        <Box center gap_025>
          <Text typography={'CaptionSmall'} {...textStyle}>
            {moment(timeframe.fixed.start).format('MMM DD, YYYY')}
          </Text>
          <ChevronRight size={18} />
          <Text typography={'CaptionSmall'} {...textStyle}>
            {moment(timeframe.fixed.end).format('MMM DD, YYYY')}
          </Text>
        </Box>
      );
    }

    if (timeframe.mode === 'to-now') {
      const now = moment(new Date());
      const maxEnd = timeframe.toNow.maxEnd ? moment(timeframe.toNow.maxEnd) : now;

      return (
        <Box center gap_025>
          <Text typography={'CaptionSmall'} {...textStyle}>
            {moment(timeframe.toNow.start).format('MMM DD, YYYY')}
          </Text>
          <ChevronRight size={18} />
          <Text typography={'CaptionSmall'} {...textStyle}>
            {now.isSameOrBefore(maxEnd) ? t('insights:builder_timeframe_now') : maxEnd.format('MMM DD, YYYY')}
          </Text>
        </Box>
      );
    }

    if (timeframe.relative.unit === 'season' && timeframe.relative.amount === 1) {
      return (
        <Box center gap_025>
          <Text typography={'CaptionSmall'} {...textStyle}>
            {t('insights:builder_timeframe_current_season')}
          </Text>
        </Box>
      );
    }

    return (
      <Box center gap_025>
        <Text typography={'CaptionSmall'} {...textStyle}>
          {t('insights:builder_timeframe_last_x_unit', {
            amount: timeframe.relative.amount,
            unit: (timeframe.relative.amount === 1
              ? t(`insights:builder_granularity_${timeframe.relative.unit}`)
              : t(`insights:builder_granularity_${timeframe.relative.unit}s`)
            ).toLowerCase(),
          })}
        </Text>
      </Box>
    );
  }, [
    timeframe.mode,
    timeframe.relative.unit,
    timeframe.relative.amount,
    timeframe.fixed.start,
    timeframe.fixed.end,
    timeframe.toNow.maxEnd,
    timeframe.toNow.start,
    textStyle,
    t,
  ]);
}

export function usePropertyFormatter(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const databaseMeta = useDatasetMeta(insight);
  const { query } = useDatasetQuery(insight, options);

  return useCallback(
    ({ originPropertyKey }: Partial<BeeInsightsVerticalAxisAggregation>) => {
      if (!databaseMeta || !originPropertyKey) return;
      const property = query?.properties[originPropertyKey] || databaseMeta.properties[originPropertyKey];
      return property?.label;
    },
    [databaseMeta, query]
  );
}

export interface AggregationFormatterOptions {
  /** Appended to the end of the formatted output. */
  suffix?: string;

  /**
   * Truncates the formatted output.
   * */
  maxLength?: number;

  /**
   * In some cases, the property name is hidden to save visual space.
   * This makes it always visible.
   * */
  alwaysUsePropName?: boolean;
}

export function useAggregationFormatter(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const t = useTranslation();
  const formatValue = useValueFormatter(insight, { staticQuery: options.staticQuery });
  const formatProperty = usePropertyFormatter(insight, options);
  const datasetMeta = useSelector((state) => state.insightsReducer.datasetsMeta[insight.datasetKey]);

  return useCallback(
    (aggregation: Partial<BeeInsightsVerticalAxisAggregation>, options?: AggregationFormatterOptions) => {
      const { originPropertyKey, aggregationType, aggregatedValue } = aggregation;
      const hasAggregation = !!aggregationType;
      const readableProperty = truncate(formatProperty(aggregation), { length: options?.maxLength ?? Infinity });

      if (!hasAggregation) {
        return readableProperty;
      }

      const valueReadable = truncate(
        formatValue(originPropertyKey as BeeInsightsDatasetPropertyKey, aggregatedValue as BeeInsightsDatasetValue),
        { length: options?.maxLength ?? Infinity }
      );

      const aggregationReadable = !hasAggregation
        ? null
        : t(`insights:builder_vertical_aggregation_${aggregationType}`);

      if (!aggregation.isSimpleAggregation && !options?.alwaysUsePropName) {
        const isVerticalIdProp =
          datasetMeta && aggregation.originPropertyKey == datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey];
        const isAVGAggregation = aggregationType === 'avg';
        const usePropertyAsPrefix = isVerticalIdProp || isAVGAggregation;
        return (
          <StyledFormattedLabel center tag={'span'} gap_025 data-pdf-unselectable={true}>
            {usePropertyAsPrefix ? readableProperty : valueReadable}
            <StyledDivider />
            <b data-pdf-unselectable={true}>
              {aggregationReadable}
              {options?.suffix}
            </b>
          </StyledFormattedLabel>
        );
      }

      return (
        <StyledFormattedLabel center tag={'span'} gap_025 data-pdf-unselectable={true}>
          {readableProperty} <StyledDivider />
          <b data-pdf-unselectable={true}>
            {aggregationReadable}
            {options?.suffix}
          </b>
        </StyledFormattedLabel>
      );
    },
    [datasetMeta, formatProperty, formatValue, t]
  );
}

export function useValueFormatter(insight: BeeInsightDataVisualization, options = { staticQuery: null as any }) {
  const t = useTranslation();
  const datasetMeta = useDatasetMeta(insight);
  const { query } = useDatasetQuery(insight, options);

  const formatDatetime = useCallback((value: any, format: string) => {
    return moment(value).format(format);
  }, []);

  const formatBool = useCallback(
    (value: any) => {
      if (value === null) {
        return t('insights:insight_chart_not_set_value');
      }
      return t(`insights:insight_chart_bool_${value ? 'yes' : 'no'}`);
    },
    [t]
  );

  const formatCategorical = useCallback(
    (value: any, maxLength: number) => {
      let formatted;
      if (value === null || value === '') {
        formatted = t('insights:insight_chart_not_set_value');
      } else {
        formatted = String(value);
      }

      if (formatted.includes(':')) {
        const [prefix, label] = formatted.split(':');
        return [
          truncate(prefix, { length: Math.floor(maxLength / 2) }),
          truncate(label, { length: Math.floor(maxLength / 2) }),
        ]
          .filter(Boolean)
          .join(': ');
      }
      return formatted;
    },
    [t]
  );

  const formatNumber = useCallback(
    (value: any, aggregationType: BeeInsightsAggregationType, propertyKey: BeeInsightsDatasetPropertyKey) => {
      if (value === null || value === undefined || isNaN(value)) {
        return t('insights:insight_chart_not_set_value');
      }

      const propType = datasetMeta?.properties[propertyKey]?.type;

      if (aggregationType === 'percent') {
        return `${(100 * value).toFixed(1)}%`;
      } else if (propType && PropertiesUtil.isPercentageProp(propType)) {
        return `${value.toFixed(1)}%`;
      }

      return String(Math.round((value as number) * 1e2) / 1e2);
    },
    [datasetMeta?.properties, t]
  );

  return useCallback(
    (
      propertyKey: BeeInsightsDatasetPropertyKey,
      value: BeeInsightsDatasetValue,
      options?: {
        maxLength?: number;
        dateformat?: string;
      }
    ) => {
      if (!query && !datasetMeta) return '-';

      const _options = {
        maxLength: 48,
        dateformat: CHART_GRANULARITY_READABLE_FORMATS[insight.horizontalAxisDateGranularity],
        ...options,
      };

      const properties = query?.properties || datasetMeta?.properties || {};
      const verticalAxisAggregations = query?.verticalAxisAggregations ?? [];

      let readableValue = String(value);
      const type = properties[propertyKey]?.type;
      const aggregation = verticalAxisAggregations.find((a) => a.aggregatedPropertyKey === propertyKey);
      const aggregationType = aggregation?.aggregationType ?? 'count';

      if (PropertiesUtil.isTemporalProp(type)) {
        readableValue = formatDatetime(value, _options.dateformat);
      } else if (PropertiesUtil.isBoolProp(type)) {
        readableValue = formatBool(value);
      } else if (PropertiesUtil.isCategoricalProp(type)) {
        readableValue = formatCategorical(value, _options.maxLength);
      } else if (PropertiesUtil.isNumericProp(type)) {
        readableValue = formatNumber(value, aggregationType, propertyKey);
      }

      return truncate(readableValue, { length: _options.maxLength });
    },
    [
      datasetMeta,
      formatBool,
      formatCategorical,
      formatDatetime,
      formatNumber,
      insight.horizontalAxisDateGranularity,
      query,
    ]
  );
}
