import React, { useCallback } from 'react';
import { Tooltip } from 'recharts';
import { TooltipProps } from 'recharts/types/component/Tooltip';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import { useAggregationFormatter, useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';

export function useTooltip(insight: BeeInsightDataVisualization, options = { disabled: false }) {
  const { query, isQueryEmpty } = useDatasetQuery(insight);
  const formatValue = useValueFormatter(insight);
  const formatProperty = useAggregationFormatter(insight);

  const tooltipRender = useCallback(
    (props: TooltipProps<any, BeeInsightsDatasetPropertyKey>) => {
      if (!props.payload?.length || !query || isQueryEmpty) {
        return null;
      }

      const payloads = props.payload;
      const aggregationValues = payloads.map((payload) => {
        const verticalProperty = payload.dataKey;
        const aggregation = [...query.verticalAxisAggregations, ...query.verticalAxisComparisonAggregations].find(
          (a) => a.aggregatedPropertyKey === verticalProperty
        );
        const value = verticalProperty ? payload.payload[verticalProperty] : null;
        return { aggregation, value, color: payload.color ?? payload.payload.fill };
      });

      const horizontalProperty = query.horizontalAxisPropertyKey;
      const horizontalValue = payloads[0].payload[horizontalProperty];

      return (
        <Box
          backgroundColor={'surfaceSecondary'}
          column
          padding_050
          gap_050
          borderRadius_075
          borderWidth={1}
          borderColor={'borderSecondary'}
        >
          {aggregationValues.map(({ aggregation, value, color }, index) => (
            <Box key={index} gap_025>
              <Text typography={'CaptionSmall'}>
                {aggregation ? formatProperty(aggregation, { suffix: ':' }) : '-'}
              </Text>
              <Text typography={'CaptionSmall'} weight={'700'} style={{ color }}>
                {aggregation ? formatValue(aggregation.aggregatedPropertyKey, value) : '-'}
              </Text>
            </Box>
          ))}

          <Box gap_025>
            <Text typography={'CaptionSmall'}>
              {formatProperty({ originPropertyKey: horizontalProperty, aggregationType: null })}:
            </Text>
            <Text typography={'CaptionSmall'} weight={'700'}>
              {formatValue(String(horizontalProperty), horizontalValue)}
            </Text>
          </Box>
        </Box>
      );
    },
    [formatProperty, formatValue, isQueryEmpty, query]
  );

  if (options.disabled) return null;

  return <Tooltip content={tooltipRender} cursor={false} />;
}
