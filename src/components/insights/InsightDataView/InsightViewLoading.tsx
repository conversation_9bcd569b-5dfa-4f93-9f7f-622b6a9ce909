import React from 'react';

import { Box } from '@components/common/Box';
import { LoadingSkeleton } from '@components/common/LoadingSkeleton';
import { LoadingSkeletonRectangle } from '@components/common/LoadingSkeleton/LoadingSkeletonRectangle';
import { LoadingSkeletonProps } from '@components/common/LoadingSkeleton/types';

export const InsightViewLoading: React.FC<LoadingSkeletonProps> = ({ ...props }) => {
  return (
    <LoadingSkeleton
      backgroundColor={'surfaceSecondary'}
      column
      absolutelyFitParent
      borderRadius_075
      gap_100
      padding_100
      {...props}
    >
      <LoadingSkeletonRectangle fit borderRadius={'paperRadius02'} />

      <Box alignItems={'center'} gap_100>
        <LoadingSkeletonRectangle height={32} width={64} borderRadius={'paperRadius03'} />
        <LoadingSkeletonRectangle height={32} width={72} borderRadius={'paperRadius03'} />
      </Box>
    </LoadingSkeleton>
  );
};
