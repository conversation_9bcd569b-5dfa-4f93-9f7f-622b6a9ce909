import React from 'react';

import { useAggregationFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { StyledVerticalLegend } from '@components/insights/InsightDataView/styles';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';

export const InsightChartTitleVertical: React.FC<InsightDataViewProps & { comparison?: boolean }> = ({
  insight,
  staticQuery,
  comparison,
}) => {
  const formatProperty = useAggregationFormatter(insight, { staticQuery });
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery });

  if (!query || isQueryEmpty || !['chart-line', 'chart-bar'].includes(insight.type)) return null;

  const aggregation = comparison ? query.verticalAxisComparisonAggregations[0] : query.verticalAxisAggregations[0];

  if (!aggregation) return null;

  return (
    <StyledVerticalLegend typography={'CaptionSmall'} weight={'600'} data-pdf-unselectable={true}>
      {formatProperty({ ...aggregation }, { alwaysUsePropName: true })}
    </StyledVerticalLegend>
  );
};
