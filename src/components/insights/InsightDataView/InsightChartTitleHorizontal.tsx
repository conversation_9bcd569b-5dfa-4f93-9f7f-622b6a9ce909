import React from 'react';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import { useAggregationFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';

export const InsightChartTitleHorizontal: React.FC<InsightDataViewProps> = ({ insight, staticQuery }) => {
  const formatProperty = useAggregationFormatter(insight, { staticQuery });
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery });

  if (!query || isQueryEmpty || !['chart-line', 'chart-bar'].includes(insight.type)) return null;

  return (
    <Box center marginTop_050>
      <Text typography={'CaptionSmall'} weight={'600'} align={'center'}>
        {formatProperty({ originPropertyKey: query.horizontalAxisPropertyKey })}
      </Text>
    </Box>
  );
};
