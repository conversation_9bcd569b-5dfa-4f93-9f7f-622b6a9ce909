import React, { ReactNode, useCallback, useLayoutEffect, useMemo } from 'react';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import {
  METRIC_MAX_VISIBLE_ITEMS,
  METRIC_TEXT_MAX_SIZE,
  METRIC_TEXT_PADDING,
} from '@components/insights/InsightDataView/constants.ts';
import { useAggregationFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import { LabelsUtil } from '@components/insights/InsightDataView/util/labels';
import { PostQueryUtil } from '@components/insights/InsightDataView/util/postQuery.ts';

import { StyledMetrics, StyledMetricSquare, StyledMetricTitle } from './styles';
import { InsightChartProps } from './types';

interface MetricValue {
  value: number | string;
  label: ReactNode;
  aggregation: BeeInsightsVerticalAxisAggregation;
}

export const InsightMetric: React.FC<InsightChartProps> = ({ insight, staticQuery, width, height, onLoad }) => {
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery });
  const { entries } = useAxisReadyDataset(insight, { skipPaginationSlicing: true, staticQuery });
  const getColor = useColorGetter();

  const formatAggregation = useAggregationFormatter(insight, { staticQuery });

  const sum = useMemo(
    () => PostQueryUtil.getAllAggregationsSum(entries, query?.verticalAxisAggregations ?? []),
    [entries, query?.verticalAxisAggregations]
  );

  const sumComparison = useMemo(
    () => PostQueryUtil.getAllAggregationsSum(entries, query?.verticalAxisComparisonAggregations ?? []),
    [entries, query?.verticalAxisComparisonAggregations]
  );

  const calculateValueAndLabel = useCallback(
    (aggregation: BeeInsightsVerticalAxisAggregation, sum: number) => {
      const valueAndLabel: MetricValue = {
        value: 0.0,
        label: formatAggregation(aggregation, { maxLength: 16 }),
        aggregation,
      };

      if (query && !isQueryEmpty) {
        const values = entries
          .filter(
            (e) => e[aggregation.aggregatedPropertyKey] !== null && e[aggregation.aggregatedPropertyKey] !== undefined
          )
          .map((e) => e[aggregation.aggregatedPropertyKey]) as Array<number>;

        if (values.length === 0) {
          valueAndLabel.value = ' \n...\n ';
        } else {
          const aggregationType = aggregation.aggregationType;
          if ([null, 'count', 'sum'].includes(aggregationType)) {
            valueAndLabel.value = values.reduce((acc, v) => acc + v, 0);
          } else if (aggregationType === 'avg') {
            valueAndLabel.value = values.reduce((acc, v) => acc + v, 0) / values.length;
          } else if (aggregationType === 'min') {
            valueAndLabel.value = Math.min(...values);
          } else if (aggregationType === 'max') {
            valueAndLabel.value = Math.max(...values);
          } else if (aggregationType === 'percent') {
            valueAndLabel.value = `${((100 * values.reduce((acc, v) => acc + v, 0)) / sum).toFixed(1)}%`;
          }
        }
      }

      return valueAndLabel;
    },
    [entries, formatAggregation, isQueryEmpty, query]
  );

  const metrics = useMemo(() => {
    const values: Array<{ metric: MetricValue; visible: boolean }> = [];
    if (query && !isQueryEmpty) {
      let visibleCount = 0;
      values.push(
        ...query.verticalAxisAggregations.map((aggregation) => ({
          metric: calculateValueAndLabel(aggregation, sum),
          visible: LabelsUtil.isAggregationVisible(insight.visibleLabels, aggregation),
        }))
      );
      values.push(
        ...query.verticalAxisComparisonAggregations.map((aggregation) => ({
          metric: calculateValueAndLabel(aggregation, sumComparison),
          visible: LabelsUtil.isComparisonAggregationVisible(insight.visibleComparisonLabels, aggregation),
        }))
      );
      values.forEach((metric) => {
        if (visibleCount >= METRIC_MAX_VISIBLE_ITEMS) {
          metric.visible = false;
        }
        if (metric.visible) {
          visibleCount += 1;
        }
      });
    }
    return { values };
  }, [
    calculateValueAndLabel,
    insight.visibleComparisonLabels,
    insight.visibleLabels,
    isQueryEmpty,
    query,
    sum,
    sumComparison,
  ]);

  const getReadableValue = useCallback((value: number | string) => {
    if (typeof value === 'number') {
      return value.toLocaleString(navigator.language, {
        compactDisplay: 'short',
        notation: 'compact',
        maximumFractionDigits: 1,
      });
    }
    return value;
  }, []);

  const renderMetric = useCallback(
    ({ metric, visible }: { metric: MetricValue; visible: boolean }, index: number) => {
      if (!visible) return;

      if (metric.aggregation.aggregatedPropertyKey.includes('health_score_pred')) {
        // TODO: Handle this when this property get added again.
        // <Gauge
        //   key={index}
        //   style={{ width, height }}
        //   value={Math.round(value * 10)}
        //   size={Math.min(width, height)}
        //   textProps={{ typography: 'Heading2', weight: '700', style: { fontSize: fontSize * 0.5 } }}
        // />
      }

      return (
        <StyledMetricSquare
          key={index}
          fit
          column
          stretch
          gap_050
          paddingVertical_050
          paddingHorizontal_200
          borderRadius_100
          style={{ backgroundColor: getColor(index) }}
        >
          <Box fit>
            <StyledMetricTitle
              textMaxSize={METRIC_TEXT_MAX_SIZE}
              textPadding={METRIC_TEXT_PADDING}
              color={'surfacePrimaryReverse'}
              typography={'Heading1'}
              weight={'700'}
              heading={
                <Text color={'surfacePrimaryReverse'} typography={'CaptionSmall'} data-pdf-unselectable={true}>
                  {metric.label}
                </Text>
              }
            >
              {getReadableValue(metric.value)}
            </StyledMetricTitle>
          </Box>
        </StyledMetricSquare>
      );
    },
    [getColor, getReadableValue]
  );

  useLayoutEffect(() => {
    onLoad?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!query || !width) return null;

  return (
    <StyledMetrics stretch wrap center gap_050 style={{ width, height }}>
      {metrics.values.map(renderMetric)}
    </StyledMetrics>
  );
};
