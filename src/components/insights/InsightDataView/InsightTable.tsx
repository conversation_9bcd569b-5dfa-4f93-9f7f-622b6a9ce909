import React, { useCallback, useLayoutEffect, useMemo } from 'react';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import { useAggregationFormatter, useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useInsightDataVisualizationUpdater } from '@components/insights/InsightDataView/hooks/update.ts';
import {
  StyledHeaderButton,
  StyledHeaderButtonArrowDouble,
  StyledHeaderButtonArrowDown,
  StyledHeaderButtonArrowUp,
  StyledTable,
  StyledTableWrapper,
} from '@components/insights/InsightDataView/styles';
import { InsightChartProps } from '@components/insights/InsightDataView/types';

export const InsightTable: React.FC<InsightChartProps> = ({ insight, staticQuery, width, height, onLoad }) => {
  const { query } = useDatasetQuery(insight, { staticQuery });
  const formatAggregation = useAggregationFormatter(insight, { staticQuery });
  const formatValue = useValueFormatter(insight, { staticQuery });
  const { entries } = useAxisReadyDataset(insight, { staticQuery, skipPaginationSlicing: false });
  const updateInsight = useInsightDataVisualizationUpdater(insight);

  const aggregations = useMemo(() => {
    const aggregations: Array<BeeInsightsVerticalAxisAggregation> = [];

    if (query) {
      aggregations.push(...query.verticalAxisAggregations);
      aggregations.push(...query.verticalAxisComparisonAggregations);
    }

    return aggregations;
  }, [query]);

  const toggleSorting = useCallback(
    (propertyKey: BeeInsightsDatasetPropertyKey) => {
      let direction: BeeInsightSortingDirection = 'asc';
      if (propertyKey === insight.sorting?.propertyKey && insight.sorting?.direction === 'asc') {
        direction = 'desc';
      }
      updateInsight({ sorting: { propertyKey, direction } });
    },
    [insight.sorting?.direction, insight.sorting?.propertyKey, updateInsight]
  );

  const renderSortIcon = useCallback(
    (propertyKey: BeeInsightsDatasetPropertyKey) => {
      return insight.sorting?.propertyKey === propertyKey ? (
        insight.sorting?.direction === 'asc' ? (
          <StyledHeaderButtonArrowUp size={16} />
        ) : (
          <StyledHeaderButtonArrowDown size={16} />
        )
      ) : (
        <StyledHeaderButtonArrowDouble />
      );
    },
    [insight.sorting?.direction, insight.sorting?.propertyKey]
  );

  useLayoutEffect(() => {
    onLoad?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!query) return;

  return (
    <Box center style={{ width, height }}>
      <StyledTableWrapper data-scrollable>
        <StyledTable>
          <thead>
            <tr>
              <th>
                <StyledHeaderButton
                  flat
                  suppressPadding
                  withLeadingIcon
                  onClick={() => toggleSorting(query!.horizontalAxisPropertyKey)}
                >
                  <Text typography={'Heading3'} weight={'600'}>
                    {formatAggregation({ originPropertyKey: query.horizontalAxisPropertyKey })}
                  </Text>

                  {renderSortIcon(query.horizontalAxisPropertyKey)}
                </StyledHeaderButton>
              </th>
              {aggregations.map((aggregation) => (
                <th key={aggregation.aggregatedPropertyKey}>
                  <StyledHeaderButton
                    flat
                    suppressPadding
                    withLeadingIcon
                    onClick={() => toggleSorting(aggregation.aggregatedPropertyKey)}
                  >
                    <Text typography={'Heading3'} weight={'600'}>
                      {formatAggregation(aggregation)}
                    </Text>

                    {renderSortIcon(aggregation.aggregatedPropertyKey)}
                  </StyledHeaderButton>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {entries.map((entry, index) => (
              <tr key={index}>
                <td>
                  <Text typography={'Heading3'} weight={'600'} data-pdf-unselectable={true}>
                    {formatValue(query.horizontalAxisPropertyKey, entry[query.horizontalAxisPropertyKey])}
                  </Text>
                </td>
                {aggregations.map((aggregation) => (
                  <td key={aggregation.aggregatedPropertyKey}>
                    <Text typography={'Heading3'} weight={'600'} data-pdf-unselectable={true}>
                      {formatValue(aggregation.aggregatedPropertyKey, entry[aggregation.aggregatedPropertyKey])}
                    </Text>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </StyledTable>
      </StyledTableWrapper>
    </Box>
  );
};
