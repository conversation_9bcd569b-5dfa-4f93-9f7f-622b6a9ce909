import { Pie<PERSON>hart, YAxisProps } from 'recharts';
import styled, { css } from 'styled-components';

import { AutoScaledText } from '@components/common/AutoScaledText/AutoScaledText.tsx';
import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { ArrowDown } from '@components/common/Icon/presets/ArrowDown.tsx';
import { ArrowsVertical } from '@components/common/Icon/presets/ArrowsVertical.tsx';
import { ArrowUp } from '@components/common/Icon/presets/ArrowUp.tsx';
import { Text } from '@components/common/Text';

export const StyledInsightBox = styled(Box)(
  () => css`
    max-width: 100%;
    overflow: hidden;

    b,
    strong {
      font-weight: 700;
    }
  `
);

export const StyledRestrictedBox = styled(Box)(
  () => css`
    max-width: 100%;
  `
);

export const StyledTruncatedTextBox = styled(Box)(
  () => css`
    max-width: 100%;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
  `
);

export const StyledChartContainer = styled(Box)(
  ({ theme }) => css`
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 1;
    max-width: 100%;
    max-height: 100%;

    position: relative;

    .recharts-yAxis.yAxis {
      background-color: ${theme.colors.surfaceSecondary};
    }
  `
);

export const StyledChartContainerInner = styled(Box)(
  () => css`
    position: absolute;
    inset: 0;
    overflow: hidden;
  `
);

export const StyledChartOverlays = styled(Box)(
  () => css`
    position: absolute;
    overflow: hidden;
    inset: 0;
  `
);

export const StyledChartLegendItem = styled(Box).attrs({
  suppressPadding: true,
})(
  ({ theme }) => css`
    max-width: 100%;
    padding: ${theme.spacing._025} ${theme.spacing._050};
    border-radius: ${theme.borderRadius._100};
    border: ${theme.colors.borderSecondary} 1px solid;
  `
);

export const StyledChartLegendDot = styled(Box)(
  ({ theme }) => css`
    min-width: ${theme.spacing._075};
    height: ${theme.spacing._075};
    border-radius: 50%;
  `
);

export const StyledChartLegendScroll = styled(Box)(
  ({ theme }) => css`
    overflow: hidden auto;
    max-height: 360px;

    button:hover {
      background-color: ${theme.colors.surfaceSecondaryHover};
    }
  `
);

export const StyledXTick = styled.foreignObject(
  () => css`
    text-align: center;
  `
);

export const StyledYTick = styled.foreignObject<{ $orientation: YAxisProps['orientation'] }>(
  ({ theme, $orientation }) => css`
    text-align: ${$orientation === 'left' ? 'right' : 'left'};

    ${$orientation === 'left'
      ? css`
          padding-right: ${theme.spacing._050};
        `
      : css`
          padding-left: ${theme.spacing._050};
        `};
  `
);

export const StyledMetrics = styled(Box)(
  () => css`
    align-content: stretch;
  `
);

export const StyledMetricSquare = styled(Box)(
  () => css`
    min-width: 128px;
  `
);

export const StyledMetricTitle = styled(AutoScaledText)(
  () => css`
    text-align: center;
  `
);

export const StyledFormattedLabel = styled(Box)(
  () => css`
    white-space: nowrap;
  `
);

export const StyledDivider = styled(Box)(
  () => css`
    width: 1px;
    margin: 3px 0;
    background-color: currentColor;
    align-self: stretch;
  `
);

export const StyledVerticalLegend = styled(Text)(
  ({ theme }) => css`
    text-align: center;
    writing-mode: vertical-lr;
    transform: rotate(180deg);

    ${StyledDivider} {
      height: 1px;
      width: ${theme.spacing._075};
      margin-left: 2px;
    }
  `
);

export const StyledHeadTitleWrapper = styled(StyledTruncatedTextBox)(
  ({ theme }) => css`
    max-width: calc(100% - ${theme.spacing._100});
  `
);

export const StyledTableWrapper = styled.div(
  ({ theme }) => css`
    width: 100%;
    height: 100%;
    overflow: auto;
    border: ${theme.colors.borderSecondary} 1px solid;
  `
);

export const StyledTable = styled.table(
  ({ theme }) => css`
    min-width: 100%;
    min-height: 100%;

    thead {
      position: sticky;
      top: 0;
      background-color: ${theme.colors.surfaceSecondary};
      box-shadow: ${theme.shadows.boxShadow01};
    }

    thead tr,
    tbody tr:not(:last-child) {
      border-bottom: ${theme.colors.borderSecondary} 1px solid;
    }

    th {
      white-space: nowrap;
    }

    td {
      padding: ${theme.spacing._050} ${theme.spacing._075};
    }

    th,
    td {
      text-align: left;

      & h3,
      span {
        text-align: left;
        justify-content: flex-start;
      }
    }

    tbody {
      overflow: hidden auto;
    }
  `
);

export const StyledHeaderButton = styled(Button)(
  ({ theme }) => css`
    width: 100%;
    padding: ${theme.spacing._050} ${theme.spacing._075};

    &:hover {
      ${StyledHeaderButtonArrowDown}, ${StyledHeaderButtonArrowUp}, ${StyledHeaderButtonArrowDouble} {
        opacity: 1;
      }
    }
  `
);

export const StyledHeaderButtonArrowDouble = styled(ArrowsVertical)`
  opacity: 0.0;
`;
export const StyledHeaderButtonArrowUp = styled(ArrowUp)`
  opacity: 0.7;
`;
export const StyledHeaderButtonArrowDown = styled(ArrowDown)`
  opacity: 0.7;
`;

export const StyledPieChart = styled(PieChart)(
  ({ theme }) => css`
    transform-origin: center;
    transition: ${theme.animations.transitionLong('width', 'height', 'transform')};

    .recharts-pie-sector path {
      transition: ${theme.animations.transitionLong('opacity')};
    }
  `
);

export const StyledEmptyPieChartPlaceholder = styled(Box)(
  () => css`
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
  `
);

export const StyledEmptyPieChartText = styled(Text)(
  () => css`
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  `
);

export const StyledPieChartWrapper = styled(Box)(
  ({ theme }) => `
  border-bottom: ${theme.colors.borderSecondary} 1px solid;
`
);

export const StyledSettingsContainer = styled(Box)<{ $floating: boolean }>(
  ({ theme, $floating }) => css`
    border-radius: ${theme.borderRadius._025};
    background-color: ${theme.colors.surfaceSecondary};

    ${$floating &&
    css`
      position: absolute;
      left: ${theme.spacing._150};
      bottom: ${theme.spacing._150};
      overflow: clip;
    `}
  `
);

export const StyledTooltipHiveTagList = styled(Box).attrs({
  column: true,
  gap_025: true,
  padding_050: true,
  borderRadius_050: true,
  borderWidth: 1,
  borderColor: 'borderSecondary',
})(
  () => css`
    max-width: 388px;
  `
);

export const StyledLineChartDotWrapper = styled.g(
  ({ theme }) => css`
    & > circle:first-child {
      transition: ${theme.animations.transitionFast('transform')};
      transform: scale(0.25);
    }

    &:hover > circle:first-child {
      transform: scale(1);
    }
  `
);
