import React from 'react';

import { Text } from '@components/common/Text';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { StyledHeadTitleWrapper, StyledRestrictedBox } from '@components/insights/InsightDataView/styles';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';
import { InsightMenu } from '@components/insights/InsightMenu';

export const InsightViewHeading: React.FC<InsightDataViewProps> = ({
  insight,
  enableMenu,
  staticQuery,
  ...boxProps
}) => {
  const { isLoading } = useDatasetQuery(insight, { staticQuery });

  if (!insight.title) return;

  return (
    <StyledRestrictedBox
      gapHorizontal_050
      gapVertical_025
      justifyContent={'space-between'}
      alignItems={'flex-start'}
      {...boxProps}
    >
      <StyledHeadTitleWrapper>
        <Text typography={'Heading3'} weight={'600'} htmlTag={'span'}>
          {insight.title}
        </Text>
      </StyledHeadTitleWrapper>

      {enableMenu && !isLoading && <InsightMenu insight={insight} staticQuery={staticQuery} />}
    </StyledRestrictedBox>
  );
};
