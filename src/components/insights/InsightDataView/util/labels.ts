export const LabelsUtil = {
  isAggregationVisible,
  isComparisonAggregationVisible,
};

function isAggregationVisible(
  visibleLabels: BeeInsightDataVisualization['visibleLabels'],
  aggregation: BeeInsightsVerticalAxisAggregation
) {
  const visible = visibleLabels?.[aggregation.aggregatedPropertyKey];
  return visible === undefined || visible;
}

function isComparisonAggregationVisible(
  visibleComparisonLabels: BeeInsightDataVisualization['visibleComparisonLabels'],
  aggregation: BeeInsightsVerticalAxisAggregation
) {
  const visible = visibleComparisonLabels?.[aggregation.aggregatedPropertyKey];
  return visible === undefined || visible;
}
