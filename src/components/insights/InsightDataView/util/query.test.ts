import { describe, expect, test, vi } from 'vitest';

import { DatabaseQueryUtil } from '@components/insights/InsightDataView/util/query.ts';

describe('Query', () => {
  test('X=Id, Y=Numerical', () => {
    const options = generateMockedQueryOptions(
      {
        horizontalAxisPropertyKey: 'label',
        verticalAxisPropertyKey: 'y',
      },
      [
        { id: 1, label: 'Entry 1', y: 10 },
        { id: 2, label: 'Entry 2', y: 20 },
      ],
      {
        y: { key: 'y', type: 'integer', label: '' },
      }
    );
    const { output } = DatabaseQueryUtil.query(options);

    expect(output.horizontalAxisPropertyKey).toBe('label');
    expect(output.verticalAxisAggregations).toStrictEqual([
      {
        originPropertyKey: 'y',
        aggregatedPropertyKey: 'y',
        aggregatedEntriesPropertyKey: null,
        aggregationType: null,
        aggregatedValue: null,
        isSimpleAggregation: true,
      },
    ]);
    expect(output.dataset.entries).toStrictEqual([
      { id: 1, label: 'Entry 1', y: 10 },
      { id: 2, label: 'Entry 2', y: 20 },
    ]);
    expect(output.dataset.aggregatedEntries).toStrictEqual([]);
  });

  test('X=Id, Y=Categorical', () => {
    const options = generateMockedQueryOptions(
      {
        horizontalAxisPropertyKey: 'label',
        verticalAxisPropertyKey: 'y',
        verticalAxisAggregation: 'count',
      },
      [
        { id: 1, label: 'Entry 1', y: 'A' },
        { id: 2, label: 'Entry 2', y: 'A' },
        { id: 3, label: 'Entry 3', y: 'B' },
      ],
      {
        y: { key: 'y', type: 'enum', label: '' },
      }
    );
    const { output } = DatabaseQueryUtil.query(options);

    expect(output.horizontalAxisPropertyKey).toBe('label');
    expect(output.verticalAxisAggregations).toStrictEqual([
      {
        originPropertyKey: 'y',
        aggregatedPropertyKey: 'y__count__A',
        aggregatedEntriesPropertyKey: 'y__count__A__entries',
        aggregationType: 'count',
        aggregatedValue: 'A',
        isSimpleAggregation: false,
      },
      {
        originPropertyKey: 'y',
        aggregatedPropertyKey: 'y__count__B',
        aggregatedEntriesPropertyKey: 'y__count__B__entries',
        aggregationType: 'count',
        aggregatedValue: 'B',
        isSimpleAggregation: false,
      },
    ]);
    expect(output.dataset.entries).toStrictEqual([
      { label: 'Entry 1', y__count__A: 1 },
      { label: 'Entry 2', y__count__A: 1 },
      { label: 'Entry 3', y__count__B: 1 },
    ]);
    expect(output.dataset.aggregatedEntries).toStrictEqual([
      {
        y__count__A__entries: options.dataset.entries.slice(0, 1),
      },
      {
        y__count__A__entries: options.dataset.entries.slice(1, 2),
      },
      {
        y__count__B__entries: options.dataset.entries.slice(2, 3),
      },
    ]);
  });

  test('X=Categorical, Y=Numerical', () => {
    const options = generateMockedQueryOptions(
      {
        horizontalAxisPropertyKey: 'x',
        verticalAxisPropertyKey: 'y',
        verticalAxisAggregation: 'sum',
      },
      [
        { id: 1, label: 'Entry 1', x: 'A', y: 1 },
        { id: 2, label: 'Entry 2', x: 'A', y: 2 },
        { id: 3, label: 'Entry 3', x: 'B', y: 4 },
      ],
      {
        x: { key: 'x', type: 'enum', label: '' },
        y: { key: 'y', type: 'integer', label: '' },
      }
    );
    const { output } = DatabaseQueryUtil.query(options);

    expect(output.horizontalAxisPropertyKey).toBe('x');
    expect(output.verticalAxisAggregations).toStrictEqual([
      {
        originPropertyKey: 'y',
        aggregatedPropertyKey: 'y__sum',
        aggregatedEntriesPropertyKey: null,
        aggregationType: 'sum',
        aggregatedValue: null,
        isSimpleAggregation: true,
      },
    ]);
    expect(output.dataset.entries).toStrictEqual([
      { x: 'A', y__sum: 3 },
      { x: 'B', y__sum: 4 },
    ]);
    expect(output.dataset.aggregatedEntries).toStrictEqual([
      {
        y__sum__entries: options.dataset.entries.slice(0, 2),
      },
      {
        y__sum__entries: options.dataset.entries.slice(2, 3),
      },
    ]);
  });
});

function generateMockedQueryOptions<E extends BeeInsightsDatasetEntry>(
  insight: Partial<
    Pick<
      BeeInsightDataVisualization,
      'horizontalAxisPropertyKey' | 'verticalAxisPropertyKey' | 'verticalAxisAggregation'
    >
  >,
  entries: Array<E>,
  properties: Omit<Record<keyof E, BeeInsightsDatasetPropertyMeta>, 'id' | 'label'>
): any {
  return {
    insight: {
      ...insight,
      timeframe: new Proxy({}, { get: () => vi.fn() }),
      filters: [],
    },
    dataset: {
      entries: [...entries],
    },
    datasetMeta: {
      idPropertyKey: 'id',
      labelPropertyKeys: { id: 'label' },
      properties: {
        id: { key: 'id', type: 'id', label: '' },
        label: { key: 'label', type: 'string', label: '' },
        ...properties,
      },
    },
  };
}
