class InsightValidationError extends Error {}

export const ValidationUtil = {
  InsightValidationError,

  validateInsightForSubmission,
  validateInsightForQuery,
  validateInsightDataset,
  validateInsightQuery,
};

function validateInsightForSubmission(insight: BeeInsightDataVisualization) {
  const hasNotTitle = insight.title.trim().length === 0;
  if (hasNotTitle) throw new InsightValidationError("The insight doesn't have a valid title");

  validateInsightForQuery(insight);
}

function validateInsightForQuery(insight: BeeInsightDataVisualization) {
  const hasNotXProp = !insight.horizontalAxisPropertyKey;
  if (hasNotXProp) throw new InsightValidationError("The insight doesn't have a valid horizontal property");

  const hasNotYProp = !insight.verticalAxisPropertyKey;
  if (hasNotYProp) throw new InsightValidationError("The insight doesn't have a valid vertical property");
}

function validateInsightQuery(query: BeeInsightsDatasetQuery | null) {
  const isQueryNull = !query;
  if (isQueryNull) throw new InsightValidationError('The output query is null');

  const isVerticalAggregationsEmpty = query.verticalAxisAggregations.length === 0;
  if (isVerticalAggregationsEmpty)
    throw new InsightValidationError("The output query doesn't have any vertical aggregation");
}

function validateInsightDataset(datasetMeta: BeeInsightsDatasetMeta, dataset: BeeInsightsDataset) {
  _areDatabasePropsValid(datasetMeta, dataset);
}

function _areDatabasePropsValid(datasetMeta: BeeInsightsDatasetMeta, dataset: BeeInsightsDataset) {
  const allowedPropTypes = [
    'id',
    'integer',
    'float',
    'percentage',
    'string',
    'enum',
    'bool',
    'datetime',
    'geolocation',
  ];

  for (const [, property] of Object.entries(datasetMeta.properties)) {
    if (!allowedPropTypes.includes(property.type)) {
      throw new InsightValidationError(`Invalid property type "${property.type}"`);
    }
  }
}
