export const PostQueryUtil = {
  getAllAggregationsSum,
  getAggregationSum,
  getAggregationValues,
  getAggregationValue,
};

function getAllAggregationsSum(
  entries: Array<BeeInsightsDatasetEntry>,
  aggregations: Array<BeeInsightsVerticalAxisAggregation>
) {
  return aggregations.reduce((acc, aggregation) => acc + getAggregationSum(entries, aggregation), 0);
}

function getAggregationSum(
  entries: Array<BeeInsightsDatasetEntry>,
  aggregation: BeeInsightsVerticalAxisAggregation | undefined
) {
  return getAggregationValues(entries, aggregation).reduce((acc, value) => acc + value, 0);
}

function getAggregationValues(
  entries: Array<BeeInsightsDatasetEntry>,
  aggregation: BeeInsightsVerticalAxisAggregation | undefined
) {
  return aggregation
    ? entries
        .map((entry) => getAggregationValue(entry, aggregation) as number)
        .filter((v) => v !== null && v !== undefined)
    : [];
}

function getAggregationValue(
  entry: BeeInsightsDatasetEntry,
  aggregation: BeeInsightsVerticalAxisAggregation | undefined
) {
  return aggregation ? entry?.[aggregation.aggregatedPropertyKey] : null;
}
