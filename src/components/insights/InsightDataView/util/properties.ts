export const PropertiesUtil = {
  getIdPropFromLabelProp,
  isGeolocationProp,
  isLabelProp,
  isStringProp,
  isCategoricalProp,
  isSequentialProp,
  isTemporalProp,
  isPercentageProp,
  isNumericProp,
  isBoolProp,
  isIdProp,
};

function getIdPropFromLabelProp(
  propertyKey: BeeInsightsDatasetPropertyKey,
  datasetMeta: BeeInsightsDatasetMeta
): BeeInsightsDatasetPropertyKey | null {
  if (isLabelProp(propertyKey, datasetMeta)) {
    return Object.entries(datasetMeta.labelPropertyKeys).filter(([, key]) => key === propertyKey)[0][0];
  }
  return null;
}

function isGeolocationProp(propertyType: BeeInsightsDatasetPropertyType) {
  return propertyType === 'geolocation';
}

function isLabelProp(propertyKey: BeeInsightsDatasetPropertyKey, datasetMeta: BeeInsightsDatasetMeta) {
  return Object.values(datasetMeta.labelPropertyKeys).includes(propertyKey);
}

function isStringProp(propertyType: BeeInsightsDatasetPropertyType) {
  return ['string'].includes(propertyType);
}

function isCategoricalProp(propertyType: BeeInsightsDatasetPropertyType) {
  return ['string', 'enum', 'bool'].includes(propertyType);
}

function isSequentialProp(propertyType: BeeInsightsDatasetPropertyType) {
  return ['integer', 'float', 'percentage', 'datetime'].includes(propertyType);
}

function isTemporalProp(propertyType: BeeInsightsDatasetPropertyType) {
  return propertyType === 'datetime';
}

function isPercentageProp(propertyType: BeeInsightsDatasetPropertyType) {
  return ['percentage'].includes(propertyType);
}

function isNumericProp(propertyType: BeeInsightsDatasetPropertyType) {
  return ['integer', 'float', 'percentage'].includes(propertyType);
}

function isBoolProp(propertyType: BeeInsightsDatasetPropertyType) {
  return propertyType === 'bool';
}

function isIdProp(propertyType: BeeInsightsDatasetPropertyType) {
  return propertyType === 'id';
}
