import moment, { Moment } from 'moment';

const UNIT_MAX_THRESHOLD = {
  day: 60,
  week: 24,
  month: 24,
  quarter: 8,
  year: Infinity,
};

export const Timeframe = {
  getAvailableGranularityOptions,
  toRange,
};

function getAvailableGranularityOptions(
  options: BeeInsightTimeframe,
  seasonStart: Date | null
): Array<BeeInsightsDateGranularity> {
  const { start, end } = toRange(options, seasonStart, true);

  const granularityOptions: Array<BeeInsightsDateGranularity> = [];

  (['day', 'week', 'month', 'quarter'] as Array<moment.unitOfTime.Diff>).forEach((unit) => {
    if (end.diff(start, unit) <= UNIT_MAX_THRESHOLD[unit]) {
      granularityOptions.push(unit as BeeInsightsDateGranularity);
    }
  });

  // Edge case.
  // If the period is less than 1 year, hide year granularity.
  if (end.diff(start, 'year') >= 1) {
    granularityOptions.push('year');
  }

  return granularityOptions;
}

function toRange({ mode, fixed, toNow, relative }: BeeInsightTimeframe, seasonStart: Date | null, useMaxRange = false) {
  let start: Moment;
  let end: Moment;

  if (mode === 'relative') {
    const { amount, unit } = relative;

    if (unit === 'season') {
      start = moment(seasonStart)
        .subtract(amount - 1, 'year')
        .startOf('day');
      end = moment().endOf('day');
    } else {
      start = moment().subtract(amount, unit).startOf('day');
      end = moment().endOf('day');
    }
  } else if (mode === 'to-now') {
    start = moment(toNow.start).startOf('day');

    const now = moment();
    if (useMaxRange) {
      end = moment.max(now, moment(toNow.maxEnd)).endOf('day');
    } else {
      end = moment.min(now, moment(toNow.maxEnd)).endOf('day');
    }
  } else {
    start = moment(fixed.start).startOf('day');
    end = moment(fixed.end).endOf('day');
  }

  return { start, end };
}
