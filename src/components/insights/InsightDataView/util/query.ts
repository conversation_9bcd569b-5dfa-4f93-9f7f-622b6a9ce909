import moment from 'moment-timezone';

import { CHART_GRANULARITY_FORMATS } from '@components/insights/InsightDataView/constants';
import { Caching } from '@components/insights/InsightDataView/util/caching';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties';
import { ValidationUtil } from '@components/insights/InsightDataView/util/validation';
import { Performance } from '@helpers/Performance/Performance.ts';

export interface QueryOptions {
  datasetMeta: BeeInsightsDatasetMeta;
  dataset: BeeInsightsDataset;
  insight: BeeInsightDataVisualization;
  seasonStart: Date | null;
}

export const DatabaseQueryUtil = {
  query,
};

function query(options: QueryOptions) {
  return Caching.performCachedTransform(Caching.getInsightQueryKey(options.insight, options.seasonStart), () =>
    Performance.measure(() => {
      _validateIn(options);

      const filteredOptions = _filter(options);
      const output = _query(filteredOptions);
      Object.freeze(output.dataset.entries);
      Object.freeze(output.dataset.aggregatedEntries);

      _validateOut(output);

      return { output };
    })
  );
}

function _validateIn(options: QueryOptions) {
  ValidationUtil.validateInsightForQuery(options.insight);
  ValidationUtil.validateInsightDataset(options.datasetMeta, options.dataset);
}

function _validateOut(query: BeeInsightsDatasetQuery) {
  ValidationUtil.validateInsightQuery(query);
}

function _filter(options: QueryOptions): QueryOptions {
  const filteredEntries = options.dataset.entries.filter((entry) => _filterEntry(entry, options));
  return {
    ...options,
    dataset: { entries: filteredEntries },
  };
}

function _filterEntry(entry, options: QueryOptions) {
  const filters = options.insight.filters;
  return filters.every((filter) => {
    const property = options.datasetMeta.properties[filter.propertyKey];
    if (PropertiesUtil.isCategoricalProp(property.type)) {
      const valuePropertyKey = PropertiesUtil.getIdPropFromLabelProp(property.key, options.datasetMeta) ?? property.key;
      const value = entry[valuePropertyKey];
      return !filter.value.oneOf?.length || filter.value.oneOf.includes(value);
    }
    return true;
  });
}

function _query(options: QueryOptions): BeeInsightsDatasetQuery {
  const { datasetMeta, insight } = options;

  const isXIdKey = datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey] === insight.horizontalAxisPropertyKey;
  const isXDiffFromY = insight.horizontalAxisPropertyKey !== insight.verticalAxisPropertyKey;
  const isXDiffFromYC = insight.horizontalAxisPropertyKey !== insight.verticalAxisComparisonPropertyKey;
  const isYNumeric = PropertiesUtil.isNumericProp(datasetMeta.properties[insight.verticalAxisPropertyKey]?.type);
  const isYCNullOrNumeric =
    !insight.verticalAxisComparisonPropertyKey ||
    PropertiesUtil.isNumericProp(datasetMeta.properties[insight.verticalAxisComparisonPropertyKey]?.type);
  const isNonAggregatedQuery = isXIdKey && isXDiffFromY && isXDiffFromYC && isYNumeric && isYCNullOrNumeric;

  return isNonAggregatedQuery ? _performNonAggregatedQuery(options) : _performAggregatedQuery(options);
}

function _performNonAggregatedQuery({ dataset, insight, datasetMeta }: QueryOptions): BeeInsightsDatasetQuery {
  const entries = dataset.entries.filter((entry) => {
    const val = entry[insight.verticalAxisPropertyKey];
    return val !== null && !isNaN(val as number);
  });
  return {
    dataset: {
      entries,
      aggregatedEntries: [],
    },
    properties: datasetMeta.properties,
    horizontalAxisPropertyKey: insight.horizontalAxisPropertyKey,
    verticalAxisAggregations: [
      {
        aggregatedPropertyKey: insight.verticalAxisPropertyKey,
        originPropertyKey: insight.verticalAxisPropertyKey,
        aggregatedEntriesPropertyKey: null,
        aggregationType: null,
        aggregatedValue: null,
        isSimpleAggregation: true,
      },
    ],
    verticalAxisComparisonAggregations: insight.verticalAxisComparisonPropertyKey
      ? [
          {
            aggregatedPropertyKey: insight.verticalAxisComparisonPropertyKey,
            originPropertyKey: insight.verticalAxisComparisonPropertyKey,
            aggregatedEntriesPropertyKey: null,
            aggregationType: null,
            aggregatedValue: null,
            isSimpleAggregation: true,
          },
        ]
      : [],
  };
}

/**
 * Performs a query that requires aggregation. It might
 * introduce one or more props in the output dataset.
 * */
function _performAggregatedQuery({ insight, dataset, datasetMeta, ...extra }: QueryOptions): BeeInsightsDatasetQuery {
  // If the user chooses the same prop for X and Y,
  // we auto set the y prop to the main id prop. It
  // gives almost the same result and keeps the chart
  // simpler.
  let verticalAxisPropertyKey = insight.verticalAxisPropertyKey;
  if (insight.horizontalAxisPropertyKey === insight.verticalAxisPropertyKey) {
    verticalAxisPropertyKey = datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey];
  }
  let verticalAxisComparisonPropertyKey = insight.verticalAxisComparisonPropertyKey;
  if (insight.horizontalAxisPropertyKey === insight.verticalAxisComparisonPropertyKey) {
    verticalAxisComparisonPropertyKey = datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey];
  }

  let aggregatedEntriesMap: Map<string, Map<string, BeeInsightsDatasetEntry>>;
  Performance.measureAndLog('_getEntriesAggregatedByHorizontalKey', () => {
    aggregatedEntriesMap = _getEntriesAggregatedByHorizontalKey({ dataset, insight, datasetMeta, ...extra });
  });

  const entriesList = [] as Array<BeeInsightsDatasetEntry>;
  const aggregatedEntriesList = [] as Array<Record<string, Array<BeeInsightsDatasetEntry>>>;

  const aggregationProperties: Record<BeeInsightsDatasetPropertyKey, BeeInsightsDatasetPropertyMeta> = {};

  const verticalAggregations: Record<BeeInsightsDatasetPropertyKey, BeeInsightsVerticalAxisAggregation> = {};
  const verticalComparisonAggregations: Record<BeeInsightsDatasetPropertyKey, BeeInsightsVerticalAxisAggregation> = {};

  Performance.measureAndLog(`Aggregate entries ${insight.verticalAxisAggregation}`, () => {
    Array.from(aggregatedEntriesMap.entries()).forEach(([, subEntries]) => {
      const entry: BeeInsightsDatasetEntry = {
        [insight.horizontalAxisPropertyKey]: subEntries.values().next().value[insight.horizontalAxisPropertyKey],
      };
      const entryAggregationEntries: Record<string, Array<BeeInsightsDatasetEntry>> = {};

      const aggregationLevels = [
        {
          property: verticalAxisPropertyKey,
          aggregationType: insight.verticalAxisAggregation,
          aggregations: verticalAggregations,
        },
        {
          property: verticalAxisComparisonPropertyKey,
          aggregationType: insight.verticalAxisComparisonAggregation,
          aggregations: verticalComparisonAggregations,
        },
      ];

      aggregationLevels.forEach(({ property, aggregationType, aggregations }) => {
        if (!property || !aggregationType) return;

        const aggregationPropertyKeyBase = `${property}__${aggregationType}`;
        const aggregationEntriesPropertyKeyBase = `${property}__${aggregationType}__entries`;

        if (PropertiesUtil.isNumericProp(datasetMeta.properties[property].type)) {
          entry[aggregationPropertyKeyBase] = _getNumericAggregation(
            Array.from(subEntries.values()),
            property,
            aggregationType
          );
          entryAggregationEntries[aggregationEntriesPropertyKeyBase] = Array.from(subEntries.values());

          aggregationProperties[aggregationPropertyKeyBase] = {
            ...datasetMeta[property],
            key: aggregationPropertyKeyBase,
            type: 'float',
          };

          aggregations[aggregationPropertyKeyBase] = {
            aggregationType,
            aggregatedPropertyKey: aggregationPropertyKeyBase,
            aggregatedEntriesPropertyKey: aggregationEntriesPropertyKeyBase,
            originPropertyKey: property,
            aggregatedValue: null,
            isSimpleAggregation: true,
          };
        } else {
          let counts: Record<any, number>;
          let values: Record<any, any>;
          let aggregatedEntries: Record<string, Array<BeeInsightsDatasetEntry>> = {};

          const countsAndValues = _getCategoricalAggregation(
            Array.from(subEntries.values()),
            property,
            aggregationType
          );

          /**
           * If the vertical prop is the id or if it's a PIE chart, we generate
           * a simple aggregation, i.e. we won't have multiple bar/line colors
           * in the same chart.
           * */
          const isYTheMainId = property === datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey];
          const isPieChart = insight.type === 'chart-pie';
          const shouldGenerateSimplifiedAggregation = isYTheMainId || isPieChart;

          if (shouldGenerateSimplifiedAggregation) {
            const nonNullValueCounts = Object.keys(countsAndValues.counts)
              .filter((key) => countsAndValues.values[key] !== null)
              .map((key) => countsAndValues.counts[key]);

            /**
             * Don't include the current entry and aggregation if there
             * is no non-null value counted.
             * */
            if (nonNullValueCounts.length === 0) {
              return;
            }

            const summedCounts = nonNullValueCounts.reduce((acc, c) => acc + c, 0);
            counts = { default: aggregationType === 'percent' ? 1 : summedCounts };
            values = { default: null };
            aggregatedEntries = { default: Array.from(subEntries.values()) };
          } else {
            counts = countsAndValues.counts;
            values = countsAndValues.values;
            aggregatedEntries = countsAndValues.aggregatedEntries;
          }

          Object.entries(counts).forEach(([value, count]) => {
            const aggregationPropertyKey = `${aggregationPropertyKeyBase}__${value}`;
            const aggregationEntriesPropertyKey = `${aggregationPropertyKeyBase}__${value}__entries`;
            entry[aggregationPropertyKey] = count;
            entryAggregationEntries[aggregationEntriesPropertyKey] = aggregatedEntries[value] ?? [];

            aggregationProperties[aggregationPropertyKey] = {
              ...datasetMeta[property],
              key: aggregationPropertyKey,
              type: 'float',
            };

            aggregations[aggregationPropertyKey] = {
              aggregationType,
              originPropertyKey: property,
              aggregatedPropertyKey: aggregationPropertyKey,
              aggregatedEntriesPropertyKey: aggregationEntriesPropertyKey,
              aggregatedValue: values[value],
              isSimpleAggregation: shouldGenerateSimplifiedAggregation,
            };
          });
        }
      });

      /**
       * If the final entry has only one property, it means only the horizontal
       * property was filled, and no non-null vertical values were found.
       * */
      const hasAtLeastOneAggregation = Object.keys(entry).length > 1;
      if (hasAtLeastOneAggregation) {
        entriesList.push(entry);
        aggregatedEntriesList.push(entryAggregationEntries);
      }
    });
  });

  return {
    dataset: { entries: entriesList, aggregatedEntries: aggregatedEntriesList },
    horizontalAxisPropertyKey: insight.horizontalAxisPropertyKey,
    verticalAxisAggregations: Object.values(verticalAggregations),
    verticalAxisComparisonAggregations: Object.values(verticalComparisonAggregations),
    properties: {
      ...datasetMeta.properties,
      ...aggregationProperties,
    },
  };
}

/**
 * Returns a map where:
 * - Keys are values of X axis.
 * - Values are a list of dataset entries for that key.
 * */
function _getEntriesAggregatedByHorizontalKey({ dataset, insight, datasetMeta }: QueryOptions) {
  const aggregatedEntriesMap = new Map<string, Map<string, BeeInsightsDatasetEntry>>();

  const parseDate = (value: any) => Caching.performCachedTransform(value, () => ({ output: moment(value) })).output;
  const normalizedDateFormat = CHART_GRANULARITY_FORMATS[insight.horizontalAxisDateGranularity];
  const parseDateAndFormat = (value: any) =>
    Caching.performCachedTransform(`${value}-${normalizedDateFormat}`, () => ({
      output: parseDate(value).format(normalizedDateFormat),
    })).output;

  dataset.entries.forEach((entry) => {
    const propertyKey =
      datasetMeta.labelPropertyKeys[datasetMeta.idPropertyKey] === insight.horizontalAxisPropertyKey
        ? datasetMeta.idPropertyKey
        : insight.horizontalAxisPropertyKey;

    const propertyMeta = datasetMeta.properties[propertyKey];
    let entryAggregationKey = entry[propertyKey] as string;
    if (PropertiesUtil.isTemporalProp(propertyMeta.type)) {
      entryAggregationKey = String(parseDateAndFormat(entryAggregationKey));
    }
    if (!aggregatedEntriesMap.has(entryAggregationKey)) {
      aggregatedEntriesMap.set(entryAggregationKey, new Map<string, BeeInsightsDatasetEntry>());
    }
    aggregatedEntriesMap.get(entryAggregationKey)?.set(entry[datasetMeta.idPropertyKey] as string, entry);
  });

  return aggregatedEntriesMap;
}

function _getCategoricalAggregation(
  entries: Array<BeeInsightsDatasetEntry>,
  property: BeeInsightsDatasetPropertyKey,
  aggregationType: BeeInsightsAggregationType
) {
  let counts: Record<string, number> = {};
  let values: Record<string, BeeInsightsDatasetValue> = {};
  const aggregatedEntries: Record<string, Array<BeeInsightsDatasetEntry>> = {};

  const seenValues: Record<string, boolean> = {};

  entries.forEach((e) => {
    const value = e[property] as string;
    seenValues[value] = true;
    counts[value] = (counts[value] ?? 0) + 1;
    values[value] = value;
    aggregatedEntries[value] = [...(aggregatedEntries[value] ?? []), e];
  });

  if (aggregationType === 'percent') {
    const sum = Object.values(counts).reduce((acc, v) => acc + v, 0);
    Object.entries(counts).forEach(([key, val]) => {
      counts[key] = val / sum;
    });
  } else if (aggregationType === 'avg') {
    const sum = Object.values(counts).reduce((acc, v) => acc + v, 0);
    const length = Object.values(counts).length;
    counts = { default: sum / length };
    values = { default: null };
  }

  return { counts, values, aggregatedEntries };
}

function _getNumericAggregation(
  entries: Array<BeeInsightsDatasetEntry>,
  property: BeeInsightsDatasetPropertyKey,
  aggregationType: BeeInsightsAggregationType
) {
  const values = entries.map((e) => e[property]).filter((v) => v !== null && v !== undefined) as Array<number>;

  if (values.length === 0) return null;

  switch (aggregationType) {
    case 'sum':
      return values.reduce((acc, v) => acc + v, 0.0);
    case 'avg':
      return values.reduce((acc, v) => acc + v, 0.0) / values.length;
    case 'min':
      return Math.min(...values);
    case 'max':
      return Math.max(...values);
    default:
      return 0.0;
  }
}
