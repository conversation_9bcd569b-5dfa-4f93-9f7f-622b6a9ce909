const CACHED_TRANSFORMS: Record<string, any> = {};

export const Caching = {
  getInsightQuery<PERSON><PERSON>,
  getDatasetRe<PERSON><PERSON>ey,
  getFilters<PERSON>ey,
  performCachedTransform,
};

function getInsightQueryKey(insight: BeeInsightDataVisualization, seasonStart: Date | null): string {
  return [
    insight.type,
    insight.typeForComparison,
    insight.horizontalAxisPropertyKey,
    insight.verticalAxisPropertyKey,
    insight.verticalAxisComparisonPropertyKey,
    insight.verticalAxisAggregation,
    insight.verticalAxisComparisonAggregation,
    insight.horizontalAxisDateGranularity,
    getDatasetRequest<PERSON>ey(insight, [], seasonStart),
    getFilters<PERSON>ey(insight),
  ].join('-');
}

function getDatasetRequestKey(
  insight: BeeInsightDataVisualization,
  propertyList: Array<string>,
  seasonStart: Date | null
): string {
  const { timeframe } = insight;
  const seasonStartStr = seasonStart?.toISOString();
  return [
    insight.dataset<PERSON><PERSON>,
    insight.horizontalAxisDateGranularity,
    timeframe.mode,
    timeframe.fixed.start,
    timeframe.fixed.end,
    timeframe.relative.unit,
    timeframe.relative.amount,
    timeframe.toNow.start,
    timeframe.toNow.maxEnd,
    seasonStartStr,
    ...propertyList,
  ].join('-');
}
function getFiltersKey(insight: BeeInsightDataVisualization): string {
  return insight.filters.map(({ propertyKey, value }) => `${propertyKey}:${JSON.stringify(value)}`).join(';');
}

function performCachedTransform<OUT>(key: string, transformer: () => OUT): OUT & { cached: boolean } {
  let cached = true;
  if (!(key in CACHED_TRANSFORMS)) {
    CACHED_TRANSFORMS[key] = transformer();
    cached = false;
  }
  return { ...CACHED_TRANSFORMS[key], cached };
}
