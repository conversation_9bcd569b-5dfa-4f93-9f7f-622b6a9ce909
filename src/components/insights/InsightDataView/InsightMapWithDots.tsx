import React, { useCallback, useLayoutEffect, useMemo } from 'react';
import { useTheme } from 'styled-components';

import { Box } from '@components/common/Box';
import { HiveDot } from '@components/common/Icon/dynamic/HiveDot.ts';
import { Map, MapControls, Marker } from '@components/common/Map';
import { MapElementsOptimizer } from '@components/common/Map/components/MapElementsOptimizer.tsx';
import { Coordinate, MapElementDescriptor, MapInstance } from '@components/common/Map/types.ts';
import { useAxisReadyDataset, useDatasetMeta, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import { LabelsUtil } from '@components/insights/InsightDataView/util/labels.ts';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties.ts';

import { InsightChartProps } from './types';

export const InsightMapWithDots: React.FC<InsightChartProps> = ({ insight, staticQuery, width, height, onLoad }) => {
  const theme = useTheme();
  const datasetMeta = useDatasetMeta(insight);

  const [map, setMap] = React.useState<MapInstance | null>(null);

  const { query } = useDatasetQuery(insight, { staticQuery });
  const { aggregatedEntries } = useAxisReadyDataset(insight, { skipPaginationSlicing: true, staticQuery });

  const getColor = useColorGetter();

  const elements = useMemo(() => {
    const geolocationProp = Object.values(datasetMeta?.properties ?? {}).find((prop) =>
      PropertiesUtil.isGeolocationProp(prop.key as BeeInsightsDatasetPropertyType)
    );
    if (!geolocationProp) return [];

    const elements: Array<MapElementDescriptor> = [];
    let serial = 0;

    aggregatedEntries?.forEach((entriesMap) => {
      query?.verticalAxisAggregations.forEach((aggregation, index) => {
        if (!LabelsUtil.isAggregationVisible(insight.visibleLabels, aggregation)) return;

        const subEntries = entriesMap?.[aggregation.aggregatedEntriesPropertyKey as string];
        subEntries?.forEach((data) => {
          if (!data[geolocationProp.key]) return;
          const color = getColor(index);
          elements.push({
            id: serial++,
            data: { color },
            position: data[geolocationProp.key] as Coordinate,
            radius: 0,
          });
        });
      });
    });

    return elements;
  }, [aggregatedEntries, datasetMeta?.properties, getColor, insight.visibleLabels, query?.verticalAxisAggregations]);

  const elementRenderer = useCallback(
    ({ data, position }: MapElementDescriptor) => (
      <Marker icon={HiveDot.getImageURI({ theme, size: 18, borderSize: 1, color: data.color })} position={position} />
    ),
    [theme]
  );

  const recenterMap = useCallback(() => {
    map?.fitCoordinates(elements.map((e) => e.position));
  }, [elements, map]);

  useLayoutEffect(() => {
    setTimeout(() => {
      recenterMap();
    }, theme.animations.durationLong);
  }, [elements, map, recenterMap, theme.animations.durationLong]);

  if (!query || !width) return null;

  return (
    <Box borderRadius_075 relative overflow={'hidden'} data-pdf-map={true} style={{ width, height }}>
      <Map onInstance={setMap} onTilesLoaded={onLoad}>
        <MapControls controls={['recenter']} onRecenterMapClick={recenterMap} />
        <MapElementsOptimizer elements={elements} enableSmartBounds elementRenderer={elementRenderer} />
      </Map>
    </Box>
  );
};
