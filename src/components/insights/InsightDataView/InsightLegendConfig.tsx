import React, { use<PERSON>allback, useContext, useState } from 'react';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { Check } from '@components/common/Icon';
import { Settings } from '@components/common/Icon/presets/Settings';
import { Tooltip } from '@components/common/Tooltip';
import { InsightBuilderContext } from '@components/insights/InsightBuilder/context';
import { useAggregationFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import { StyledChartLegendDot, StyledChartLegendScroll } from '@components/insights/InsightDataView/styles';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';
import { LabelsUtil } from '@components/insights/InsightDataView/util/labels';
import { Analytics } from '@helpers/Analytics';
import { AnalyticsEventType } from '@helpers/Analytics/types';
import { useDispatch } from '@helpers/Thunk/hooks';
import { useTranslation } from '@hooks/useTranslation';
import { makeInsightSaveThunk } from '@redux/Insights/actions';
import { useCurrentDashboardId } from '@redux/Insights/hooks';

export interface InsightLegendConfigProps extends InsightDataViewProps {
  isComparison?: boolean;
}

export const InsightLegendConfig: React.FC<InsightLegendConfigProps> = ({ insight, isComparison, staticQuery }) => {
  const t = useTranslation();
  const dispatch = useDispatch();

  const context = useContext(InsightBuilderContext);
  const currentDashboardId = useCurrentDashboardId();
  const getColor = useColorGetter();
  const formatProperty = useAggregationFormatter(insight, { staticQuery });
  const { query } = useDatasetQuery(insight, { staticQuery });

  const [visibleLabels, setVisibleLabels] = useState(insight.visibleLabels);
  const [visibleComparisonLabels, setVisibleComparisonLabels] = useState(insight.visibleComparisonLabels);

  const updateVisibleLabels = useCallback(
    (
      visibleLabels: BeeInsightDataVisualization['visibleLabels'],
      visibleComparisonLabels: BeeInsightDataVisualization['visibleComparisonLabels']
    ) => {
      if (!currentDashboardId) return;

      const isUnderInsightBuilder = !!context;

      const partialInsight = {
        visibleLabels: visibleLabels ?? insight.visibleLabels,
        visibleComparisonLabels: visibleComparisonLabels ?? insight.visibleComparisonLabels,
      };

      if (isUnderInsightBuilder) {
        context.updateInsight({ ...partialInsight });
      } else {
        dispatch(makeInsightSaveThunk(currentDashboardId, { ...insight, ...partialInsight }));
      }

      Analytics.sendEvent({
        event: AnalyticsEventType.INSIGHTS_INSIGHT_LABEL_TOGGLE,
      });
    },
    [context, currentDashboardId, dispatch, insight]
  );

  const toggleAggVisibility = useCallback(
    (aggregation: BeeInsightsVerticalAxisAggregation) => {
      setVisibleLabels((curr) => {
        curr = { ...(curr ?? {}) };
        curr[aggregation.aggregatedPropertyKey] = !LabelsUtil.isAggregationVisible(curr, aggregation);
        updateVisibleLabels(curr, visibleComparisonLabels);
        return curr;
      });
    },
    [updateVisibleLabels, visibleComparisonLabels]
  );

  const toggleComparisonAggVisibility = useCallback(
    (aggregation: BeeInsightsVerticalAxisAggregation) => {
      setVisibleComparisonLabels((curr) => {
        curr = { ...(curr ?? {}) };
        curr[aggregation.aggregatedPropertyKey] = !LabelsUtil.isComparisonAggregationVisible(curr, aggregation);
        updateVisibleLabels(visibleLabels, curr);
        return curr;
      });
    },
    [updateVisibleLabels, visibleLabels]
  );

  const menuId = isComparison
    ? `insight-visible-values-${insight.id}-comparison`
    : `insight-visible-values-${insight.id}`;
  const aggregations = isComparison ? query?.verticalAxisComparisonAggregations : query?.verticalAxisAggregations;

  if (!aggregations) return null;

  return (
    <>
      <Button
        id={menuId}
        iconOnly
        shortcut
        suppressPadding
        aria-label={t('insights:builder_choose_visible_labels')}
        data-image-ignore
      >
        <Box padding_025>
          <Settings size={18} />
        </Box>
      </Button>

      <Tooltip
        target={menuId}
        light
        interactive
        placement={'top-end'}
        renderContent={(children) => (
          <Box column stretch borderRadius_050 shadow={'boxShadow03'} backgroundColor={'surfaceSecondary'}>
            {children}
          </Box>
        )}
      >
        <StyledChartLegendScroll column stretch padding_050>
          {aggregations.map((aggregation, index) => {
            const colorIndex = isComparison ? index + (query?.verticalAxisAggregations.length ?? 0) : index;
            const color = getColor(colorIndex);
            const selected = isComparison
              ? LabelsUtil.isComparisonAggregationVisible(visibleComparisonLabels, aggregation)
              : LabelsUtil.isAggregationVisible(visibleLabels, aggregation);

            const toggle = isComparison ? toggleComparisonAggVisibility : toggleAggVisibility;
            return (
              <Button key={index} suppressPadding onClick={() => toggle(aggregation)}>
                <Box fit alignItems={'center'} justifyContent={'flex-start'} paddingHorizontal_050 gap_100>
                  <StyledChartLegendDot style={{ backgroundColor: color }} />
                  <Box fit>{formatProperty(aggregation, { maxLength: 22 })}</Box>
                  {selected && <Check size={18} />}
                </Box>
              </Button>
            );
          })}
        </StyledChartLegendScroll>
      </Tooltip>
    </>
  );
};
