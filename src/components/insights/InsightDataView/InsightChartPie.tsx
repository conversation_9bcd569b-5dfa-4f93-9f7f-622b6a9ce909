import React, { CSSProperties, Fragment, useCallback, useLayoutEffect, useMemo } from 'react';
import { Cell, Pie, Sector, Text as RText } from 'recharts';
import { useTheme } from 'styled-components';

import { PIE_PROPS } from '@components/insights/InsightDataView/constants';
import { useValueFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import {
  StyledEmptyPieChartPlaceholder,
  StyledEmptyPieChartText,
  StyledPie<PERSON>hart,
  StyledPieChartWrapper,
} from '@components/insights/InsightDataView/styles';
import { InsightChartProps } from '@components/insights/InsightDataView/types';
import { PostQueryUtil } from '@components/insights/InsightDataView/util/postQuery';
import { Color } from '@helpers/Color';
import { Geometry } from '@helpers/Geometry';
import { useTranslation } from '@hooks/useTranslation';

/**
 * Defines the shape of the pie chart,
 * i.e. how thick is the circle.
 * */
const PIE_RADIUS = [
  { outerRadius: 0.75, innerRadius: 0.3 },
  { outerRadius: 0.0, innerRadius: 0.0 },
];
const PIE_RADIUS_FOR_COMPARISON = [
  { outerRadius: 0.75, innerRadius: 0.55 },
  { outerRadius: 0.4, innerRadius: 0.2 },
];

export const InsightChartPie: React.FC<InsightChartProps> = ({
  insight,
  staticQuery,
  width,
  height,
  onLoad,
  children,
}) => {
  const t = useTranslation();
  const theme = useTheme();
  const { query } = useDatasetQuery(insight, { staticQuery });
  const getColor = useColorGetter();
  const { entries, pagination } = useAxisReadyDataset(insight, { skipPaginationSlicing: true, staticQuery });
  const formatValue = useValueFormatter(insight, { staticQuery });

  const areAllValuesZero = useMemo(() => {
    return PostQueryUtil.getAggregationValues(entries, query?.verticalAxisAggregations[0]).every((val) => val === 0);
  }, [entries, query?.verticalAxisAggregations]);

  const areAllComparisonValuesZero = useMemo(() => {
    return PostQueryUtil.getAggregationValues(entries, query?.verticalAxisComparisonAggregations[0]).every(
      (val) => val === 0
    );
  }, [entries, query?.verticalAxisComparisonAggregations]);

  const getVisibleSlice = useCallback(
    (aggregation?: BeeInsightsVerticalAxisAggregation) => {
      const totalEntryCount = entries.length ?? 0;
      const firstEntryIndex = pagination.pageIndex * pagination.pageSize;
      const lastEntryIndex = Math.min((pagination.pageIndex + 1) * pagination.pageSize, totalEntryCount) - 1;

      const values = PostQueryUtil.getAggregationValues(entries, aggregation);
      const sum = PostQueryUtil.getAggregationSum(entries, aggregation);

      const maxAngle = Math.PI * 2.0;
      const startAngle = (values.slice(0, firstEntryIndex).reduce((acc, value) => acc + value, 0) * maxAngle) / sum;
      const endAngle = (values.slice(0, lastEntryIndex + 1).reduce((acc, value) => acc + value, 0) * maxAngle) / sum;

      return { firstEntryIndex, lastEntryIndex, startAngle, endAngle };
    },
    [entries, pagination.pageIndex, pagination.pageSize]
  );

  const visibleSlice = useMemo(() => {
    const slice = getVisibleSlice(query?.verticalAxisAggregations[0]);
    const sliceComparison = query?.verticalAxisComparisonAggregations.length
      ? getVisibleSlice(query?.verticalAxisComparisonAggregations[0])
      : null;
    if (sliceComparison) {
      return {
        ...slice,
        firstEntryIndex: Math.min(slice.firstEntryIndex, sliceComparison.firstEntryIndex),
        lastEntryIndex: Math.max(slice.lastEntryIndex, sliceComparison.lastEntryIndex),
        startAngle: Math.min(slice.startAngle, sliceComparison.startAngle),
        endAngle: Math.max(slice.endAngle, sliceComparison.endAngle),
      };
    }
    return slice;
  }, [getVisibleSlice, query?.verticalAxisAggregations, query?.verticalAxisComparisonAggregations]);

  const isEntryHighlighted = useCallback(
    (index: number) => {
      return index >= visibleSlice.firstEntryIndex && index <= visibleSlice.lastEntryIndex;
    },
    [visibleSlice.firstEntryIndex, visibleSlice.lastEntryIndex]
  );

  const renderLabel = useCallback(
    (props, propertyKey) => {
      if (!isEntryHighlighted(props.index) || props.percent === 0) return null;

      const angleInRadians = -Geometry.degreesToRadians(props.midAngle);

      const outerRadius = props.outerRadius;
      const innerRadius = props.innerRadius;
      const centerX = props.cx;
      const centerY = props.cy;
      const offset = outerRadius + theme.primitives.px._025;
      const offsetX = Math.cos(angleInRadians) * offset;
      const offsetY = Math.sin(angleInRadians) * offset;
      const pos = { x: centerX + offsetX, y: centerY + offsetY };
      const textAnchor = pos.x < centerX ? 'end' : 'start';
      const verticalAnchor = pos.y < centerY ? 'end' : 'start';

      const percentOffset = innerRadius + (outerRadius - innerRadius) * 0.5;
      const percentOffsetX = Math.cos(angleInRadians) * percentOffset;
      const percentOffsetY = Math.sin(angleInRadians) * percentOffset;
      const percentPos = { x: centerX + percentOffsetX, y: centerY + percentOffsetY };

      const labelsScale = Math.max(0.01, Math.min(0.02, props.percent * 0.3));
      const labelBaseSize = 512;
      const labelPercentageAngle =
        -angleInRadians > Math.PI * 0.5 && -angleInRadians < Math.PI * 1.5 ? angleInRadians + Math.PI : angleInRadians;

      const labelPercentageColor =
        Color.getGreyScale(props.fill) < 180 ? theme.primitives.colors.white : theme.primitives.colors.grey08;

      return (
        <>
          <RText
            {...pos}
            textAnchor={textAnchor}
            verticalAnchor={verticalAnchor}
            fontSize={labelBaseSize * labelsScale}
            fontWeight={'700'}
            fill={props.fill}
            pointerEvents={'none'}
          >
            {formatValue(propertyKey, props.value)}
          </RText>

          <g
            style={{
              transform: `rotate(${labelPercentageAngle}rad)`,
              transformOrigin: `${percentPos.x}px ${percentPos.y}px`,
            }}
          >
            <RText
              {...percentPos}
              textAnchor={'middle'}
              verticalAnchor={'middle'}
              fontSize={labelBaseSize * labelsScale}
              fontWeight={'600'}
              fill={labelPercentageColor}
              pointerEvents={'none'}
            >
              {`${(props.percent * 100).toFixed(1)}%`}
            </RText>
          </g>
        </>
      );
    },
    [
      formatValue,
      isEntryHighlighted,
      theme.primitives.colors.grey08,
      theme.primitives.colors.white,
      theme.primitives.px._025,
    ]
  );

  const renderSlice = useCallback((aggregation: BeeInsightsVerticalAxisAggregation | undefined, props: any) => {
    if (!aggregation) return <></>;

    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, opacity } = props;

    return (
      <g id={'ABC'}>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          opacity={opacity}
        />
        {/* DISABLED FOR NOW */}
        {/*<InsightTooltip*/}
        {/*  insight={insight}*/}
        {/*  staticQuery={staticQuery}*/}
        {/*  index={props.index}*/}
        {/*  aggregationPropertyKey={props.tooltipPayload[0]?.dataKey}*/}
        {/*/>*/}
      </g>
    );
  }, []);

  const getPieSliceBoundingBox = useCallback(() => {
    const { startAngle, endAngle } = visibleSlice;

    if (startAngle === endAngle) {
      return null;
    }

    /**
     * List of all angles within the visible pie slice, including
     * inner straight angles.
     *
     * All angles are multiplied by -1 since the UI starts Y from the
     * top, which inverts all angles.
     * */
    const visibleSliceAngles = [startAngle, endAngle, 0, Math.PI * 0.5, Math.PI, Math.PI * 1.5]
      .filter((angle) => angle >= startAngle && angle <= endAngle)
      .map((a) => a * -1);

    /**
     * From each angle, a (x,y) point is generated,
     * considering a virtual circle with diameter of 1.
     * */
    const points: Array<{ x: number; y: number }> = [];
    visibleSliceAngles.forEach((angle) => {
      points.push({
        x: Math.cos(angle) * 0.5,
        y: Math.sin(angle) * 0.5,
      });
      points.push({
        x: Math.cos(angle) * 0.5 * 0.2 /** 0.2 is a visual adjustment */,
        y: Math.sin(angle) * 0.5 * 0.2,
      });
    });

    /**
     * The bounding box is formed by the edge points.
     * */
    const left = Math.min(...points.map((p) => p.x));
    const right = Math.max(...points.map((p) => p.x));
    const top = Math.min(...points.map((p) => p.y));
    const bottom = Math.max(...points.map((p) => p.y));
    return { left, right, top, bottom };
  }, [visibleSlice]);

  const chartTransform = useMemo<CSSProperties | undefined>(() => {
    if (!query) return undefined;

    const box = getPieSliceBoundingBox();
    if (!box) return undefined;

    const smallestDimension = Math.min(width, height);
    const largestRelativeDimension = Math.max(box.bottom - box.top, box.right - box.left);
    const scale = 1.0 / largestRelativeDimension;
    const centerOffset = {
      x: (0.0 - (box.right + box.left) / 2.0) * smallestDimension * scale,
      y: (0.0 - (box.bottom + box.top) / 2.0) * smallestDimension * scale,
    };

    return {
      transform: `translate(${centerOffset.x}px, ${centerOffset.y}px) scale(${scale})`,
    };
  }, [getPieSliceBoundingBox, height, query, width]);

  const hasComparison = query?.verticalAxisComparisonAggregations?.length ?? 0 > 0;
  const [piePrimaryRadius, pieSecondaryRadius] = hasComparison ? PIE_RADIUS_FOR_COMPARISON : PIE_RADIUS;
  const pieBaseSize = Math.min(width, height);

  useLayoutEffect(() => {
    onLoad?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!query) return null;

  return (
    <StyledPieChartWrapper relative>
      <StyledPieChart width={width} height={height} style={chartTransform}>
        {children}

        {areAllValuesZero && (
          <Fragment>
            <StyledEmptyPieChartPlaceholder
              backgroundColor={'surfaceComponentDisabled'}
              style={{
                width: pieBaseSize * piePrimaryRadius.outerRadius,
                height: pieBaseSize * piePrimaryRadius.outerRadius,
              }}
            />
            <StyledEmptyPieChartPlaceholder
              backgroundColor={'surfaceSecondary'}
              style={{
                width: pieBaseSize * piePrimaryRadius.innerRadius,
                height: pieBaseSize * piePrimaryRadius.innerRadius,
              }}
            />
          </Fragment>
        )}

        {areAllComparisonValuesZero && (
          <Fragment>
            <StyledEmptyPieChartPlaceholder
              backgroundColor={'surfaceComponentDisabled'}
              style={{
                width: pieBaseSize * pieSecondaryRadius.outerRadius,
                height: pieBaseSize * pieSecondaryRadius.outerRadius,
              }}
            />
            <StyledEmptyPieChartPlaceholder
              backgroundColor={'surfaceSecondary'}
              style={{
                width: pieBaseSize * pieSecondaryRadius.innerRadius,
                height: pieBaseSize * pieSecondaryRadius.innerRadius,
              }}
            />
          </Fragment>
        )}

        <Pie
          data={entries}
          nameKey={query.horizontalAxisPropertyKey}
          dataKey={query.verticalAxisAggregations[0].aggregatedPropertyKey}
          label={(props) => renderLabel(props, query.verticalAxisAggregations[0].aggregatedPropertyKey)}
          labelLine={false}
          activeShape={(props) => renderSlice(query?.verticalAxisAggregations[0], props)}
          inactiveShape={(props) => renderSlice(query?.verticalAxisAggregations[0], props)}
          outerRadius={`${piePrimaryRadius.outerRadius * 100}%`}
          innerRadius={`${piePrimaryRadius.innerRadius * 100}%`}
          activeIndex={0}
          {...PIE_PROPS}
        >
          {entries.map((_, index) => (
            <Cell key={index} fill={getColor(index)} opacity={isEntryHighlighted(index) ? 1.0 : 0.1} />
          ))}
        </Pie>

        {hasComparison && (
          <Pie
            data={entries}
            nameKey={query.horizontalAxisPropertyKey}
            dataKey={query.verticalAxisComparisonAggregations[0].aggregatedPropertyKey}
            label={(props) => renderLabel(props, query?.verticalAxisComparisonAggregations[0].aggregatedPropertyKey)}
            labelLine={false}
            activeShape={(props) => renderSlice(query?.verticalAxisComparisonAggregations[0], props)}
            inactiveShape={(props) => renderSlice(query?.verticalAxisComparisonAggregations[0], props)}
            outerRadius={`${pieSecondaryRadius.outerRadius * 100}%`}
            innerRadius={`${pieSecondaryRadius.innerRadius * 100}%`}
            activeIndex={0}
            {...PIE_PROPS}
          >
            {entries.map((_, index) => (
              <Cell key={index} fill={getColor(index)} opacity={isEntryHighlighted(index) ? 1.0 : 0.1} />
            ))}
          </Pie>
        )}
      </StyledPieChart>

      {areAllValuesZero && areAllComparisonValuesZero && (
        <StyledEmptyPieChartText typography={'CaptionSmall'}>
          {t('insights:insight_chart_empty')}
        </StyledEmptyPieChartText>
      )}
    </StyledPieChartWrapper>
  );
};
