import React, { ReactNode, useCallback, useMemo } from 'react';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import {
  useAggregationFormatter,
  usePropertyFormatter,
  useValueFormatter,
} from '@components/insights/InsightDataView/hooks/formatting';
import { useAxisReadyDataset, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import {
  StyledChartLegendDot,
  StyledChartLegendItem,
  StyledDivider,
  StyledTruncatedTextBox,
} from '@components/insights/InsightDataView/styles';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';
import { PostQueryUtil } from '@components/insights/InsightDataView/util/postQuery';
import { useTranslation } from '@hooks/useTranslation';

export const InsightChartLegendPie: React.FC<InsightDataViewProps> = ({ insight, staticQuery }) => {
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery });
  const t = useTranslation();
  const getColor = useColorGetter();
  const formatProperty = usePropertyFormatter(insight, { staticQuery });
  const formatAggregation = useAggregationFormatter(insight, { staticQuery });
  const formatValue = useValueFormatter(insight, { staticQuery });
  const { entries, pagination } = useAxisReadyDataset(insight, { skipPaginationSlicing: true, staticQuery });

  const sum = useMemo(
    () => PostQueryUtil.getAllAggregationsSum(entries, query?.verticalAxisAggregations ?? []),
    [entries, query?.verticalAxisAggregations]
  );

  const sumComparison = useMemo(
    () => PostQueryUtil.getAllAggregationsSum(entries, query?.verticalAxisComparisonAggregations ?? []),
    [entries, query?.verticalAxisComparisonAggregations]
  );

  const renderItem = useCallback(
    (options: {
      label: ReactNode;
      index: number;
      value: number | null;
      valueComparison: number | null;
      tooltip?: string;
    }) => {
      const color = getColor(options.index);
      const proportion = sum && options.value ? options.value / sum : 0;
      const proportionComparison =
        sumComparison && options.valueComparison ? options.valueComparison / sumComparison : 0;
      return (
        <StyledChartLegendItem key={options.index} alignItems={'center'} gap_050>
          <StyledChartLegendDot style={{ backgroundColor: color }} />
          <StyledTruncatedTextBox>
            <Text typography={'CaptionSmall'}>{options.label}</Text>
          </StyledTruncatedTextBox>
          <StyledDivider />
          <Text typography={'CaptionSmall'} weight={'700'}>
            {(proportion * 100).toFixed(1)}%
          </Text>

          {options.valueComparison !== null ? (
            <>
              <StyledDivider />
              <Text typography={'CaptionSmall'} weight={'700'}>
                {(proportionComparison * 100).toFixed(1)}%
              </Text>
            </>
          ) : null}
        </StyledChartLegendItem>
      );
    },
    [getColor, sum, sumComparison]
  );

  const totalEntryCount = entries.length ?? 0;
  const firstEntryIndex = pagination.pageIndex * pagination.pageSize;
  const lastEntryIndex = Math.min((pagination.pageIndex + 1) * pagination.pageSize, totalEntryCount) - 1;

  if (!query || isQueryEmpty || insight.type !== 'chart-pie') return null;

  return (
    <Box gap_100 column center>
      <Box center gap_050 marginTop_050>
        <Text typography={'CaptionSmall'} weight={'600'}>
          {formatAggregation(query.verticalAxisAggregations[0])}
        </Text>

        {query.verticalAxisComparisonAggregations[0] ? (
          <>
            <Text typography={'CaptionSmall'}>{t('common:and')}</Text>
            <Text typography={'CaptionSmall'} weight={'600'}>
              {formatAggregation(query.verticalAxisComparisonAggregations[0])}
            </Text>
          </>
        ) : null}

        <Text typography={'CaptionSmall'}>{t('common:by')}</Text>
        <Text typography={'CaptionSmall'} weight={'600'}>
          {formatProperty({ originPropertyKey: query.horizontalAxisPropertyKey })}
        </Text>
      </Box>

      <Box gap_050 wrap alignItems={'center'} justifyContent={'center'}>
        {entries.map((entry, index) => {
          if (index < firstEntryIndex || index > lastEntryIndex) return null;

          const value = PostQueryUtil.getAggregationValue(entry, query?.verticalAxisAggregations[0]) as number;
          const valueComparison = PostQueryUtil.getAggregationValue(
            entry,
            query?.verticalAxisComparisonAggregations[0]
          ) as number;

          return renderItem({
            label: formatValue(query.horizontalAxisPropertyKey, entry[query.horizontalAxisPropertyKey], {
              maxLength: 24,
            }),
            tooltip: formatValue(query.horizontalAxisPropertyKey, entry[query.horizontalAxisPropertyKey]),
            value,
            valueComparison,
            index,
          });
        })}
      </Box>
    </Box>
  );
};
