import React, { ReactNode, useCallback, useMemo } from 'react';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';
import { LEGEND_MAX_VISIBLE_ITEMS, METRIC_MAX_VISIBLE_ITEMS } from '@components/insights/InsightDataView/constants';
import { useAggregationFormatter } from '@components/insights/InsightDataView/hooks/formatting';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { useColorGetter } from '@components/insights/InsightDataView/hooks/styling';
import { InsightLegendConfig } from '@components/insights/InsightDataView/InsightLegendConfig';
import {
  StyledChartLegendDot,
  StyledChartLegendItem,
  StyledRestrictedBox,
  StyledSettingsContainer,
  StyledTruncatedTextBox,
} from '@components/insights/InsightDataView/styles';
import { InsightDataViewProps } from '@components/insights/InsightDataView/types';
import { LabelsUtil } from '@components/insights/InsightDataView/util/labels';
import { useTranslation } from '@hooks/useTranslation.ts';

export const InsightLegend: React.FC<InsightDataViewProps> = ({ insight, enableLegendConfig, staticQuery }) => {
  const { query, isQueryEmpty } = useDatasetQuery(insight, { staticQuery });
  const t = useTranslation();
  const getColor = useColorGetter();
  const formatProperty = useAggregationFormatter(insight, { staticQuery });

  const renderItem = useCallback(
    (options: { label: ReactNode; index: number; tooltip?: string }) => {
      const color = getColor(options.index);
      return (
        <StyledChartLegendItem key={options.index} alignItems={'center'} gap_050>
          <StyledChartLegendDot style={{ backgroundColor: color }} />
          <StyledTruncatedTextBox>
            <Text typography={'CaptionSmall'} weight={'600'} data-pdf-unselectable={true}>
              {options.label}
            </Text>
          </StyledTruncatedTextBox>
        </StyledChartLegendItem>
      );
    },
    [getColor]
  );

  const legendItems = useMemo(() => {
    const items =
      query?.verticalAxisAggregations
        .map((aggregation, index) => ({ aggregation, index }))
        .filter(({ aggregation }) => LabelsUtil.isAggregationVisible(insight.visibleLabels, aggregation)) ?? [];
    const visibleItems = items.slice(0, LEGEND_MAX_VISIBLE_ITEMS);
    const invisibleItemsCount = items.length - visibleItems.length;
    return { visibleItems, invisibleItemsCount };
  }, [insight.visibleLabels, query?.verticalAxisAggregations]);

  const comparisonLegendItems = useMemo(() => {
    const items =
      query?.verticalAxisComparisonAggregations
        .map((aggregation, index) => ({ aggregation, index: index + query?.verticalAxisAggregations.length ?? 0 }))
        .filter(({ aggregation }) =>
          LabelsUtil.isComparisonAggregationVisible(insight.visibleComparisonLabels, aggregation)
        ) ?? [];
    const visibleItems = items.slice(0, LEGEND_MAX_VISIBLE_ITEMS);
    const invisibleItemsCount = items.length - visibleItems.length;
    return { visibleItems, invisibleItemsCount };
  }, [
    insight.visibleComparisonLabels,
    query?.verticalAxisAggregations.length,
    query?.verticalAxisComparisonAggregations,
  ]);

  if (!query || isQueryEmpty) return null;

  const hasMultipleLegendItems =
    query.verticalAxisAggregations.length + query.verticalAxisComparisonAggregations.length > 1;
  const renderLegendItems = ['chart-line', 'chart-bar', 'map-with-dots'].includes(insight.type);
  const renderLegendConfig =
    enableLegendConfig && !['chart-pie', 'table'].includes(insight.type) && hasMultipleLegendItems;
  const renderMaxMetricItemsWarning =
    ['metric'].includes(insight.type) &&
    legendItems.visibleItems.length + comparisonLegendItems.visibleItems.length > METRIC_MAX_VISIBLE_ITEMS;

  if (!renderLegendItems && !renderLegendConfig && !renderMaxMetricItemsWarning) {
    return;
  }

  return (
    <StyledSettingsContainer $floating={['metric'].includes(insight.type)} column stretch gap_100 marginTop_050>
      <Box alignItems={'flex-end'} gap_050>
        <StyledRestrictedBox gap_050 fit wrap alignItems={'center'}>
          {renderLegendItems && (
            <>
              {legendItems.visibleItems.map(({ aggregation, index }) =>
                renderItem({ label: formatProperty(aggregation), index })
              )}
              {legendItems.invisibleItemsCount > 0 && (
                <Text typography={'CaptionSmall'} data-pdf-unselectable={true}>
                  +{legendItems.invisibleItemsCount}
                </Text>
              )}
            </>
          )}
          {renderLegendConfig && <InsightLegendConfig insight={insight} />}
        </StyledRestrictedBox>

        {!!query.verticalAxisComparisonAggregations.length && (
          <StyledRestrictedBox gap_050 fit wrap alignItems={'center'} justifyContent={'flex-end'}>
            {renderLegendItems && (
              <>
                {comparisonLegendItems.visibleItems.map(({ aggregation, index }) =>
                  renderItem({ label: formatProperty(aggregation), index })
                )}
                {comparisonLegendItems.invisibleItemsCount > 0 && (
                  <Text typography={'CaptionSmall'} data-pdf-unselectable={true}>
                    +{comparisonLegendItems.invisibleItemsCount}
                  </Text>
                )}
              </>
            )}
            {renderLegendConfig && <InsightLegendConfig insight={insight} isComparison />}
          </StyledRestrictedBox>
        )}
        {renderMaxMetricItemsWarning && (
          <Text typography={'CaptionSmall'} color={'contentSecondary'} data-pdf-unselectable={true}>
            {t('insights:insight_metric_max_items_warning', { max: METRIC_MAX_VISIBLE_ITEMS })}
          </Text>
        )}
      </Box>
    </StyledSettingsContainer>
  );
};
