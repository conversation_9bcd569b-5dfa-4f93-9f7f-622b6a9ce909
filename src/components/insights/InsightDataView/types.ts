import { BoxProps } from '@components/common/Box/types';

export interface InsightChartProps extends InsightDataViewProps {
  width: number;
  height: number;
}

export interface InsightDataViewProps extends BoxProps<'div'> {
  insight: BeeInsightDataVisualization;
  enableMenu?: boolean;
  enableLegendConfig?: boolean;
  enablePagination?: boolean;

  /**
   * If defined, the insight will use this
   * data instead of querying the API.
   * */
  staticQuery?: any;

  /**
   * Called after the insight chart is
   * completely loaded.
   * */
  onLoad?: () => void;
}
