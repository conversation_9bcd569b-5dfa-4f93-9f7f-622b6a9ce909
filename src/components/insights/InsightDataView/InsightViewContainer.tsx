import React, { ComponentType, useLayoutEffect, useMemo, useRef, useState } from 'react';

import { Text } from '@components/common/Text';
import { useDatasetQuery } from '@components/insights/InsightDataView/hooks/query';
import { InsightChartCartesian } from '@components/insights/InsightDataView/InsightChartCartesian';
import { InsightChartPie } from '@components/insights/InsightDataView/InsightChartPie';
import { InsightMapWithDots } from '@components/insights/InsightDataView/InsightMapWithDots.tsx';
import { InsightMetric } from '@components/insights/InsightDataView/InsightMetric.tsx';
import { InsightTable } from '@components/insights/InsightDataView/InsightTable';
import {
  StyledChartContainer,
  StyledChartContainerInner,
  StyledChartOverlays,
} from '@components/insights/InsightDataView/styles';
import { useTranslation } from '@hooks/useTranslation';

import { InsightChartProps, InsightDataViewProps } from './types';

const CHART_COMPONENT_MAP: Record<BeeInsightDataVisualizationType, ComponentType<InsightChartProps>> = {
  'chart-line': InsightChartCartesian,
  'chart-bar': InsightChartCartesian,
  'chart-pie': InsightChartPie,
  table: InsightTable,
  metric: InsightMetric,
  'map-with-dots': InsightMapWithDots,
};

export const InsightViewContainer: React.FC<InsightDataViewProps> = ({ insight, staticQuery, onLoad }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);

  const t = useTranslation();
  const { type } = insight;
  const { isQueryEmpty } = useDatasetQuery(insight, { staticQuery });
  const ChartComponent = useMemo(() => (type ? CHART_COMPONENT_MAP[type] : null), [type]);

  const [chartRect, setChartRect] = useState<DOMRect | null>(null);

  useLayoutEffect(() => {
    const chartElement = chartContainerRef.current;
    if (!chartElement) return;
    const handle = () => {
      setChartRect(chartElement.getBoundingClientRect());
    };
    const observer = new ResizeObserver(handle);
    observer.observe(chartElement);
    return () => observer.disconnect();
  }, []);

  const canShowChart = !!ChartComponent && !!chartRect;

  return (
    <StyledChartContainer ref={chartContainerRef} column relative>
      {canShowChart && (
        <StyledChartContainerInner>
          <ChartComponent
            insight={insight}
            staticQuery={staticQuery}
            width={chartRect.width}
            height={chartRect.height}
            onLoad={onLoad}
          />
        </StyledChartContainerInner>
      )}

      {isQueryEmpty && (
        <StyledChartOverlays center column>
          <Text typography={'Heading3'} weight={'600'}>
            {t('insights:insight_chart_empty')}
          </Text>
          <Text typography={'CaptionSmall'}>{t('insights:insight_chart_empty_hint_timeframe')}</Text>
        </StyledChartOverlays>
      )}
    </StyledChartContainer>
  );
};
