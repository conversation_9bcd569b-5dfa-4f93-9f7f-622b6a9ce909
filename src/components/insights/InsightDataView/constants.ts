import { Props as BarProps } from 'recharts/types/cartesian/Bar';
import { Props as LineProps } from 'recharts/types/cartesian/Line';
import { Props as XAxisProps } from 'recharts/types/cartesian/XAxis';
import { Props as YAxisProps } from 'recharts/types/cartesian/YAxis';
import { Props as PieProps } from 'recharts/types/polar/Pie';

export const CHART_UPDATE_DEBOUNCE = 80;
export const LEGEND_MAX_VISIBLE_ITEMS = 8;
export const METRIC_MAX_VISIBLE_ITEMS = 24;
export const METRIC_TEXT_MAX_SIZE = 512;
export const METRIC_TEXT_PADDING = 16;
export const INCLUDED_HIVES_MAX_VISIBLE_ITEMS = 6;

export const PAGE_SIZES = [5, 10, 20, 40, 80, 100];

export const CHART_GRANULARITY_FORMATS: Record<BeeInsightsDateGranularity, string> = {
  day: 'YYYY-MM-DD',
  week: 'YYYY-WW',
  month: 'YYYY-MM',
  quarter: 'YYYY-Q',
  year: 'YYYY',
  season: 'YYYY',
};
export const CHART_GRANULARITY_READABLE_FORMATS: Record<BeeInsightsDateGranularity, string> = {
  day: 'MMM DD',
  week: 'MMM DD',
  month: 'MMM',
  quarter: 'YYYY-[Q]Q',
  year: 'YYYY',
  season: 'YYYY',
};

export const X_AXIS_ID = 'x-axis';
export const X_SECONDARY_AXIS_ID = 'x-secondary-axis';
export const Y_AXIS_ID = 'y-axis';
export const Y_COMPARISON_AXIS_ID = 'y-comparison-axis';

export const AXIS_COLORS = [
  '#3F51B5', // Indigo
  '#FF7F0E', // Orange
  '#2CA02C', // Green
  '#D62728', // Red
  '#9467BD', // Purple
  '#8C564B', // Brown
  '#E377C2', // Pink
  '#7F7F7F', // Gray
  '#BCBD22', // Olive
  '#17BECF', // Cyan
  '#393B79', // Dark Blue
  '#F58518', // Deep Orange
  '#637939', // Dark Green
  '#8B1A1A', // Dark Red
  '#6B4E31', // Tan
  '#843C39', // Maroon
  '#5254A3', // Indigo
  '#FF9F1A', // Bright Orange
  '#117A65', // Teal
  '#AD1457', // Magenta
];

export const LINE_PROPS: Omit<LineProps, 'ref'> = {
  type: 'linear',
  strokeWidth: 3,
  animationDuration: 0,
};

export const BAR_PROPS: Partial<Omit<BarProps, 'ref'>> = {
  animationDuration: 0,
};

export const PIE_PROPS: Partial<Omit<PieProps, 'ref'>> = {
  animationDuration: 0,
};

export const AXIS_PROPS: XAxisProps & YAxisProps = {
  allowDecimals: false,
};
export const X_AXIS_PROPS: XAxisProps = {
  interval: 0,
};

export const Y_AXIS_PROPS: YAxisProps = {
  interval: 0,
  strokeWidth: 0,
};
