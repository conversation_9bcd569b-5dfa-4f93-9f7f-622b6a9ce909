import React, { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import produce from 'immer';
import { minBy } from 'lodash';

import { Box } from '@components/common/Box';
import { Anchor } from '@components/common/CTA';
import { Logo } from '@components/common/Icon/presets/Logo.tsx';
import { LoadingDownload } from '@components/common/LoadingDownload';
import { Modal } from '@components/common/Modal';
import { ModalProps } from '@components/common/Modal/types.ts';
import { Text } from '@components/common/Text';
import { useTransientPlacements } from '@components/insights/Dashboard/hooks.ts';
import { StyledModalContent } from '@components/insights/DashboardPDFModal/styles.ts';
import { InsightView } from '@components/insights/InsightView';
import { isDataVisualizationInsight, isTextInsight } from '@components/insights/types.ts';
import APP from '@config/constants.ts';
import { PDF } from '@helpers/PDF';
import { useTranslation } from '@hooks/useTranslation.ts';
import { useCurrentDashboard, useCurrentDashboardInsights } from '@redux/Insights/hooks.ts';

const PAGE_SCALE = 0.5;
const PAGE_WIDTH = 2480 * PAGE_SCALE;
const PAGE_HEIGHT = 3508 * PAGE_SCALE;
const PAGE_HORIZONTAL_MARGIN = 150 * PAGE_SCALE;
const PAGE_VERTICAL_MARGIN = 200 * PAGE_SCALE;
const PAGE_HORIZONTAL_UNITS = 12;
const PAGE_VERTICAL_UNITS = 19;
const HORIZONTAL_UNIT = (PAGE_WIDTH - PAGE_HORIZONTAL_MARGIN * 2) / PAGE_HORIZONTAL_UNITS;
const VERTICAL_UNIT = (PAGE_HEIGHT - PAGE_VERTICAL_MARGIN * 2) / PAGE_VERTICAL_UNITS;

// Extra time to wait before generating the PDF. This should
// give enough time for map pan and loading animations to finish.
const PAGE_LOAD_SAFE_TIME_MARGIN = 600;
const AUTO_SCROLL_INTERVAL = 250;

export const DashboardPDFModal = (props: ModalProps) => {
  const t = useTranslation();
  const pagesWrapperRef = useRef<HTMLDivElement>(null);
  const [loadedInsights, setLoadedInsights] = useState<Record<string, boolean>>({});

  const currentDashboard = useCurrentDashboard();
  const currentDashboardInsights = useCurrentDashboardInsights();
  const { placements } = useTransientPlacements();

  const isAllLoaded = useMemo(
    () => placements.length > 0 && placements.every((placement) => loadedInsights[placement.insightId]),
    [loadedInsights, placements]
  );

  const attemptToGeneratePDFAndClose = useCallback(async () => {
    if (!currentDashboard) return;
    if (!isAllLoaded) return;

    await new Promise((resolve) => setTimeout(resolve, PAGE_LOAD_SAFE_TIME_MARGIN));

    // Make sure text insights won't show scroll.
    const scrollable = (pagesWrapperRef.current?.querySelectorAll('[data-scrollable]') ?? []) as Array<HTMLElement>;
    scrollable?.forEach((textArea) => (textArea.style.overflow = 'clip'));

    const pages = Array.from(pagesWrapperRef.current?.children ?? []) as Array<HTMLElement>;
    const doc = await PDF.elementToPDF(...pages);
    await doc?.save(currentDashboard.title + '.pdf', { returnPromise: true });
    props.onRequestClose?.();
  }, [currentDashboard, isAllLoaded, props]);

  const pages = useMemo(() => {
    const pages: Array<Array<BeeInsightPlacement>> = [placements.sort((a, b) => a.layout.top - b.layout.top)];

    return produce(pages, (pages) => {
      let maxIterations = 128; // Avoid infinite loop.
      while (maxIterations-- > 0) {
        let didChangePlacement = false;

        // Move all insights out of bounds to the next page.
        for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
          for (let placementIndex = 0; placementIndex < pages[pageIndex].length; placementIndex++) {
            const placement = pages[pageIndex][placementIndex];
            const placementBottom = placement.layout.top + placement.layout.height;
            const isOutOfBounds = placementBottom > PAGE_VERTICAL_UNITS;

            if (isOutOfBounds) {
              if (pages.length < pageIndex + 2) {
                pages.push([]);
              }
              pages[pageIndex + 1].push(...pages[pageIndex].splice(placementIndex, 1));
              didChangePlacement = true;
              placementIndex--;
            }
          }

          // If placements where moved, don't change the next pages yet.
          if (didChangePlacement) break;
        }

        // In all created pages, move all placements up so there is no empty space.
        for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
          const topMostPlacement = minBy(pages[pageIndex], (placement) => placement.layout.top)?.layout.top ?? 0;
          for (const placement of pages[pageIndex]) {
            placement.layout.top -= topMostPlacement;
          }
        }

        // If no placement was moved, break.
        if (!didChangePlacement) break;
      }

      return pages;
    });
  }, [placements]);

  useLayoutEffect(() => {
    if (props.isOpen) {
      attemptToGeneratePDFAndClose().then(null);
    } else {
      setLoadedInsights({});
    }
  }, [attemptToGeneratePDFAndClose, props.isOpen]);

  // Auto-scroll over the placements
  useLayoutEffect(() => {
    if (props.isOpen) {
      const tid = setInterval(async () => {
        const placements = document.querySelectorAll('[id*="pdf-placement-"]');
        for (const placement of placements) {
          placement.scrollIntoView();
          await new Promise((resolve) => setTimeout(resolve, AUTO_SCROLL_INTERVAL));
        }
      }, AUTO_SCROLL_INTERVAL);
      return () => clearInterval(tid);
    }
  }, [props.isOpen]);

  if (!props.isOpen) return null;

  return (
    <Modal suppressAutoScroll {...props}>
      <StyledModalContent ref={pagesWrapperRef}>
        {pages.map((pagePlacements, index) => (
          <Box
            key={index}
            relative
            column
            stretch
            style={{
              width: PAGE_WIDTH,
              height: PAGE_HEIGHT,
              border: '1px black solid',
            }}
          >
            <Box
              alignItems={'flex-end'}
              justifyContent={'space-between'}
              padding_050
              style={{
                height: PAGE_VERTICAL_MARGIN,
                marginLeft: PAGE_HORIZONTAL_MARGIN,
                marginRight: PAGE_HORIZONTAL_MARGIN,
              }}
            >
              <Box column gap_025>
                <Text typography={'Heading2'}>{currentDashboard?.title}</Text>
                <Text typography={'CaptionSmall'}>
                  {t('insights:dashboard_pdf_page_x_of_y', { x: index + 1, y: pages.length })}
                </Text>
              </Box>

              <Box column gap_025>
                <Text typography={'CaptionSmall'} color={'contentSecondary'}>
                  {t('insights:dashboard_pdf_powered_by')}
                </Text>
                <Logo width={96} />
                <Anchor tertiary suppressPadding href={APP.externalLinks.MARKETING_WEBSITE}>
                  <Text typography={'CaptionSmall'} color={'contentSecondary'}>
                    {new URL(APP.externalLinks.MARKETING_WEBSITE).hostname}
                  </Text>
                </Anchor>
              </Box>
            </Box>

            {pagePlacements.map(({ layout, insightId }) => {
              const insight = currentDashboardInsights[insightId];
              return (
                <Box
                  id={`pdf-placement-${insightId}`}
                  key={insight.id}
                  padding_050
                  style={{
                    position: 'absolute',
                    left: layout.left * HORIZONTAL_UNIT + PAGE_HORIZONTAL_MARGIN,
                    top: layout.top * VERTICAL_UNIT + PAGE_VERTICAL_MARGIN,
                    width: layout.width * HORIZONTAL_UNIT,
                    height: layout.height * VERTICAL_UNIT,
                  }}
                >
                  <InsightView
                    fit
                    paddingTop_100={isTextInsight(insight)}
                    paddingTop_050={isDataVisualizationInsight(insight)}
                    paddingRight_100
                    paddingLeft_100
                    paddingBottom_100
                    borderWidth={1}
                    borderColor={'borderSecondary'}
                    borderRadius_075
                    insight={insight}
                    onLoad={() => setLoadedInsights((curr) => ({ ...curr, [insight.id]: true }))}
                  />
                </Box>
              );
            })}
          </Box>
        ))}
      </StyledModalContent>

      <LoadingDownload />
    </Modal>
  );
};
