import React, { FC, useCallback, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import produce from 'immer';

import { ChartBar } from '@components/common/Icon/presets/ChartBar.tsx';
import { ChartLine } from '@components/common/Icon/presets/ChartLine.tsx';
import { ChartPie } from '@components/common/Icon/presets/ChartPie.tsx';
import { Copy } from '@components/common/Icon/presets/Copy.tsx';
import { Delete } from '@components/common/Icon/presets/Delete.tsx';
import { Download } from '@components/common/Icon/presets/Download.tsx';
import { Edit } from '@components/common/Icon/presets/Edit.tsx';
import { Image } from '@components/common/Icon/presets/Image.tsx';
import { Insights } from '@components/common/Icon/presets/Insights.tsx';
import { Map } from '@components/common/Icon/presets/Map.tsx';
import { Metric } from '@components/common/Icon/presets/Metric.tsx';
import { Table } from '@components/common/Icon/presets/Table.tsx';
import { Text as TextIcon } from '@components/common/Icon/presets/Text.tsx';
import { Menu } from '@components/common/Menu';
import { MenuItem } from '@components/common/Menu/types.ts';
import { useDynamicModal } from '@components/common/ModalBase/hooks.tsx';
import { ThreeDotsMenuTrigger } from '@components/common/ThreeDotsMenuTrigger';
import { useDatasetMeta, useDatasetQuery } from '@components/insights/InsightDataView/hooks/query.ts';
import { PropertiesUtil } from '@components/insights/InsightDataView/util/properties.ts';
import { StyledHeadMenuWrapper } from '@components/insights/InsightMenu/styles.ts';
import { isDataVisualizationInsight } from '@components/insights/types.ts';
import APP from '@config/constants.ts';
import { Analytics } from '@helpers/Analytics';
import { AnalyticsEventType } from '@helpers/Analytics/types.ts';
import { CSV } from '@helpers/CSV/CSV.ts';
import { DOM } from '@helpers/DOM';
import { useDispatch } from '@helpers/Thunk/hooks.ts';
import { URLUtil } from '@helpers/URL';
import { useTranslation } from '@hooks/useTranslation.ts';
import { useEnsuredUser } from '@redux/Auth/hooks.ts';
import {
  makeInsightDeleteThunk,
  makeInsightSaveThunk,
  makeInsightsDashboardSaveThunk,
  makeInsightsPresetsCreateThunk,
} from '@redux/Insights/actions.ts';
import { useCurrentDashboard, useCurrentDashboardId, useInsightsDashboardsRefresher } from '@redux/Insights/hooks.ts';

export const InsightMenu: FC<{ insight: BeeInsight; staticQuery?: any }> = ({ insight, staticQuery }) => {
  const t = useTranslation();
  const history = useHistory();
  const dashboardId = useCurrentDashboardId();
  const dashboard = useCurrentDashboard();
  const dispatch = useDispatch();
  const { query } = useDatasetQuery(isDataVisualizationInsight(insight) ? insight : null, { staticQuery });
  const { isStaff } = useEnsuredUser();
  const datasetMeta = useDatasetMeta(isDataVisualizationInsight(insight) ? insight : null);
  const dashboardsRefresher = useInsightsDashboardsRefresher();
  const dynamicDeleteModal = useDynamicModal();
  const enableExport = isDataVisualizationInsight(insight);

  const insightTitleAsFilename = useMemo(() => {
    return insight.title.replace(/ /g, '_');
  }, [insight.title]);

  const goToInsightsBuilder = useCallback(
    () =>
      history.push(
        URLUtil.buildPagePath(APP.routes.insightsDashboardBuilderEdit, {
          pathParams: { did: dashboardId, uid: insight.id },
        })
      ),
    [dashboardId, history, insight.id]
  );

  const duplicateInsight = useCallback(async () => {
    if (!dashboard) return;
    const newInsight = await dispatch(makeInsightSaveThunk(null, { ...insight, id: null }));

    if (newInsight) {
      const updatedDashboard = produce(dashboard, (dashboard) => {
        const currentPlacement = dashboard?.insightsPlacements.find((placement) => placement.insightId === insight.id);

        if (dashboard && currentPlacement && newInsight) {
          const newWidth = Math.floor(currentPlacement.layout.width / 2);
          currentPlacement.layout.width = Math.ceil(currentPlacement.layout.width / 2);
          const newPlacement: any = {
            ...currentPlacement,
            insightId: newInsight.id as number,
            layout: {
              ...currentPlacement.layout,
              width: newWidth,
              left: currentPlacement.layout.left + currentPlacement.layout.width,
            },
          };
          delete newPlacement.id; // Create a new item.
          dashboard.insightsPlacements.push(newPlacement);
        }
      });

      await dispatch(makeInsightsDashboardSaveThunk(updatedDashboard));
    }
  }, [dashboard, dispatch, insight]);

  const changeInsightType = useCallback(
    (type: BeeInsightType) => {
      dispatch(makeInsightSaveThunk(dashboardId, { ...insight, type }));
    },
    [dashboardId, dispatch, insight]
  );

  const exportToImg = useCallback(async () => {
    const id = `insight-view-${insight.id}`;
    const insightViewElement = document.getElementById(id);
    if (insightViewElement) {
      const image = (await DOM.elementToCanvas(insightViewElement, 'alt1')) as string;
      await DOM.triggerDownloadFromDataURI(image, `${insightTitleAsFilename}.png`);
    }
  }, [insight.id, insightTitleAsFilename]);

  const exportToCSV = useCallback(async () => {
    const idProps = Object.entries(datasetMeta?.properties ?? {})
      .filter(([, { type }]) => PropertiesUtil.isIdProp(type))
      .map(([key]) => key);

    // Cleanup id props since they are meaningless to users.
    const entries = (query?.dataset.entries ?? []).map((row) => {
      for (const propKey of idProps) {
        delete row[propKey];
      }
      return row;
    });

    const data = CSV.jsonToCSV(entries);
    const blob = new Blob([data], { type: 'text/csv' });
    await DOM.triggerDownloadFromBlob(blob, `${insightTitleAsFilename}.csv`);
  }, [datasetMeta?.properties, insightTitleAsFilename, query?.dataset.entries]);

  const deleteInsight = useCallback(() => {
    dynamicDeleteModal.open({
      headerProps: {
        title: t('insights:insight_delete_modal_title'),
        subtitle: t('insights:insight_delete_modal_message'),
      },
      footerProps: {
        acceptText: t('delete'),
        rejectText: t('cancel'),
        autoCloseOnAccept: true,
        autoCloseOnReject: true,
        acceptButtonProps: { danger: true },
        onAcceptClick: async () => {
          await dispatch(makeInsightDeleteThunk(insight));
          await dashboardsRefresher.refresh();
          Analytics.sendEvent({ event: AnalyticsEventType.INSIGHTS_INSIGHT_DELETE });
        },
      },
    });
  }, [dashboardsRefresher, dispatch, dynamicDeleteModal, insight, t]);

  const createPreset = useCallback(() => {
    const queryCopy: BeeInsightsDatasetQuery = JSON.parse(JSON.stringify(query));
    queryCopy.dataset.aggregatedEntries = queryCopy.dataset.aggregatedEntries.slice(0, 8);
    Object.values(queryCopy.dataset.aggregatedEntries).forEach((entryMap) => {
      Object.entries(entryMap).forEach(([key, items]) => {
        entryMap[key] = items.slice(0, 8);
      });
    });
    dispatch(makeInsightsPresetsCreateThunk(insight, queryCopy));
  }, [dispatch, insight, query]);

  const menuItems = useMemo<Array<MenuItem>>(() => {
    const items: Array<MenuItem> = [
      { title: t('edit'), icon: Edit, onClick: goToInsightsBuilder },
      { title: t('duplicate'), icon: Copy, onClick: duplicateInsight },
      {
        title: t('insights:insight_change_type_to'),
        icon: Insights,
        subItems: [
          {
            title: t('insights:insight_type_bar_chart'),
            icon: ChartBar,
            checked: insight.type === 'chart-bar',
            onClick: () => changeInsightType('chart-bar'),
          },
          {
            title: t('insights:insight_type_line_chart'),
            icon: ChartLine,
            checked: insight.type === 'chart-line',
            onClick: () => changeInsightType('chart-line'),
          },
          {
            title: t('insights:insight_type_pie_chart'),
            icon: ChartPie,
            checked: insight.type === 'chart-pie',
            onClick: () => changeInsightType('chart-pie'),
          },
          {
            title: t('insights:insight_type_table'),
            icon: Table,
            checked: insight.type === 'table',
            onClick: () => changeInsightType('table'),
          },
          {
            title: t('insights:insight_type_metric'),
            icon: Metric,
            checked: insight.type === 'metric',
            onClick: () => changeInsightType('metric'),
          },
          {
            title: t('insights:insight_type_map'),
            icon: Map,
            checked: insight.type === 'map-with-dots',
            onClick: () => changeInsightType('map-with-dots'),
          },
          {
            title: t('insights:insight_type_heading'),
            icon: TextIcon,
            checked: insight.type === 'heading',
            onClick: () => changeInsightType('heading'),
          },
          {
            title: t('insights:insight_type_text'),
            icon: TextIcon,
            checked: insight.type === 'text',
            onClick: () => changeInsightType('text'),
          },
        ],
      },
    ];

    if (enableExport) {
      items.push({
        title: t('insights:insight_export_to'),
        icon: Download,
        subItems: [
          {
            title: t('insights:insight_export_to_image'),
            icon: Image,
            onClick: () => exportToImg(),
          },
          {
            title: t('insights:insight_export_to_spreadsheet'),
            icon: Table,
            onClick: () => exportToCSV(),
          },
        ],
      });
    }

    items.push({ title: t('delete'), icon: Delete, onClick: deleteInsight, danger: true });

    if (isStaff) {
      items.push({ title: t('insights:insight_create_preset'), icon: Insights, onClick: createPreset });
    }

    return items;
  }, [
    changeInsightType,
    createPreset,
    deleteInsight,
    duplicateInsight,
    enableExport,
    exportToCSV,
    exportToImg,
    goToInsightsBuilder,
    insight.type,
    isStaff,
    t,
  ]);

  const menuId = `insight-menu-${insight.id}`;

  return (
    <>
      <StyledHeadMenuWrapper data-image-ignore>
        <ThreeDotsMenuTrigger id={menuId} />
        <Menu items={menuItems} target={menuId} placement={'bottom-end'} subMenuProps={{ placement: 'right' }} />
      </StyledHeadMenuWrapper>
      {dynamicDeleteModal.content}
    </>
  );
};
