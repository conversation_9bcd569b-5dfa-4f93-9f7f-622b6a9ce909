import { FCWith<PERSON>hildren, ReactNode, useLayoutEffect, useRef } from 'react';
import throttle from 'lodash/throttle';

import { StyledAutoScaledText, StyledAutoScaledTextWrapper } from '@components/common/AutoScaledText/styles.ts';
import { TextProps } from '@components/common/Text/types.ts';

export const AutoScaledText: FCWithChildren<
  TextProps & { heading?: ReactNode; textMaxSize: number; textPadding: number }
> = ({ heading, textMaxSize, textPadding, children, ...textProps }) => {
  const textWrapperRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const textWrapper = textWrapperRef.current;
    if (!textWrapper) return;

    const handle = throttle(
      () => {
        function getScaleFromMatrix(matrixStr: string) {
          const values = matrixStr
            .match(/matrix\(([^)]+)\)/)?.[1]
            .split(',')
            .map(parseFloat);
          if (values) {
            const [a, b] = values;
            return Math.sqrt(a * a + b * b);
          }
          return 1.0;
        }

        const textWrapperRect = textWrapper.getBoundingClientRect();

        const textHeading = (heading ? textWrapper?.children[0] : null) as HTMLElement;
        const text = (heading ? textWrapper?.children[1] : textWrapper?.children[0]) as HTMLElement;
        if (!text) return;

        const textRect = text.getBoundingClientRect();
        const textHeadingRect = textHeading?.getBoundingClientRect();

        const existingScale = getScaleFromMatrix(getComputedStyle(text).transform);
        const scale = Math.max(
          0.05,
          Math.min(
            Math.min(textMaxSize, textWrapperRect.width - textPadding * 2) / (textRect.width / existingScale),
            Math.min(textMaxSize, textWrapperRect.height - textPadding * 2) / (textRect.height / existingScale)
          )
        );
        text.style.transform = `scale(${scale})`;

        if (textHeading && textHeadingRect) {
          textHeading.style.position = `absolute`;
          textHeading.style.top = `calc(50% - ${textHeadingRect.height}px - ${
            (scale * textRect.height) / existingScale / 2
          }px)`;
        }
      },
      80,
      { leading: false, trailing: true }
    );

    const observer = new ResizeObserver(handle);

    observer.observe(textWrapper);
    return () => {
      observer.disconnect();
      handle.cancel();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StyledAutoScaledTextWrapper ref={textWrapperRef}>
      {heading}
      <StyledAutoScaledText
        data-pdf-unselectable={true}
        style={{
          fontSize: `${textMaxSize}px`,
          lineHeight: `${textMaxSize}px`,
        }}
        {...textProps}
      >
        {children}
      </StyledAutoScaledText>
    </StyledAutoScaledTextWrapper>
  );
};
