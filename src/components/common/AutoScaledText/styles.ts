import styled, { css, keyframes } from 'styled-components';

import { Box } from '@components/common/Box';
import { Text } from '@components/common/Text';

const initialAnim = keyframes`
  0% {opacity: 0.0;}
  30% {opacity: 0.0;}
  100% {opacity: 1.0;}
`;

export const StyledAutoScaledTextWrapper = styled(Box).attrs({
  relative: true,
  fit: true,
  center: true,
})(
  () => css`
    overflow: clip;
  `
);

export const StyledAutoScaledText = styled(Text)(
  ({ theme }) => css`
    position: absolute;
    transition: ${theme.animations.transitionFast('scale')};
    transform: scale(0.05);

    animation: ${initialAnim};
    animation-duration: 1000ms;
    animation-iteration-count: 1;
    animation-timing-function: linear;
  `
);
