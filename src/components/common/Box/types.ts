import React, { HTMLAttributes, ImgHTMLAttributes, LabelHTMLAttributes, ReactNode, RefAttributes } from 'react';
import { Color, CSSObject, Shadow, Spacing } from 'styled-components';

export type BoxProps<T extends BoxTag> = BoxDefaultProps<T> & BoxHTMLAttributes<T> & BoxSpacingProps;

export interface BoxDefaultProps<T extends BoxTag> {
  /**
   * Prints the box bounds to help on debugging.
   * */
  debug?: boolean;

  /**
   * Overrides the default box div element.
   * */
  tag?: T;

  /**
   * Shortcut for
   *    position: relative;
   * */
  relative?: boolean;

  /**
   * Shortcut for:
   *     flex: 1;
   * */
  fit?: boolean;

  /**
   * Shortcut for:
   *     align-items: center;
   *     justify-content: center;
   * */
  center?: boolean;

  /**
   * Shortcut for:
   *     flex-direction: column;
   * */
  column?: boolean;

  /**
   * Shortcut for:
   *     align-items: stretch;
   * */
  stretch?: boolean;

  /**
   * Shortcut for:
   *     cursor: pointer;
   * */
  clickable?: boolean;

  /**
   * Shortcut for the paper appearance.
   * */
  paper01?: boolean;
  paper02?: boolean;
  paper03?: boolean;

  /**
   * Shortcut to make display block instead of flex
   * */
  block?: boolean;

  /**
   * Shortcut to make display grid instead of flex
   * */
  grid?: boolean;

  /**
   * Shortcut to give the element 100% width
   * */
  fullWidth?: boolean;

  /**
   * Shortcut to give the element 100% height
   * */
  fullHeight?: boolean;

  /**
   * Shortcut to make flex items wrap.
   * */
  wrap?: boolean;

  /**
   * CSS Shortcuts.
   * */
  flex?: CSSObject['flex'];
  flexGrow?: CSSObject['flexGrow'];
  alignItems?: CSSObject['alignItems'];
  justifyContent?: CSSObject['justifyContent'];
  alignSelf?: CSSObject['alignSelf'];
  gap?: CSSObject['gap'];
  borderWidth?: number;
  overflow?: CSSObject['overflow'];

  /**
   * Sets the wanted color props from the
   * given theme color keys.
   * */
  color?: Color;
  backgroundColor?: Color;
  borderColor?: Color;

  /**
   * Sets the wanted box-shadow from the given
   * theme keys.
   * */
  shadow?: Shadow;

  /**
   * Add more props as needed...
   * */
}

/**
 * HTML attributes.
 * */
export type BoxHTMLAttributes<T extends BoxTag> = T extends 'img'
  ? BoxHTMLImageAttributes
  : T extends 'label'
  ? BoxHTMLLabelAttributes
  : T extends 'form'
  ? BoxHTMLFormAttributes
  : BoxDefaultHTMLAttributes;

/**
 * Generic HTML attributes.
 * */
export interface BoxDefaultHTMLAttributes {
  id?: HTMLAttributes<HTMLElement>['id'];
  className?: HTMLAttributes<HTMLElement>['className'];
  title?: HTMLAttributes<HTMLElement>['title'];
  style?: HTMLAttributes<HTMLElement>['style'];
  onClick?: HTMLAttributes<HTMLElement>['onClick'];
  children?: ReactNode;
}

/**
 * Label specific HTML attributes.
 * */
export interface BoxHTMLLabelAttributes extends BoxDefaultHTMLAttributes {
  htmlFor?: LabelHTMLAttributes<HTMLElement>['htmlFor'];
}

/**
 * Image specific HTML attributes.
 * */
export interface BoxHTMLImageAttributes extends BoxDefaultHTMLAttributes {
  src?: ImgHTMLAttributes<HTMLElement>['src'];
  alt?: ImgHTMLAttributes<HTMLElement>['alt'];
}

/**
 * Form specific HTML attributes.
 * */
export interface BoxHTMLFormAttributes extends BoxDefaultHTMLAttributes {
  onSubmit?: ImgHTMLAttributes<HTMLElement>['onSubmit'];
}

/**
 * Special set of props for padding, margin and gap.
 * */
export type BoxSpacingProps = BoxPaddingProps & BoxMarginProps & BoxGapProps & BoxBorderRadiusProps;

/**
 * Special mappings to generate props like:
 *     padding_050, padding_200, paddingVertical_050, paddingHorizontal_200, etc...
 * */
export type BoxPaddingProps = {
  [S in Spacing as `padding${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `padding${Capitalize<Direction>}${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `padding${Capitalize<Orientation>}${Uppercase<Spacing>}`]?: boolean;
};

/**
 * Special mappings to generate props like:
 *     margin_050, margin_200, marginVertical_050, marginHorizontal_200, etc...
 * */
export type BoxMarginProps = {
  [S in Spacing as `margin${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `margin${Capitalize<Direction>}${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `margin${Capitalize<Orientation>}${Uppercase<Spacing>}`]?: boolean;
};

/**
 * Special mappings to generate props like:
 *     gap_050, gap_200, gap_050, gapHorizontal_200, etc...
 * */
export type BoxGapProps = {
  [S in Spacing as `gap${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `gap${Capitalize<Direction>}${Uppercase<Spacing>}`]?: boolean;
} & {
  [S in Spacing as `gap${Capitalize<Orientation>}${Uppercase<Spacing>}`]?: boolean;
};

/**
 * Special mappings to generate props like:
 *     borderRadius_050, borderRadius_200, borderRadius_050, borderRadiusHorizontal_200, etc...
 * */
export type BoxBorderRadiusProps = {
  [S in Spacing as `borderRadius${Uppercase<Spacing>}`]?: boolean;
};

export type Orientation = 'horizontal' | 'vertical';
export type Direction = 'left' | 'right' | 'top' | 'bottom';

/**
 * Types hook types.
 * */
export interface CachedStyleValue<T extends BoxTag> {
  style: CSSObject | null;
  props: BoxProps<T> | null;
}

export type StyleGetter<T extends BoxTag> = (props: BoxDefaultProps<T>) => CSSObject;
export type PropStyleMap<T extends BoxTag> = { [P in keyof BoxDefaultProps<T>]: CSSObject };

/**
 * Helper component types;
 * */
export type BoxTag = keyof Pick<JSX.IntrinsicElements, 'div' | 'span' | 'label' | 'img' | 'form'>;
export type BoxRef<T extends BoxTag> = HTMLElementTagNameMap[T];
export type BoxPropsWithRef<T extends BoxTag> = BoxProps<T> & RefAttributes<BoxRef<T>>;
export type BoxFunctionalComponent<T extends BoxTag> = React.FC<BoxPropsWithRef<T>>;
export type BoxStyledProps = { boxStyle: CSSObject };
