import styled from 'styled-components';

import { BoxStyledProps } from '@components/common/Box/types';

export const BoxDiv = styled.div<BoxStyledProps>(({ boxStyle }) => boxStyle);
export const BoxSpan = styled.span<BoxStyledProps>(({ boxStyle }) => boxStyle);
export const BoxLabel = styled.label<BoxStyledProps>(({ boxStyle }) => boxStyle);
export const BoxImage = styled.img<BoxStyledProps>(({ boxStyle }) => boxStyle);
export const BoxForm = styled.form<BoxStyledProps>(({ boxStyle }) => boxStyle);
