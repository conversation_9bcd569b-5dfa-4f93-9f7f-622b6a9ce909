import styled, { css } from 'styled-components';

export const StyledAutoRefreshProgress = styled.span(
  ({ theme }) => css`
    position: absolute;
    inset: auto 2px 1px 2px;
    height: 3px;
    border-radius: 3px;
    background-color: ${theme.colors.surfaceDefault};

    span {
      position: absolute;
      inset: 0;
      background-color: ${theme.colors.contentPositive};
      transition: ${theme.animations.transitionMedium('right')};
    }
  `
);
