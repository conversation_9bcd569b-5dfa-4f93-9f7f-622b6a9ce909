import { FC, useCallback, useContext, useEffect, useId, useMemo, useState } from 'react';
import moment from 'moment-timezone';

import { BigMonitorToolsRefreshContext } from '@components/common/BigMonitorTools/context.tsx';
import { StyledAutoRefreshProgress } from '@components/common/BigMonitorTools/styles.ts';
import { BigMonitorToolsProps } from '@components/common/BigMonitorTools/types.ts';
import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { _FullScreen } from '@components/common/Icon/presets/_FullScreen.tsx';
import { _FullScreenExit } from '@components/common/Icon/presets/_FullScreenExit.tsx';
import { ChevronDown } from '@components/common/Icon/presets/ChevronDown.tsx';
import { Refresh } from '@components/common/Icon/presets/Refresh.tsx';
import { Menu } from '@components/common/Menu';
import { MenuItem } from '@components/common/Menu/types.ts';
import { Text } from '@components/common/Text';
import { Tooltip } from '@components/common/Tooltip';
import { useFullScreenControl } from '@helpers/FullScreen/hooks.ts';
import { useLocalStorageState } from '@helpers/Storage/hooks.ts';
import { useTranslation } from '@hooks/useTranslation.ts';

import { ResponsiveRender } from '../ResponsiveRender';

const DEF_AUTO_UPDATE_INTERVALS_IN_HOURS = [1, 2, 4, 8, 12, 24, 48];
const DEF_AUTO_UPDATE_INTERVAL_UNIT = 60 * 60 * 1000;
const DEF_AUTO_UPDATE_CHECK_INTERVAL = 1000;

export const BigMonitorTools: FC<BigMonitorToolsProps> = ({
  storageKey,
  fullScreenTarget,
  onBeforeRefresh,
  ...boxProps
}) => {
  const t = useTranslation();
  const menuId = useId();
  const context = useContext(BigMonitorToolsRefreshContext);

  const [{ autoUpdate }, setStorageState] = useLocalStorageState(`${storageKey}-big-monitor-tools-storage-state`, {
    autoUpdate: {
      enabled: false,
      intervalInHours: 0,
    },
  });

  // Helps to update the "Last update x seconds ago" text.
  const [, setForceUpdateIndex] = useState(0);

  const [autoUpdateIntervalProgress, setAutoUpdateIntervalProgress] = useState(0);
  const [lastUpdateTimestamp, setLastUpdateTimestamp] = useState(() => Date.now());

  const { isFullScreen, canToggleFullScreen, toggleFullScreen } = useFullScreenControl({ target: fullScreenTarget });

  const requestAutoUpdate = useCallback(async () => {
    onBeforeRefresh?.();
    context?.refresh();
    setLastUpdateTimestamp(Date.now());
  }, [context, onBeforeRefresh]);

  const setAutoUpdateInterval = useCallback(
    (intervalInHours: number) => {
      setStorageState((state) => ({ ...state, autoUpdate: { enabled: intervalInHours > 0, intervalInHours } }));
      setAutoUpdateIntervalProgress(0);
    },
    [setStorageState]
  );

  const menuItems = useMemo<Array<MenuItem>>(
    () => [
      { title: t('auto_refresh_disabled'), onClick: () => setAutoUpdateInterval(0) },
      ...DEF_AUTO_UPDATE_INTERVALS_IN_HOURS.map((interval) => ({
        title: interval === 1 ? t('every_hour') : t('every_x_hours', { interval }),
        onClick: () => setAutoUpdateInterval(interval),
      })),
    ],
    [setAutoUpdateInterval, t]
  );

  // Handles the update loop
  useEffect(() => {
    let tid: ReturnType<typeof setInterval> | null = null;

    if (autoUpdate.enabled) {
      let start = Date.now();
      tid = setInterval(async () => {
        const elapsed = (Date.now() - start) / DEF_AUTO_UPDATE_INTERVAL_UNIT;
        const progress = Math.min(1, elapsed / autoUpdate.intervalInHours);
        setAutoUpdateIntervalProgress(progress);

        if (progress === 1) {
          await requestAutoUpdate();
          start = Date.now();
        }
      }, DEF_AUTO_UPDATE_CHECK_INTERVAL);
    } else {
      tid = setInterval(() => {
        setForceUpdateIndex((curr) => curr + 1);
      }, DEF_AUTO_UPDATE_CHECK_INTERVAL);
    }

    return () => tid && clearInterval(tid);
  }, [autoUpdate.enabled, autoUpdate.intervalInHours, requestAutoUpdate]);

  const didUpdateNow = moment().diff(moment(lastUpdateTimestamp), 'seconds') < 30;

  return (
    <ResponsiveRender from="desktopSM">
      <Box center gap_050 {...boxProps}>
        {canToggleFullScreen && (
          <Button iconOnly aria-label={t('common:fullscreen')} elevated suppressPadding onClick={toggleFullScreen}>
            <Box padding_025>{isFullScreen ? <_FullScreenExit /> : <_FullScreen />}</Box>
            <Tooltip appendTo={fullScreenTarget}>
              <Text typography={'TooltipSmall'}>{t(isFullScreen ? 'exit_fullscreen' : 'enter_fullscreen')}</Text>
            </Tooltip>
          </Button>
        )}
        <Button iconOnly aria-label={t('refresh_screen')} elevated suppressPadding onClick={requestAutoUpdate}>
          <Box padding_025>
            <Refresh />
          </Box>
          <Tooltip appendTo={fullScreenTarget}>
            <Text typography={'TooltipSmall'}>{t('refresh_screen')}</Text>
          </Tooltip>
        </Button>
        <Button withTrailingIcon elevated suppressPadding>
          <Box id={menuId} center paddingHorizontal_050 gap_050>
            {autoUpdate.enabled
              ? autoUpdate.intervalInHours == 1
                ? t('auto_refresh_screen_every_hour')
                : t('auto_refresh_screen_every_x_hours', { interval: autoUpdate.intervalInHours })
              : t('auto_refresh_disabled')}
            <ChevronDown />
          </Box>
          {autoUpdate.enabled && (
            <StyledAutoRefreshProgress>
              <span style={{ right: `${(1 - autoUpdateIntervalProgress) * 100}%` }} />
            </StyledAutoRefreshProgress>
          )}

          <Menu appendTo={fullScreenTarget} target={menuId} items={menuItems} placement={'bottom'} />
        </Button>

        <Text typography={'CaptionSmall'}>
          {t('last_updated')}{' '}
          <Text typography={'CaptionSmall'} weight={'700'}>
            {didUpdateNow ? t('now') : moment(lastUpdateTimestamp).fromNow()}
          </Text>
        </Text>
      </Box>
    </ResponsiveRender>
  );
};
