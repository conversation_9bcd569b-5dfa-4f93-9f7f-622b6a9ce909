import { create<PERSON>ontext, FCWithChildren, useCallback, useMemo, useState } from 'react';

import { BigMonitorToolsRefreshContextValue } from '@components/common/BigMonitorTools/types.ts';

export const BigMonitorToolsRefreshContext = createContext<BigMonitorToolsRefreshContextValue | null>(null);

export const BigMonitorToolsRefreshProvider: FCWithChildren = ({ children }) => {
  const [refreshIndex, setRefreshIndex] = useState(0);

  const refresh = useCallback(() => setRefreshIndex((curr) => curr + 1), []);
  const context = useMemo(() => ({ refresh }), [refresh]);

  return (
    <BigMonitorToolsRefreshContext.Provider key={refreshIndex} value={context}>
      {children}
    </BigMonitorToolsRefreshContext.Provider>
  );
};
