import * as React from 'react';
import { DismissButton, FocusScope, useOverlay } from 'react-aria';
import styled from 'styled-components';

import { from } from '@style/mediaQueries';

interface PopoverProps {
  popoverRef?: React.RefObject<HTMLDivElement>;
  children: React.ReactNode;
  isOpen?: boolean;
  onClose: () => void;
}

const StyledWrapper = styled.div(
  ({ theme }) => `
    position: absolute;
    box-shadow: ${theme.shadows.boxShadow02};
    transform: translate(-1px, 4px);
    z-index: ${theme.getZIndexOf('content')};
    
    ${from('tablet')} {
      min-width: 50%;
    }
  `
);

export function Popover(props: PopoverProps) {
  const ref = React.useRef<HTMLDivElement>(null);
  const { popoverRef = ref, isOpen, onClose, children } = props;

  // Handle events that should cause the popup to close,
  // e.g. blur, clicking outside, or pressing the escape key.
  const { overlayProps } = useOverlay(
    {
      isOpen,
      onClose,
      shouldCloseOnBlur: true,
      isDismissable: false,
    },
    popoverRef
  );

  // Add a hidden <DismissButton> component at the end of the popover
  // to allow screen reader users to dismiss the popup easily.
  return (
    <FocusScope restoreFocus>
      <StyledWrapper {...overlayProps} ref={popoverRef}>
        {children}
        <DismissButton onDismiss={onClose} />
      </StyledWrapper>
    </FocusScope>
  );
}
