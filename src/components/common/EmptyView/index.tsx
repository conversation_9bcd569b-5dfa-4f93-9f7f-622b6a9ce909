import React, { ReactNode } from 'react';
import styled, { css } from 'styled-components';

import { Box } from '@components/common/Box';
import { Button, NavRouterLink } from '@components/common/CTA';
import { Text } from '@components/common/Text';

const StyledWrapper = styled(Box).attrs({
  center: true,
  column: true,
  gap_150: true,
  padding_200: true,
  borderRadius_075: true,
  backgroundColor: 'surfaceSecondary',
})(
  ({ theme }) => css`
    margin: auto;
    text-align: center;

    p {
      max-width: min(500px, calc(100vw - ${theme.spacing._200}));
    }

    a {
      text-decoration: none;
    }
  `
);

export interface EmptyViewProps {
  heading: string;
  text: string;
  actionText?: string;
  actionHref?: string;
  actionIcon?: ReactNode;
  onActionClick?: () => void;
}

export const EmptyView: React.FCWithChildren<EmptyViewProps> = ({
  heading,
  text,
  actionText,
  actionHref,
  actionIcon,
  onActionClick,
  children,
}) => (
  <StyledWrapper>
    <Box column gap_050>
      <Text typography={'Heading2'} weight={'700'}>
        {heading}
      </Text>
      <Text typography="SmallParagraph">{text}</Text>
    </Box>

    {!!actionText &&
      (actionHref ? (
        <NavRouterLink primary to={actionHref} withLeadingIcon={!!actionIcon}>
          {actionIcon}
          {actionText}
        </NavRouterLink>
      ) : (
        <Button primary onClick={onActionClick} withLeadingIcon={!!actionIcon}>
          {actionIcon}
          {actionText}
        </Button>
      ))}

    {children}
  </StyledWrapper>
);
