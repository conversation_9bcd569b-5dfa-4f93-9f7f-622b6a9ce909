import styled, { css } from 'styled-components';

import { Box } from '@components/common/Box';
import { from } from '@style/mediaQueries';

export const StyledMatrix = styled(Box)(
  () => css`
    overflow: hidden auto;
  `
);

export const StyledMatrixInner = styled(Box)(
  ({ theme }) => css`
    position: relative;

    margin: ${theme.spacing._100};
    ${from('tablet')} {
      margin: ${theme.spacing._100} ${theme.spacing._150};
    }
  `
);

export const StyledCellMobile = styled(Box)(() => css``);

export const StyledCellDesktop = styled(Box)(
  ({ theme }) => css`
    position: absolute;
    z-index: ${theme.getZIndexOf('content')};
    transition: ${theme.animations.transitionMedium('top', 'left', 'width', 'height')};

    pointer-events: none;
    ${from('desktopSM')} {
      pointer-events: all;
    }

    ${StyledCellDagHandler}, ${StyledCellResizeHandler} {
      opacity: 0;
    }
    &:hover {
      ${StyledCellDagHandler}, ${StyledCellResizeHandler} {
        opacity: 1;
      }
    }
  `
);

export const StyledCellPreview = styled(Box)(
  ({ theme }) => css`
    position: absolute;
    background-color: ${theme.colors.surfaceTertiary};
    outline: ${theme.colors.surfaceTertiary} ${theme.spacing._050} solid;
    z-index: ${theme.getZIndexOf('base')};
    transition: ${theme.animations.transitionFast('top', 'left', 'width', 'height', 'opacity')};
    opacity: 0;
  `
);

export const StyledCellDagHandler = styled(Box)(
  ({ theme }) => css`
    position: absolute;
    top: calc(${theme.spacing._025} * 0.5);
    left: calc(${theme.spacing._025} * 0.25);

    cursor: grab;
    &:active {
      cursor: grabbing;
    }

    path {
      fill: ${theme.colors.surfaceTertiary};
    }

    &:hover path {
      fill: ${theme.colors.surfaceTertiaryHover};
    }

    transition: ${theme.animations.transitionMedium('background', 'opacity')};
  `
);

export const StyledCellResizeHandler = styled(Box)(
  ({ theme }) => css`
    position: absolute;

    &:before {
      content: '';
      position: absolute;
      inset: ${theme.spacing._025};
      border-radius: ${theme.borderRadius._025};
      background-color: ${theme.primitives.colors.grey04};
    }

    &:hover:before {
      background-color: ${theme.colors.surfaceTertiaryHover};
    }

    transition: ${theme.animations.transitionMedium('background', 'opacity')};
  `
);

export const StyledCellResizeHandlerHorizontal = styled(StyledCellResizeHandler)(
  ({ theme }) => css`
    left: 50%;
    width: ${theme.spacing._200};
    height: ${theme.spacing._075};
    transform: translateX(-50%);
    cursor: ns-resize;
  `
);

export const StyledCellResizeHandlerVertical = styled(StyledCellResizeHandler)(
  ({ theme }) => css`
    top: 50%;
    width: ${theme.spacing._075};
    height: ${theme.spacing._200};
    transform: translateY(-50%);
    cursor: ew-resize;
  `
);

export const StyledCellResizeHandlerTop = styled(StyledCellResizeHandlerHorizontal)(
  () => css`
    top: 0;
  `
);

export const StyledCellResizeHandlerBottom = styled(StyledCellResizeHandlerHorizontal)(
  () => css`
    bottom: 0;
  `
);

export const StyledCellResizeHandlerLeft = styled(StyledCellResizeHandlerVertical)(
  () => css`
    left: 0;
  `
);

export const StyledCellResizeHandlerRight = styled(StyledCellResizeHandlerVertical)(
  () => css`
    right: 0;
  `
);

export const StyledCellResizeHandlerBottomRight1 = styled(StyledCellResizeHandlerHorizontal)(
  ({ theme }) => css`
    bottom: 0;
    right: 0;
    left: auto;
    transform: translateX(0);
    cursor: nwse-resize;
    width: ${theme.spacing._150};
  `
);
export const StyledCellResizeHandlerBottomRight2 = styled(StyledCellResizeHandlerVertical)(
  ({ theme }) => css`
    top: auto;
    bottom: 0;
    right: 0;
    transform: translateY(0);
    cursor: nwse-resize;
    height: ${theme.spacing._150};
  `
);
