import { CellHandlingState, GridCellLayout, GridCellRegistry } from '@components/common/DragDropGrid/types';

const MAX_WHILE_ITERATIONS = 2048;

export const DragDropGridUtil = {
  applyMoveToOtherCells,
  autoAdjustCellsDown,
  autoAdjustCellsUp,
};

/**
 * Commits the transient layouts to the respective cells.
 * */
function applyMoveToOtherCells(
  /** Other cell instances. */
  otherCells: Array<GridCellRegistry>,

  /** Other cell layouts. */
  otherCellsTransientLayout: Record<string, GridCellLayout>
) {
  otherCells.forEach((c) => {
    if (c.id in otherCellsTransientLayout) {
      c.requestLayoutChange(otherCellsTransientLayout[c.id]);
    }
  });
}

/**
 * Automatically adjust cell layouts by moving colliding cells down.
 * Used to get cell "out of the way" while dragging the current one.
 * */
function autoAdjustCellsDown(
  /** Cell being dragged/resized */
  currentCellState: CellHandlingState,

  /** All cell instances. */
  cells: Record<string, GridCellRegistry>,

  /** Other cell layouts. */
  otherCellsTransientLayout: Record<string, GridCellLayout>
) {
  const flag = loopFlag();
  let intersectionPair = getNextIntersection(currentCellState, otherCellsTransientLayout);
  let lastMovedCellId: string | null = null;

  while (intersectionPair && flag.didntReachMaxIteration()) {
    // The second cell in the intersection is the one that gets moved.
    // If it was already moved last time, we invert it to make sure it only moves once.
    const [aId, bId] = intersectionPair[1] === lastMovedCellId ? intersectionPair.reverse() : intersectionPair;
    lastMovedCellId = bId;

    const aLayout = getTransientLayout(aId, currentCellState, otherCellsTransientLayout);
    const bLayout = getTransientLayout(bId, currentCellState, otherCellsTransientLayout);

    const nextLayout = {
      ...bLayout,
      top: bLayout.top + (aLayout.top + aLayout.height - bLayout.top),
    };
    otherCellsTransientLayout[bId] = nextLayout;
    cells[bId].requestTransientLayoutChange(nextLayout);
    intersectionPair = getNextIntersection(currentCellState, otherCellsTransientLayout);
  }
}

/**
 * Automatically adjust cell layouts by moving colliding cells up.
 * Used after "autoAdjustCellsDown()" to fill any blank space left.
 * */
function autoAdjustCellsUp(
  /** Cell being dragged/resized */
  currentCellState: CellHandlingState,

  /** All cell instances. */
  cells: Record<string, GridCellRegistry>,

  /** Other cell layouts. */
  otherCellsTransientLayout: Record<string, GridCellLayout>,

  options = {
    /**
     * If the current cell should also be adjusted.
     * - False while the cell is being hold.
     * - True when the cell is released.
     * */
    skipCurrent: false,
  }
) {
  const _cellIds = [...Object.keys(otherCellsTransientLayout)];

  if (!options.skipCurrent) {
    _cellIds.splice(0, 0, currentCellState.id);
  }

  let done = false;
  const flag = loopFlag();

  while (!done && flag.didntReachMaxIteration()) {
    let didMove = false;
    for (const cellId of _cellIds) {
      const cellLayout = getTransientLayout(cellId, currentCellState, otherCellsTransientLayout);
      if (cellLayout.top === 0) {
        continue;
      }

      let didFit = false;
      const maxIterations = 1;

      for (let i = 1; i <= maxIterations; i++) {
        const cellNextLayout = {
          ...cellLayout,
          top: cellLayout.top - i,
        };

        if (!hasIntersection(cellId, cellNextLayout, currentCellState, otherCellsTransientLayout)) {
          didFit = true;
          didMove = true;

          if (cellId === currentCellState.id) {
            currentCellState.transientPreviewLayout = cellNextLayout;
          } else {
            otherCellsTransientLayout[cellId] = cellNextLayout;
          }

          cells[cellId].requestTransientLayoutChange(cellNextLayout);

          break;
        }
      }

      if (didFit) {
        break;
      }
    }

    if (!didMove) {
      done = true;
    }
  }
}

/**
 * Returns the next pair of cells intersecting.
 * */
const getNextIntersection = (
  /** Cell currently being dragged */
  currentCellState: CellHandlingState,

  /** Layout of other cells. */
  otherCellsTransientLayout: Record<string, GridCellLayout>
) => {
  const _cellEntries = [
    [currentCellState.id, currentCellState.transientPreviewLayout],
    ...Object.entries(otherCellsTransientLayout),
  ] as Array<[string, GridCellLayout]>;

  for (let i = 0; i < _cellEntries.length; i++) {
    for (let j = i + 1; j < _cellEntries.length; j++) {
      const [aId, aLayout] = _cellEntries[i];
      const [bId, bLayout] = _cellEntries[j];

      if (aId !== bId && areCellLayoutsIntersecting(aLayout, bLayout)) {
        return [aId, bId];
      }
    }
  }
  return null;
};

/**
 * Checks whether the given cell layout intersects with any other.
 * */
function hasIntersection(
  /** Info of the cell - not necessarily the one being dragged/resized */
  cellId: string,
  cellNextLayout: GridCellLayout,

  /** Cell currently being dragged */
  currentCellState: CellHandlingState,

  /** Layout of other cells. */
  otherCellsTransientLayout: Record<string, GridCellLayout>
) {
  const _otherCellIds = [currentCellState.id, ...Object.keys(otherCellsTransientLayout)];

  for (const otherCellId of _otherCellIds) {
    if (otherCellId === cellId) continue;

    const otherCellLayout = getTransientLayout(otherCellId, currentCellState, otherCellsTransientLayout);
    if (areCellLayoutsIntersecting(cellNextLayout, otherCellLayout)) {
      return true;
    }
  }
  return false;
}

/**
 * Checks whether two given cell layouts are intersecting.
 * Ref: https://www.jeffreythompson.org/collision-detection/rect-rect.php
 * */
function areCellLayoutsIntersecting(a: GridCellLayout, b: GridCellLayout) {
  const aLeft = a.left;
  const aRight = a.left + a.width;
  const aTop = a.top;
  const aBottom = a.top + a.height;
  const bLeft = b.left;
  const bRight = b.left + b.width;
  const bTop = b.top;
  const bBottom = b.top + b.height;

  return [aLeft < bRight, aRight > bLeft, aTop < bBottom, aBottom > bTop].every(Boolean);
}

/**
 * Gets a transient cell layout from its id.
 * */
function getTransientLayout(
  cellId: string,
  currentCellState: CellHandlingState,
  otherCellsTransientLayout: Record<string, GridCellLayout>
) {
  return otherCellsTransientLayout[cellId] || currentCellState.transientPreviewLayout;
}

/**
 * Helper to provide while loops a flag.
 * */
function loopFlag(
  /**
   * To avoid infinite loops.
   * */
  maxIterations: number = MAX_WHILE_ITERATIONS
) {
  let stepsLeft = maxIterations;
  return {
    didntReachMaxIteration: () => {
      const endReached = stepsLeft === 0;

      if (endReached) {
        console.error('Reached max iterations for cell layout adjustments. There might be an implementation error.');
      }

      stepsLeft--;
      return !endReached;
    },
  };
}
