import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { CSSProperties, useTheme } from 'styled-components';
import * as uuid from 'uuid';

import { DragDropGridContext } from '@components/common/DragDropGrid/context';
import { DragDropGridUtil } from '@components/common/DragDropGrid/util';
import { Drag } from '@components/common/Icon/presets/Drag';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { useRequiredContext } from '@hooks/useRequiredContext';

import {
  StyledCellDagHandler,
  StyledCellDesktop,
  StyledCellMobile,
  StyledCellPreview,
  StyledCellResizeHandlerBottom,
  StyledCellResizeHandlerBottomRight1,
  StyledCellResizeHandlerBottomRight2,
  StyledCellResizeHandlerLeft,
  StyledCellResizeHandlerRight,
  StyledCellResizeHandlerTop,
} from './styles';
import { CellHandlingAnchor, CellHandlingState, GridCellLayout, GridCellProps } from './types';

export const GridCell: React.FCWithChildren<GridCellProps> = ({ layout, onLayoutChange, children, ...nativeProps }) => {
  const [id] = useState(() => uuid.v4());

  const cellRef = useRef<HTMLDivElement>(null);
  const cellPreviewRef = useRef<HTMLDivElement>(null);
  const handlerTopRef = useRef<HTMLDivElement>(null);
  const handlerBottomRef = useRef<HTMLDivElement>(null);
  const handlerLeftRef = useRef<HTMLDivElement>(null);
  const handlerRightRef = useRef<HTMLDivElement>(null);
  const handlerBottomRight1Ref = useRef<HTMLDivElement>(null);
  const handlerBottomRight2Ref = useRef<HTMLDivElement>(null);
  const handlerBoxRef = useRef<HTMLDivElement>(null);

  const theme = useTheme();
  const { matrixProps, registerCell, cells } = useRequiredContext(DragDropGridContext);

  const gap = theme.primitives.px._100;

  const getStyleFromLayout = useCallback(
    (layout: GridCellLayout) => {
      const { columnCount, rowHeight } = matrixProps;
      const gapPerColumn = (gap * (columnCount - 1)) / columnCount;

      const rowWidthPercent = 100.0 / columnCount;
      const cardUnitWidth = `${rowWidthPercent}% - ${gapPerColumn}px`;
      const cardUnitHeight = `${rowHeight}px - ${gapPerColumn}px`;
      const cardWidth = `${layout.width} * (${cardUnitWidth}) + ${(layout.width - 1) * gap}px`;
      const cardHeight = `${layout.height} * (${cardUnitHeight}) + ${(layout.height - 1) * gap}px`;

      const top = `calc(${layout.top} * (${cardUnitHeight} + ${gap}px))`;
      const left = `calc(${layout.left} * (${cardUnitWidth} + ${gap}px))`;
      const height = `calc(${cardHeight})`;
      const width = `calc(${cardWidth})`;
      return {
        top,
        left,
        width,
        height,
      };
    },
    [gap, matrixProps]
  );

  const getMobileStyleFromLayout = useCallback(
    (layout: GridCellLayout) => {
      const { rowHeight, rowHeightMobile } = matrixProps;
      return {
        height: (rowHeightMobile ?? rowHeight) * (layout.height ?? 1),
      };
    },
    [matrixProps]
  );

  const cellStyle = useMemo<CSSProperties>(() => getStyleFromLayout(layout), [getStyleFromLayout, layout]);
  const cellMobileStyle = useMemo<CSSProperties>(
    () => getMobileStyleFromLayout(layout),
    [getMobileStyleFromLayout, layout]
  );
  const cellPreviewStyle = useMemo<CSSProperties>(() => getStyleFromLayout(layout), [getStyleFromLayout, layout]);

  const updateCellStyle = useCallback((cell: HTMLElement | null, layout: GridCellLayout) => {
    if (!cell) return;

    const { top, left, width, height } = getStyleFromLayout(layout);
    cell.style.top = top;
    cell.style.left = left;
    cell.style.width = width;
    cell.style.height = height;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    const cell = cellRef.current;
    const cellPreview = cellPreviewRef.current;
    const cellParent = cell?.parentElement;
    const handlerTop = handlerTopRef.current;
    const handlerBottom = handlerBottomRef.current;
    const handlerLeft = handlerLeftRef.current;
    const handlerRight = handlerRightRef.current;
    const handlerBottomRight1 = handlerBottomRight1Ref.current;
    const handlerBottomRight2 = handlerBottomRight2Ref.current;
    const handlerBox = handlerBoxRef.current;
    if (
      !cell ||
      !cellPreview ||
      !cellParent ||
      !handlerTop ||
      !handlerBottom ||
      !handlerLeft ||
      !handlerRight ||
      !handlerBottomRight1 ||
      !handlerBottomRight2Ref
    )
      return;

    const unsubscribe: Array<() => void> = [];
    const cellTransition = getComputedStyle(cell).transition;

    const otherCells = Object.values(cells).filter((cell) => cell.id !== id);
    const otherCellsTransientLayout = otherCells.reduce(
      (acc, c) => ({
        ...acc,
        [c.id]: c.layout,
      }),
      {} as Record<string, GridCellLayout>
    );

    const state: CellHandlingState = {
      id,
      handling: false,
      anchor: 'top',
      startingCursorPosition: { x: 0, y: 0 },
      transientLayout: { ...layout },
      transientPreviewLayout: { ...layout },
    };

    // Show/hide the visual feedback showing the next available cell position.
    const showCellPreview = () => (cellPreview.style.opacity = '0.16');
    const hideCellPreview = () => (cellPreview.style.opacity = '0.0');

    // Enable/disables the cell transitioning animation to avoid visual
    // glitches while the cell is being dragged.
    const enableCellTransitions = () => (cell.style.transition = cellTransition);
    const disableCellTransitions = () => (cell.style.transition = 'none');

    const onMouseDown = (e: MouseEvent, anchor: CellHandlingAnchor) => {
      e.preventDefault();
      e.stopPropagation();

      state.handling = true;
      state.startingCursorPosition = { x: e.pageX, y: e.pageY };
      state.anchor = anchor;

      showCellPreview();
      disableCellTransitions();
    };

    const onMouseUp = (e: MouseEvent) => {
      if (!state.handling) return;

      e.preventDefault();
      e.stopPropagation();

      state.handling = false;

      hideCellPreview();
      enableCellTransitions();

      DragDropGridUtil.autoAdjustCellsUp(state, cells, otherCellsTransientLayout);
      DragDropGridUtil.applyMoveToOtherCells(otherCells, otherCellsTransientLayout);

      updateCellStyle(cell, state.transientPreviewLayout);
      updateCellStyle(cellPreview, state.transientPreviewLayout);

      onLayoutChange && onLayoutChange({ ...state.transientPreviewLayout });
    };

    const onMouseMove = (e: MouseEvent) => {
      if (!state.handling) return;

      e.preventDefault();
      e.stopPropagation();

      const transientLayout: GridCellLayout = { ...layout };
      const { anchor, startingCursorPosition } = state;
      const minWidth = matrixProps.minCellColumnCount ?? 1;
      const minHeight = matrixProps.minCellRowCount ?? 1;
      const pos = { x: e.pageX, y: e.pageY };
      const deltaPos = { x: pos.x - startingCursorPosition.x, y: pos.y - startingCursorPosition.y };
      const cellContainerRect = cellParent.getBoundingClientRect();

      let deltaX = (deltaPos.x / cellContainerRect.width) * matrixProps.columnCount;
      let deltaY = deltaPos.y / matrixProps.rowHeight;

      switch (anchor) {
        case 'left':
          deltaX = Math.max(deltaX, -transientLayout.left);
          deltaX = Math.min(deltaX, transientLayout.width - minWidth);
          transientLayout.left += deltaX;
          transientLayout.width -= deltaX;
          break;
        case 'right':
          deltaX = Math.max(-(transientLayout.width - minWidth), deltaX);
          deltaX = Math.min(matrixProps.columnCount - (transientLayout.left + transientLayout.width), deltaX);
          transientLayout.width += deltaX;
          break;
        case 'top':
          deltaY = Math.max(deltaY, -transientLayout.top);
          deltaY = Math.min(deltaY, transientLayout.height - minHeight);
          transientLayout.top += deltaY;
          transientLayout.height -= deltaY;
          break;
        case 'bottom':
          deltaY = Math.max(-(transientLayout.height - minHeight), deltaY);
          transientLayout.height += deltaY;
          break;
        case 'bottom-right':
          deltaY = Math.max(-(transientLayout.height - minHeight), deltaY);
          transientLayout.height += deltaY;
          deltaX = Math.max(-(transientLayout.width - minWidth), deltaX);
          deltaX = Math.min(matrixProps.columnCount - (transientLayout.left + transientLayout.width), deltaX);
          transientLayout.width += deltaX;
          break;
        case 'box':
          deltaX = Math.max(deltaX, -transientLayout.left);
          deltaX = Math.min(deltaX, matrixProps.columnCount - (transientLayout.left + transientLayout.width));
          deltaY = Math.max(deltaY, -transientLayout.top);
          transientLayout.left += deltaX;
          transientLayout.top += deltaY;
          break;
      }

      state.transientLayout = transientLayout;
      state.transientPreviewLayout = {
        top: Math.round(state.transientLayout.top),
        left: Math.round(state.transientLayout.left),
        width: Math.round(state.transientLayout.width),
        height: Math.round(state.transientLayout.height),
      };
      updateCellStyle(cell, state.transientLayout);
      updateCellStyle(cellPreview, state.transientPreviewLayout);

      DragDropGridUtil.autoAdjustCellsDown(state, cells, otherCellsTransientLayout);
      DragDropGridUtil.autoAdjustCellsUp(state, cells, otherCellsTransientLayout, { skipCurrent: true });
    };

    const mouseDownHandlers = [
      [handlerTop, 'top'],
      [handlerBottom, 'bottom'],
      [handlerLeft, 'left'],
      [handlerRight, 'right'],
      [handlerBottomRight1, 'bottom-right'],
      [handlerBottomRight2, 'bottom-right'],
      [handlerBox, 'box'],
    ] as Array<[HTMLElement, CellHandlingAnchor]>;

    mouseDownHandlers.forEach(([element, anchor]) => {
      const _handler = (e: MouseEvent) => onMouseDown(e, anchor);
      element.addEventListener('mousedown', _handler);
      unsubscribe.push(() => element.removeEventListener('mousedown', _handler));
    });

    const globalEvents = [
      [onMouseUp, 'mouseup'],
      [onMouseMove, 'mousemove'],
    ] as Array<[any, any]>;

    globalEvents.forEach(([handler, event]) => {
      const _handler = (e: MouseEvent) => handler(e);
      document.addEventListener(event, _handler);
      unsubscribe.push(() => document.removeEventListener(event, _handler));
    });

    return () => unsubscribe.forEach((u) => u());
  }, [
    id,
    cells,
    layout,
    onLayoutChange,
    getStyleFromLayout,
    matrixProps.columnCount,
    matrixProps.minCellColumnCount,
    matrixProps.minCellRowCount,
    matrixProps.rowHeight,
    updateCellStyle,
  ]);

  useEffect(() => {
    return registerCell({
      id,
      layout,
      requestLayoutChange: (layout) => {
        updateCellStyle(cellRef.current, layout);
        onLayoutChange && onLayoutChange(layout);
      },
      requestTransientLayoutChange: (layout) => {
        updateCellStyle(cellRef.current, layout);
      },
    });
  }, [id, layout, onLayoutChange, registerCell, updateCellStyle]);

  return (
    <>
      <ResponsiveRender until={'tablet'}>
        <StyledCellMobile
          style={cellMobileStyle}
          backgroundColor={'surfaceSecondary'}
          borderRadius_050
          {...nativeProps}
        >
          {children}
        </StyledCellMobile>
      </ResponsiveRender>

      <ResponsiveRender from={'desktopSM'}>
        <StyledCellDesktop
          ref={cellRef}
          style={cellStyle}
          backgroundColor={'surfaceSecondary'}
          borderRadius_050
          {...nativeProps}
        >
          {children}

          <StyledCellResizeHandlerTop ref={handlerTopRef} />
          <StyledCellResizeHandlerBottom ref={handlerBottomRef} />
          <StyledCellResizeHandlerLeft ref={handlerLeftRef} />
          <StyledCellResizeHandlerRight ref={handlerRightRef} />
          <StyledCellResizeHandlerBottomRight1 ref={handlerBottomRight1Ref} />
          <StyledCellResizeHandlerBottomRight2 ref={handlerBottomRight2Ref} />
          <StyledCellDagHandler ref={handlerBoxRef}>
            <Drag size={20} />
          </StyledCellDagHandler>
        </StyledCellDesktop>
        <StyledCellPreview ref={cellPreviewRef} style={cellPreviewStyle} borderRadius_050 />
      </ResponsiveRender>
    </>
  );
};
