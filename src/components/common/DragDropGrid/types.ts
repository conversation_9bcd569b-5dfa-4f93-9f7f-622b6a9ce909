export interface DragDropGridContextValue {
  cells: Record<string, GridCellRegistry>;
  registerCell: (cellRegistry: GridCellRegistry) => () => void;
  matrixProps: DragDropGridProps;
}

export interface DragDropGridProps {
  columnCount: number;
  rowHeight: number;
  rowHeightMobile?: number;
  minCellColumnCount?: number;
  minCellRowCount?: number;
}

export interface GridCellRegistry {
  id: string;
  layout: GridCellLayout;
  requestLayoutChange: (layout: GridCellLayout) => void;
  requestTransientLayoutChange: (layout: GridCellLayout) => void;
}

export interface GridCellProps {
  id?: string;
  layout: GridCellLayout;
  onLayoutChange?: (layout: GridCellLayout) => void;
}

export interface GridCellLayout {
  top: number;
  left: number;
  width: number;
  height: number;
}

export interface CellHandlingState {
  id: string;
  handling: boolean;
  anchor: CellHandlingAnchor;
  startingCursorPosition: { x: number; y: number };
  transientLayout: GridCellLayout;
  transientPreviewLayout: GridCellLayout;
}

export type CellHandlingAnchor = 'top' | 'bottom' | 'left' | 'right' | 'bottom-right' | 'box';
