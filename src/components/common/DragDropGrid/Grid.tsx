import React, { useCallback, useMemo, useState } from 'react';

import { ResponsiveRender } from '@components/common/ResponsiveRender';

import { DragDropGridContext } from './context';
import { StyledMatrix, StyledMatrixInner } from './styles';
import { GridCellRegistry, DragDropGridContextValue, DragDropGridProps } from './types';

export const Grid: React.FCWithChildren<DragDropGridProps> = ({
  columnCount,
  rowHeight,
  rowHeightMobile,
  minCellColumnCount,
  minCellRowCount,
  children,
}) => {
  const [cells, setCells] = useState<Record<string, GridCellRegistry>>({});

  const registerCell = useCallback((cell: GridCellRegistry) => {
    setCells((curr) => ({ ...curr, [cell.id]: cell }));
    return () => {
      setCells((curr) => {
        delete curr[cell.id];
        return { ...curr };
      });
    };
  }, []);

  const context = useMemo<DragDropGridContextValue>(() => {
    return {
      cells,
      registerCell,
      matrixProps: { columnCount, rowHeight, rowHeightMobile, minCellColumnCount, minCellRowCount },
    };
  }, [cells, columnCount, minCellColumnCount, minCellRowCount, registerCell, rowHeight, rowHeightMobile]);

  const innerHeight = useMemo(() => {
    const cellBottoms = Object.values(cells).map((cell) => (cell.layout.top + cell.layout.height) * rowHeight);
    const downMost = Math.max(0, ...cellBottoms);
    const containerHeight = Math.max(window.innerHeight, downMost + rowHeight);
    return `${containerHeight}px`;
  }, [cells, rowHeight]);

  return (
    <DragDropGridContext.Provider value={context}>
      <ResponsiveRender until={'tablet'}>
        <StyledMatrix column stretch backgroundColor={'background'}>
          <StyledMatrixInner column stretch gap_100>
            {children}
          </StyledMatrixInner>
        </StyledMatrix>
      </ResponsiveRender>

      <ResponsiveRender from={'desktopSM'}>
        <StyledMatrix column stretch backgroundColor={'background'}>
          <StyledMatrixInner style={{ minHeight: innerHeight }}>{children}</StyledMatrixInner>
        </StyledMatrix>
      </ResponsiveRender>
    </DragDropGridContext.Provider>
  );
};
