import styled, { css } from 'styled-components';

import { PRESETS } from '@components/common/Text/styles';

import { Box } from '../Box';
import { Button } from '../CTA';
import { Text } from '../Text';

import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

/**
 * Header styles
 */
export const StyledShowOnHover = styled.span(
  () => css`
    opacity: 0;
    max-width: 0;
    margin: 0;
  `
);

export const StyledHeader = styled(Box)(
  ({ theme }) => css`
    width: 100%;
    height: 100%;
    flex-direction: row;
    align-items: stretch;
    padding: 0 ${theme.spacing._100};
    transition: ${theme.animations.transitionFast('background-color')};
    overflow: hidden;

    &:hover,
    &:focus-within {
      background-color: ${theme.primitives.colors.grey02};

      ${StyledShowOnHover} {
        opacity: 1;
        max-width: 96px;
        margin-left: ${theme.spacing._050};
      }
    }
  `
);
export const StyledHeaderText = styled(Text).attrs({ typography: 'Heading3', weight: '600' })(() => css``);

export const StyledHeaderSortButton = styled(Button).attrs({
  unstyled: true,
})(
  () =>
    css`
      display: flex;
      align-items: center;
      overflow: hidden;
      flex: 1;
    `
);

export const StyledHeaderSortButtonTextBox = styled.div(
  () =>
    css`
      display: block;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-weight: 400;
      max-width: 100%;

      & > * {
        display: inline;
      }
    `
);

/**
 * Table styles
 */
export const DynamicTableStyleSetter = styled.div<{ $enableInteraction?: boolean; $isRowInteractive: boolean }>(
  ({ theme, $enableInteraction, $isRowInteractive }) => css`
    .ag-theme-alpine,
    .ag-theme-alpine-dark {
      --ag-border-color: ${theme.primitives.colors.grey04};
      --ag-row-border-color: ${theme.primitives.colors.grey04};
      --ag-alpine-active-color: transparent;
      --ag-icon-font-code-checkbox-checked: ${theme.primitives.colors.grey08};
      --ag-checkbox-unchecked-color: ${theme.primitives.colors.grey05};
      --ag-selected-row-background-color: ${theme.primitives.colors.grey04};
      --ag-row-hover-color: ${theme.primitives.colors.grey02};
      --ag-cell-horizontal-padding: 0;

      --tableHeight: 800px;

      transition: ${theme.animations.transitionFast('height')};
      height: var(--tableHeight);
      width: 100%;
      position: relative;

      pointer-events: ${$enableInteraction ? 'auto' : 'none'};
    }

    .ag-root-wrapper {
      border-radius: 4px;
    }

    .ag-row {
      background-color: transparent;
      cursor: ${$isRowInteractive ? 'pointer' : 'initial'};
    }

    .ag-cell {
      display: flex;
      align-items: center;
      outline: none !important;
      padding: 0 ${theme.spacing._100};

      // Makes the grid font weight looks consistent.
      -webkit-font-smoothing: antialiased;
    }

    .ag-cell:not([col-id*='select']),
    .ag-cell-last-left-pinned:not([col-id*='select'])
    .ag-cell-last-right-pinned:not([col-id*='select']) {
      border-right: none !important;
      border-left: none !important;
    }

    .ag-cell[col-id*='menu'] {
      padding: 0 ${theme.spacing._050};
    }

    .ag-cell-value[col-id^='select'],
    .ag-header-cell[col-id^='select'],
    .ag-header-group-cell[col-id^='0_'],
    .ag-header-group-cell[col-id^='select'] {
      border-right: var(--ag-border-color) 1px solid !important;
    }

    .ag-header-group-cell-label {
      padding: 0 ${theme.spacing._100};
    }

    .ag-header {
      background-color: ${theme.primitives.colors.white};
    }

    .ag-pinned-left-header .ag-header-cell-comp-wrapper {
      overflow: visible;
    }

    .ag-header,
    .ag-pinned-left-header,
    .ag-pinned-right-header,
    .ag-header-row,
    .ag-header-cell {
      overflow: visible;
    }

    .ag-header-group-text {
      font-weight: 600;
      ${css({
        fontSize: PRESETS.Heading3.fontSize,
      })}
    }

    .ag-ltr .ag-header-cell-resize {
      background-color: transparent;
      inset: 0 -1px 0 calc(100% - 1px);
      width: auto;
      transition: ${theme.animations.transitionFast('background-color')};

      &::before,
      &::after {
        content: '';
        position: absolute;
        inset: 0 0 0 50%;
        width: 0;
        height: 0;
        border: transparent 12px solid;
        background: transparent;
        transform: translateX(-50%) scaleX(0.55);
        transition: ${theme.animations.transitionFast('border-color')};
      }

      &::after {
        inset: calc(100% - 24px) 0 0 50%;
        transform: translateX(-50%) rotate(180deg) scaleX(0.55);
      }

      &:hover {
        background-color: ${theme.primitives.colors.grey04};

        &::before,
        &::after {
          border-top-color: ${theme.primitives.colors.grey04};
        }
      }

      &:active,
      &:focus {
        background-color: ${theme.primitives.colors.focus02};

        &::before,
        &::after {
          border-top-color: ${theme.primitives.colors.focus02};
        }
      }
    }

    .ag-pinned-left-cols-container,
    .ag-pinned-left-header,
    .ag-pinned-right-cols-container,
    .ag-pinned-right-header,
    .ag-header {
      border: none;

      &::before,
      &::after {
        content: '';
        position: absolute;
        z-index: ${theme.getZIndexOf('tableShadow')};
        transition: ${theme.animations.transitionMedium('all')};
        pointer-events: none;
      }
    }

    .ag-pinned-left-cols-container,
    .ag-pinned-left-header {
      &::before {
        inset: 0 -12px 0 100%;
        border-left: ${theme.primitives.colors.grey04} 1px solid;
      }

      &.ag-shadow-active {
        &::before {
          background: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 0%, rgba(217, 217, 217, 0) 100%);
        }
      }
    }

    .ag-pinned-right-cols-container,
    .ag-pinned-right-header {
      &::after {
        inset: 0 100% 0 -12px;
        border-right: ${theme.primitives.colors.grey04} 1px solid;
      }

      &.ag-shadow-active {
        &::after {
          background: linear-gradient(-90deg, rgba(0, 0, 0, 0.1) 0%, rgba(217, 217, 217, 0) 100%);
        }
      }
    }

    .ag-header {
      &::before {
        inset: 100% 0 -12px 0;
        border-top: ${theme.primitives.colors.grey04} 1px solid;
      }

      &.ag-shadow-active {
        &::before {
          background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(217, 217, 217, 0) 100%);
        }
      }
    }

    input.ag-input-field-input.ag-checkbox-input {
      appearance: none;
      cursor: pointer;
      position: relative;
      margin: 0;
      width: 18px;
      height: 18px;
      display: inline-block;
      border-radius: 3px;
      border: 2px solid ${theme.primitives.colors.grey05};
      background-color: white;

      -webkit-appearance: none;
      opacity: 1;
    }

    /* transition: ${theme.animations.transitionFast('border')}; */

    input.ag-input-field-input.ag-checkbox-input::after {
      content: '';
      position: absolute;
      width: 11px;
      height: 6px;
      border-bottom: 0 solid white;
      border-left: 0 solid white;
      transform: translate(0px, -1px) rotate(-45deg);
      pointer-events: none;
      top: 5px;
      left: 4px;
    }

    .ag-checked input.ag-input-field-input.ag-checkbox-input {
      border: none;
      background-color: ${theme.primitives.colors.grey08};
    }

    .ag-checked input.ag-input-field-input.ag-checkbox-input::after {
      border-bottom: 2px solid white;
      border-left: 2px solid white;
      content: '';
    }

    .ag-checkbox-input-wrapper::after {
      content: initial;
    }
  `
);
