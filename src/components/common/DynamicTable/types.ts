import { ColDef, SortDirection } from 'ag-grid-community';

export interface DynamicTableConfig {
  /**
   * Default columns used for sorting.
   * Only the first column here is used by default, and only in
   * case it's currently hidden, the next ones are attempted.
   * */
  defaultSorting?: Array<ColDef>;

  /**
   * Columns that are always there at left and right.
   * */
  permanentLeftColumns?: Array<ColDefWithMetadata>;
  permanentRightColumns?: Array<ColDefWithMetadata>;

  /**
   * Columns that don't depend on any downloaded data.
   * E.g.: Menus and selection checkboxes
   * */
  abstractColumnKeys?: Array<ColumnKey>;

  /**
   * Sets which view mode is set as default. In case the first view
   * mode is not present, the next one is taken.
   * */
  viewModesPrecedence?: Array<ViewKey>;

  /**
   * Sets the min width per column;
   * */
  columnsMinWidth?: Record<ColumnKey, number>;
}

export interface HeaderComponentParams {
  getColumnDisplayName?: (columnDef: ColDef, displayName: string) => HeaderTitle;
  onSortRequested?: (sort: SortDirection | undefined) => void;
}

export type HeaderTitle = HeaderTitleSimple | HeaderTitleComposed;
export type HeaderTitleSimple = string;
export type HeaderTitleComposed = { title: string; tooltip: string };

/**
 * Column definitions
 * Basically, stuff about how the columns should look
 * and function
 *
 * @example
    export const DEF_SELECT_COLUMN_DEF: ColDefWithMetadata = {
      field: 'select',
      pinned: 'left',
      lockPosition: true,
      headerName: '',
      maxWidth: 52,
      resizable: false,
      suppressAutoSize: true,
      headerComponent: YardsSelectAllCheckbox,
      metadata: {
        groupId: 'select',
        groupName: 'select',
        column: {
          type: ColumnType.DEFAULT,
        } as Column,
      },
    };
 */
export type ColDefWithMetadata<T = unknown> = Omit<ColDef, 'field'> & {
  field: string;
  metadata: T & ColDefMetadata;
};

export interface ColDefMetadata {
  type: 'default' | string;
  column: Column;
  groupId?: string;
  groupName?: string;
  activeView?: ViewKey;
}

export interface Column {
  key: ColumnKey;
  views: Record<ViewKey, View>;
  type: string;
}

export interface View {
  key: ViewKey;
  sortableKey: string;
}
export type ColumnKey = string;
export type ViewKey = 'default' | string;
export interface RowViewValue {
  data: any;
}
export interface RowColumnData {
  views: Record<ViewKey, RowViewValue>;
}
export type RowData = Record<ColumnKey, RowColumnData>;
export interface DynamicTableRow<MetaType = object> {
  meta: MetaType & {
    id: number;
  };
  data: RowData;
}

export interface DynamicTableExternalQueryParams {
  columns: string[];
}

export interface DynamicTableInternalQueryParams extends DynamicTableExternalQueryParams {
  limit: number;
  offset: number;
}

export enum StaticCellFieldDefaultType {
  SELECT = 'select',
  FILL_TABLE_RIGHT = 'fillTableRight',
  MENU = 'menu',
}

export interface StaticCellRenderParams<MetaDataType = object> {
  field: string;
  gridParamData: DynamicTableRow<MetaDataType>;
}
export interface CellRenderParams<DataType = any, IdType = any, FieldType = any> {
  field: FieldType;
  data: DataType;
  column: Column;
  meta: any;
  view: View;
  id: IdType | undefined;
}

export interface DynamicTableProps {
  headerComponentParams?: HeaderComponentParams;
  /**
   *
   * Used to render static content like menus or checkboxes, for which
   * an initial loading state doesn't make sense
   */
  renderStaticCell?: (staticCellRenderParams: StaticCellRenderParams) => React.JSX.Element | null;
  /**
   *
   * Function to be
   * called the assign the cellRender to a component.
   * Ideally this would be a switch statement
   * Default should be undefined, since the default is already provided her by the table
   */
  renderDynamicCell?: (cellRenderParams: CellRenderParams) => React.JSX.Element | null;
  /**
   * Will be spread into the default-defaultColDef object (sorry so many 'defaults')
   * Include any values you want to override, like renderCell if you want full control
   */
  defaultColDef?: ColDef;
  /**
   * To be able to disable table interaction while data is loading
   * Default is true
   * currently has to be handled outside of the dynamic table, but maybe later will
   * be handled inside
   */
  enableTableInteraction?: boolean;

  paginationOptions?: {
    /**
     * Overrides the name of the URL query parameter used for pagination.
     * */
    pageParamName?: string;
  };

  /**
   * Sets the table preferences.
   * */
  config?: DynamicTableConfig;
}
