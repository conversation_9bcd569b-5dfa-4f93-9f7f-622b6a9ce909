import React, { useCallback, useEffect, useState } from 'react';
import { IHeaderParams } from 'ag-grid-community';

import { Box } from '@components/common/Box';
import { useDynamicTableHasLoaded, useDynamicTableSelection } from '@components/common/DynamicTable/hooks';
import { Checkbox } from '@components/form/deprecated/CheckBox';

export const SelectAllCheckbox: React.FC<IHeaderParams> = () => {
  const [isChecked, setIsChecked] = useState(false);

  const { selectAllOnPage, deselectAllOnPage, hasSelectedAllOnCurrentPage } = useDynamicTableSelection();

  const listHasLoaded = useDynamicTableHasLoaded();
  const handleOnChange = useCallback(
    (checked: boolean) => {
      setIsChecked(checked);
      if (checked) selectAllOnPage();
      else deselectAllOnPage();
    },
    [deselectAllOnPage, selectAllOnPage]
  );

  useEffect(() => {
    if (isChecked !== hasSelectedAllOnCurrentPage) {
      setIsChecked(hasSelectedAllOnCurrentPage);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasSelectedAllOnCurrentPage]);

  if (!listHasLoaded) return null;

  return (
    <Box marginLeft_100>
      <Checkbox value={isChecked} name={`select-all-rows}`} onChange={handleOnChange} />
    </Box>
  );
};
