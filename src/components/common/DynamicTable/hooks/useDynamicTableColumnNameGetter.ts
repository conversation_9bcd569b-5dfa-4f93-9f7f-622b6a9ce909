import { useCallback } from 'react';
import { ColDef } from 'ag-grid-community';

import { HeaderComponentParams } from '@components/common/DynamicTable/types';
import { CaseAdapter } from '@helpers/CaseAdapter';
import { useTranslation } from '@hooks/useTranslation';

/**
 * 
 * @example
    const columnNameGetter = useDynamicTableColumnNameGetter('member_list_');
    // Field name: 'lastName'

    // src/translations/common/index.ts

    tool.add('member_list_last_name', {
        en: 'Last Name',
    });
 *
 */
export function useDynamicTableColumnNameGetter(prefix: string = ''): HeaderComponentParams['getColumnDisplayName'] {
  const t = useTranslation();

  return useCallback(
    (columnDef: ColDef, defaultDisplayName: string) => {
      const translationKey = `${prefix}${CaseAdapter.toSnakeCase(columnDef.field ?? '')}`;
      switch (columnDef.field) {
        case 'menu':
          return '';

        default:
          if (translationKey === t(translationKey)) return defaultDisplayName;
          return t(translationKey);
      }
    },
    [prefix, t]
  );
}
