import { useCallback } from 'react';
import { GridApi } from 'ag-grid-community';

import { useDynamicTableUtil } from '@components/common/DynamicTable/hooks/useDynamicTableUtil';
import { DynamicTableConfig } from '@components/common/DynamicTable/types';

import { useDynamicTableDataCycle } from './useDynamicTableDataCycle';

export function useDynamicTablePaginationHelper(options?: { pageParamName?: string; config?: DynamicTableConfig }) {
  const tableUtil = useDynamicTableUtil(options?.config);

  const applyPageFromURL = useCallback(
    (gridApi: GridApi) => {
      const initialPage = tableUtil.getPageFromURLParams(options);
      gridApi.paginationGoToPage(initialPage);
    },
    [options, tableUtil]
  );

  const updatePageToURL = useCallback(
    (gridApi: GridApi) => {
      const currentPage = gridApi.paginationGetCurrentPage() ?? 0;
      tableUtil.setPageToURLParams({ page: currentPage, ...options });
    },
    [options, tableUtil]
  );

  /**
   * If there's only 1 page, disable pagination
   * (hide pagination footer)
   */
  const togglePagination = useCallback((gridApi: GridApi) => {
    const pages = gridApi.paginationGetTotalPages();
    gridApi.setPagination(pages > 1);
  }, []);

  const onDataServerLoad = useCallback(
    (gridApi: GridApi) => {
      applyPageFromURL(gridApi);
      togglePagination(gridApi);
    },
    [applyPageFromURL, togglePagination]
  );

  const onDataServerReload = useCallback(
    (gridApi: GridApi) => {
      updatePageToURL(gridApi);
      togglePagination(gridApi);
    },
    [togglePagination, updatePageToURL]
  );

  useDynamicTableDataCycle({
    onDataServerLoad,
    onDataServerReload,
  });
}
