import React, { Fragment, useCallback, useMemo } from 'react';
import { useTheme } from 'styled-components';

import { MarkerCluster as MarkerClusterIcon } from '@components/common/Icon/dynamic/MarkerCluster';
import { Marker } from '@components/common/Map';
import { useMap } from '@components/common/Map/hooks';
import {
  MapElementCluster,
  MapElementDescriptor,
  MapElementsOptimizerProps,
  MarkerProps,
} from '@components/common/Map/types';
import { MapClusterer, MapSmartBounds } from '@helpers/MapOptimizer';

const CLUSTER_FITTING_PADDING = 192;
const CLUSTER_ICON_SIZE = 32;

export const MapElementsOptimizer: React.FC<MapElementsOptimizerProps> = ({
  debug,
  enableClustering,
  enableSmartBounds,
  elementClusterProps,
  minZoomToRender,
  maxZoomToRender,
  maxZoomToCluster,
  elements,
  elementRenderer: propElementRenderer,
  elementClusterRenderer: propElementClusterRenderer,
}) => {
  const map = useMap();
  const mapBounds = map.state.bounds;
  const mapZoom = map.state.zoom;
  const shouldRender =
    (minZoomToRender === undefined || mapZoom >= minZoomToRender) &&
    (maxZoomToRender === undefined || mapZoom <= maxZoomToRender);

  if (!!enableClustering === !!enableSmartBounds) {
    console.warn(
      `MapElementsOptimizer: You must set or enableClustering or enableSmartBounds for the MapElementsOptimizer to work properly.`
    );
  }

  const smartBoundsHelper = useMemo(() => {
    if (!enableSmartBounds) return null;
    return new MapSmartBounds(
      elements.map((data) => ({
        id: data.id,
        ...data.position,
        properties: { data },
        radius: data.radius,
      })),
      debug
    );
  }, [debug, elements, enableSmartBounds]);

  const clusteringHelper = useMemo(() => {
    if (!enableClustering) return null;
    const clusterOptions = maxZoomToCluster ? { maxZoom: maxZoomToCluster } : {};
    return new MapClusterer(
      elements.map((data) => ({
        id: data.id,
        ...data.position,
        properties: { data },
        radius: data.radius,
      })),
      clusterOptions,
      debug
    );
  }, [debug, elements, enableClustering, maxZoomToCluster]);

  const inBoundsElements = useMemo(() => {
    if (!smartBoundsHelper || !mapBounds) return [];
    const elementsMap = smartBoundsHelper.getVisibleElements(mapBounds);
    return Object.values(elementsMap).map((element) => element.properties.data);
  }, [mapBounds, smartBoundsHelper]);

  const clusteringData = useMemo(() => {
    if (!clusteringHelper || !mapBounds) return;
    const { elements, clusters } = clusteringHelper.getElementsAndClusters(mapBounds, mapZoom);
    return {
      elements: Object.values(elements).map((element) => element.properties.data),
      clusters: Object.values(clusters).map(({ id, lat, lng, elementCount, getElements }) => ({
        id,
        position: { lat, lng },
        elementCount,
        getElements: () => Object.values(getElements()).map((element) => element.properties.data),
      })),
    };
  }, [clusteringHelper, mapBounds, mapZoom]);

  const elementRenderer = useCallback(
    (element: MapElementDescriptor) => {
      if (propElementRenderer) {
        return propElementRenderer(element);
      }
      return null;
    },
    [propElementRenderer]
  );

  const elementClusterRenderer = useCallback(
    (elementCluster: MapElementCluster) => {
      if (propElementClusterRenderer) {
        return propElementClusterRenderer(elementCluster);
      }

      return <ElementClusterCircle elementCluster={elementCluster} elementClusterProps={elementClusterProps} />;
    },
    [elementClusterProps, propElementClusterRenderer]
  );

  if (!shouldRender) return null;

  return (
    <>
      {inBoundsElements.map((element) => (
        <Fragment key={element.id}>{elementRenderer(element)}</Fragment>
      ))}

      {clusteringData?.elements.map((element) => (
        <Fragment key={element.id}>{elementRenderer(element)}</Fragment>
      ))}
      {clusteringData?.clusters.map((elementCluster) => (
        <Fragment key={elementCluster.id}>{elementClusterRenderer(elementCluster)}</Fragment>
      ))}
    </>
  );
};

export const ElementClusterCircle: React.FC<{
  elementCluster: MapElementCluster;
  elementClusterProps?: Partial<MarkerProps>;
}> = ({ elementCluster, elementClusterProps }) => {
  const map = useMap();
  const theme = useTheme();

  const onClusterClick = useCallback(() => {
    const clusteredPositions = elementCluster.getElements().map((data) => data.position);
    map.instance?.fitCoordinates(clusteredPositions, CLUSTER_FITTING_PADDING);
  }, [elementCluster, map.instance]);

  const { elementCount } = elementCluster;

  return (
    <Marker
      position={elementCluster.position}
      onClick={onClusterClick}
      icon={MarkerClusterIcon.getImageURI({ count: elementCount, theme, size: CLUSTER_ICON_SIZE })}
      {...elementClusterProps}
    />
  );
};
