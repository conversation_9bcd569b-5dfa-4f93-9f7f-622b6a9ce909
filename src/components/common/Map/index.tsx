/**
 * TODO: Move these components to ./components folder, and break into files.
 * */

import React, { ComponentType, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import satellite from '@assets/map/satellite.png';
import terrain from '@assets/map/terrain.png';
import { Box } from '@components/common/Box';
import { _FullScreen } from '@components/common/Icon/presets/_FullScreen';
import { _FullScreenExit } from '@components/common/Icon/presets/_FullScreenExit';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Close } from '@components/common/Icon/presets/Close';
import { Layers } from '@components/common/Icon/presets/Layers';
import { Location } from '@components/common/Icon/presets/Location';
import { ZoomIn } from '@components/common/Icon/presets/ZoomIn';
import { ZoomOut } from '@components/common/Icon/presets/ZoomOut';
import { MapApiProviders } from '@components/common/Map/apis';
import { DEF_MAX_ZOOM, DEF_MIN_ZOOM, DEF_PANE_WIDTH, DEFAULT_MAP_API } from '@components/common/Map/constants';
import { useMap, useMapChildren, useMapStyle, useMapType } from '@components/common/Map/hooks';
import {
  StyledMapControlButton,
  StyledMapControlButtonMinus,
  StyledMapControlButtonPlus,
  StyledMapControlWrapper,
  StyledMapOptionsCardHeader,
  StyledMapOptionsCardImage,
  StyledMapOptionsCardSection,
  StyledMapOptionsCardWrapper,
  StyledMapPane,
  StyledMapPaneDrawer,
  StyledMapPaneToggleButton,
  StyledMapPaneToggleIconWrapper,
  StyledMapPaneWrapper,
  StyledMapTypeOption,
  StyledMenuBase,
} from '@components/common/Map/styles';
import {
  MapApi,
  MapContextValue,
  MapControlMenuProps,
  MapControlsProps,
  MapInstance,
  MapPaneProps,
  MapProps,
  MapType,
  MarkerProps,
} from '@components/common/Map/types';
import { Text } from '@components/common/Text';
import { Tooltip } from '@components/common/Tooltip';
import { TooltipProps } from '@components/common/Tooltip/types';
import { Checkbox } from '@components/form/deprecated/CheckBox';
import { useFullScreenControl } from '@helpers/FullScreen/hooks';
import { useQueryParamsState } from '@helpers/QueryParams/hooks/useQueryParamState';

import { ResponsiveRender } from '../ResponsiveRender';

import { MapContext, MapContextProvider } from './context';

export * from './components';

const MAP_OPTIONS_BUTTON_ID = 'map-options-button';

/**
 * Main map component.
 * */
export const Map: React.FC<MapProps> = ({ children, onInstance, ...props }) => {
  const MapRenderer = MapApiProviders[props.api ?? DEFAULT_MAP_API].mapRenderer;

  const forwardOnInstance = useCallback(
    (context: MapContextValue) => {
      return (instance: MapInstance) => {
        context.__setInstance(instance);
        onInstance && onInstance(instance);
      };
    },
    [onInstance]
  );

  return (
    <MapContextProvider {...props}>
      <MapContext.Consumer>
        {(mapContext) =>
          mapContext ? (
            <MapRenderer
              state={mapContext.state}
              onStateChange={mapContext.setState}
              onInstance={forwardOnInstance(mapContext)}
              {...props}
            >
              {children}
            </MapRenderer>
          ) : null
        }
      </MapContext.Consumer>
    </MapContextProvider>
  );
};

/**
 * Injects the current map API without
 * unnecessary re-renders.
 * */
export function withMapApi<P>(Component: ComponentType<P & { api: MapApi }>): ComponentType<P> {
  return (props) => {
    const { api } = useMap();
    return useMemo(() => <Component api={api} {...props} />, [api, props]);
  };
}

/**
 * Renders any react content in a map coordinate.
 * */
export const Marker = withMapApi<MarkerProps>(({ api, children, ...props }) => {
  const MarkerRenderer = useMemo(() => MapApiProviders[api].markerRenderer, [api]);
  return <MarkerRenderer {...props}>{children}</MarkerRenderer>;
});

/**
 * Renders a maker info window.
 * */
export const InfoWindow: React.FCWithChildren = ({ children }) => {
  return (
    <Tooltip appendToTarget placement="top" interactive arrow light>
      {children}
    </Tooltip>
  );
};

const satelliteMapActivatedByMapType = {
  [MapType.TERRAIN]: MapType.SATELLITE,
  [MapType.TERRAIN_SIMPLIFIED]: MapType.SATELLITE_SIMPLIFIED,
};
const terrainMapActivatedByMapType = {
  [MapType.SATELLITE]: MapType.TERRAIN,
  [MapType.SATELLITE_SIMPLIFIED]: MapType.TERRAIN_SIMPLIFIED,
};

const streetMapToggleOnByMapType = {
  [MapType.SATELLITE_SIMPLIFIED]: MapType.SATELLITE,
  [MapType.TERRAIN_SIMPLIFIED]: MapType.TERRAIN,
};
const streetMapToggleOffByMapType = {
  [MapType.SATELLITE]: MapType.SATELLITE_SIMPLIFIED,
  [MapType.TERRAIN]: MapType.TERRAIN_SIMPLIFIED,
};

const MapControlMenu: React.FC<MapControlMenuProps> = ({ extraMenuItems, target, mapContainerElement }) => {
  const { t } = useTranslation();
  const { setState } = useMap();
  const { isSatellite, isTerrain, hasStreetNames } = useMapType();

  const mapTypeItems = useMemo(
    () => [
      {
        img: satellite,
        text: 'satellite',
        isSelected: isSatellite,
        onClick: () =>
          setState((curr) => ({ ...curr, mapType: satelliteMapActivatedByMapType[curr.mapType] ?? curr.mapType })),
      },
      {
        img: terrain,
        text: 'terrain',
        isSelected: isTerrain,
        onClick: () =>
          setState((curr) => ({ ...curr, mapType: terrainMapActivatedByMapType[curr.mapType] ?? curr.mapType })),
      },
    ],
    [isSatellite, isTerrain, setState]
  );

  const menuItems = useMemo(() => {
    return (extraMenuItems ?? []).concat([
      {
        title: 'map_street_names',
        checked: hasStreetNames,
        onChange: (value) =>
          setState((curr) => ({
            ...curr,
            mapType: value ? streetMapToggleOnByMapType[curr.mapType] : streetMapToggleOffByMapType[curr.mapType],
          })),
      },
    ]);
  }, [extraMenuItems, hasStreetNames, setState]);

  const renderHeader = useCallback(
    (closeMenu: () => void) => (
      <>
        <ResponsiveRender from={'tablet'}>
          <StyledMapOptionsCardHeader onClick={closeMenu}>
            <Text typography={'Heading3'} weight={'600'}>
              {t('map_details')}
            </Text>
            <Close size={16} color={'grey08'} />
          </StyledMapOptionsCardHeader>
        </ResponsiveRender>
        <ResponsiveRender until={'phone'}>
          <StyledMapOptionsCardSection>
            <Text typography={'Heading3'} weight={'600'}>
              {t('map_details')}
            </Text>
          </StyledMapOptionsCardSection>
        </ResponsiveRender>
      </>
    ),
    [t]
  );

  const renderChildren = useCallback(
    (closeMenu: () => void) => {
      return (
        <StyledMapOptionsCardWrapper>
          {renderHeader(closeMenu)}
          <StyledMapOptionsCardSection column gap_050>
            {menuItems.map(({ title, onChange, checked }) => (
              <Box key={title} gap_050 alignItems={'center'}>
                <Checkbox value={checked} id={title} name={title} onChange={onChange}></Checkbox>
                <Box tag="label" htmlFor={title}>
                  <Text typography="SmallParagraph">{t(title)}</Text>
                </Box>
              </Box>
            ))}
          </StyledMapOptionsCardSection>
          <StyledMapOptionsCardSection>
            <Text typography={'Heading3'} weight={'600'} align="left">
              {t('map_type')}
            </Text>
          </StyledMapOptionsCardSection>
          <StyledMapOptionsCardSection gap_100 $hideBorder={true}>
            {mapTypeItems.map(({ img, text, onClick, isSelected }) => (
              <StyledMapTypeOption key={text} column alignItems={'center'} onClick={onClick} $isClickable>
                <StyledMapOptionsCardImage src={img} $isSelected={isSelected} />
                <Text typography="SmallParagraph" color={isSelected ? 'grey08' : 'grey06'}>
                  {t(text)}
                </Text>
              </StyledMapTypeOption>
            ))}
          </StyledMapOptionsCardSection>
        </StyledMapOptionsCardWrapper>
      );
    },
    [menuItems, t, mapTypeItems, renderHeader]
  );

  return (
    <StyledMenuBase
      target={target}
      placement="top-start"
      hideOnClick={false}
      trigger="click"
      renderChildren={renderChildren}
      appendTo={mapContainerElement}
    />
  );
};

/**
 * Renders the zoom and map type controls.
 * Also accept extra menu options.
 * */
export const MapControls: React.FCWithChildren<MapControlsProps> = ({
  controls,
  placement,
  orientation,
  mapTypeAndDetailsExtraItems,
  mapContainerElement,
  onRecenterMapClick,
  children,
}) => {
  const { t } = useTranslation();

  const { setState } = useMap();

  const { canToggleFullScreen, isFullScreen, toggleFullScreen } = useFullScreenControl({
    target: mapContainerElement,
  });

  const zoom = useCallback(
    (by: number) =>
      setState((curr) => ({ ...curr, zoom: Math.max(DEF_MIN_ZOOM, Math.min(DEF_MAX_ZOOM, curr.zoom + by)) })),
    [setState]
  );

  const zoomIn = useCallback(() => zoom(1), [zoom]);
  const zoomOut = useCallback(() => zoom(-1), [zoom]);

  const iconButtonCommonProps = {
    ...useMapStyle().buttonProps,
    isVisible: true,
    iconOnly: true,
  };

  const tooltipsCommonProps: TooltipProps = {
    placement: ['column'].includes(orientation as string) ? 'left' : 'bottom',
    appendTo: mapContainerElement,
  };

  return (
    <StyledMapControlWrapper $orientation={orientation} $placement={placement} gap_050>
      {children}

      {controls?.includes('fullscreen') && canToggleFullScreen && (
        <StyledMapControlButton
          {...iconButtonCommonProps}
          aria-label={t(isFullScreen ? 'exit_fullscreen' : 'enter_fullscreen')}
          onClick={toggleFullScreen}
        >
          {isFullScreen ? <_FullScreenExit size={24} /> : <_FullScreen size={24} />}
          <Tooltip {...tooltipsCommonProps}>
            <Text typography="CaptionSmall">{t(isFullScreen ? 'exit_fullscreen' : 'enter_fullscreen')}</Text>
          </Tooltip>
        </StyledMapControlButton>
      )}

      {controls?.includes('recenter') && (
        <StyledMapControlButton
          {...iconButtonCommonProps}
          isVisible={!!onRecenterMapClick}
          onClick={onRecenterMapClick}
          aria-label={t('map_recenter')}
        >
          <Location size={24} />
          <Tooltip {...tooltipsCommonProps}>
            <Text typography="CaptionSmall">{t('map_recenter')}</Text>
          </Tooltip>
        </StyledMapControlButton>
      )}

      {controls?.includes('mapTypeAndDetails') && (
        <StyledMapControlButton
          {...iconButtonCommonProps}
          id={MAP_OPTIONS_BUTTON_ID}
          aria-label={t('map_change_options')}
        >
          <Layers size={24} />
          <MapControlMenu
            target={MAP_OPTIONS_BUTTON_ID}
            extraMenuItems={mapTypeAndDetailsExtraItems}
            mapContainerElement={mapContainerElement}
          />
          <Tooltip {...tooltipsCommonProps}>
            <Text typography="CaptionSmall">{t('map_change_options')}</Text>
          </Tooltip>
        </StyledMapControlButton>
      )}

      {controls?.includes('zoom') && (
        <Box column>
          <StyledMapControlButtonPlus {...iconButtonCommonProps} onClick={zoomIn} aria-label={t('zoom-in')}>
            <ZoomIn size={24} />
          </StyledMapControlButtonPlus>
          <StyledMapControlButtonMinus {...iconButtonCommonProps} onClick={zoomOut} aria-label={t('zoom-out')}>
            <ZoomOut size={24} />
          </StyledMapControlButtonMinus>
        </Box>
      )}
    </StyledMapControlWrapper>
  );
};

export const MapPane: React.FC<MapPaneProps> = ({
  initiallyExpanded,
  expanded: propExpanded,
  onToggled: propOnExpanded,
  width: propWidth,
  contractMapToExpand,
  hideToggle,
  scrollable,
  useURLState,
  children,
  ...props
}) => {
  const [expandedMemState, setExpandedMemState] = useState({ pane: initiallyExpanded ? 'expanded' : 'collapsed' });
  const [expandedURLState, setExpandedURLState] = useQueryParamsState({
    pane: initiallyExpanded ? 'expanded' : 'collapsed',
  });

  const [expandedState, setExpandedState] = useMemo(
    () => (useURLState ? [expandedURLState, setExpandedURLState] : [expandedMemState, setExpandedMemState]),
    [expandedMemState, expandedURLState, setExpandedURLState, useURLState]
  );

  const isExpanded = useMemo(() => expandedState.pane === 'expanded', [expandedState.pane]);
  const width = propWidth ?? DEF_PANE_WIDTH;
  const panelLeft = useMemo(() => (isExpanded ? 0 : -width), [width, isExpanded]);
  const contentLeft = useMemo(
    () => (isExpanded && contractMapToExpand ? width : 0),
    [contractMapToExpand, isExpanded, width]
  );
  const toggleLeft = useMemo(
    () => (isExpanded && !contractMapToExpand ? width : 0),
    [contractMapToExpand, isExpanded, width]
  );

  const [map, ...elements] = useMapChildren(children);

  const toggle = useCallback(
    () => setExpandedState((curr) => ({ pane: curr.pane === 'expanded' ? 'collapsed' : 'expanded' })),
    [setExpandedState]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => propOnExpanded && propOnExpanded(isExpanded), [isExpanded]);
  useEffect(
    () =>
      typeof propExpanded !== 'undefined'
        ? setExpandedState(() => ({ pane: propExpanded ? 'expanded' : 'collapsed' }))
        : void 0,
    [propExpanded, setExpandedState]
  );

  return (
    <MapContextProvider>
      <StyledMapPane {...props}>
        <StyledMapPaneDrawer left={panelLeft} width={width} scrollable={scrollable}>
          {elements}
        </StyledMapPaneDrawer>
        <StyledMapPaneWrapper left={contentLeft}>
          {map}
          <StyledMapPaneToggleButton
            suppressPadding
            onClick={toggle}
            disabled={hideToggle}
            isVisible={!hideToggle}
            left={toggleLeft}
          >
            <StyledMapPaneToggleIconWrapper open={isExpanded}>
              <ChevronRight size={24} color={'white'} />
            </StyledMapPaneToggleIconWrapper>
          </StyledMapPaneToggleButton>
        </StyledMapPaneWrapper>
      </StyledMapPane>
    </MapContextProvider>
  );
};
