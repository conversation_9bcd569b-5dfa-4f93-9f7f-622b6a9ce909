/**
 * TODO: Move everything to ./components/styles.
 * */

import styled, { css, PrefixedStyledProps } from 'styled-components';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { MapControlsProps } from '@components/common/Map/types';
import { from, until } from '@style/mediaQueries';

import { MenuBase } from '../Menu';

export const StyledMapControlWrapper = styled(Box)<
  PrefixedStyledProps<Pick<MapControlsProps, 'placement' | 'orientation'>>
>(
  ({ theme, $placement, $orientation }) => css`
    position: absolute;
    flex-direction: ${$orientation ?? 'unset'};
    align-items: center;

    ${$placement === 'top-right'
      ? `
          top: ${theme.spacing._100};
          right: ${theme.spacing._100};
        `
      : `
          bottom: 2rem;
          right: ${theme.spacing._100};
        `}
  `
);

export const StyledMapControlButtonBase = styled(Button).attrs({ type: 'button' })``;

export const StyledMapControlButtonPlus = styled(StyledMapControlButtonBase)(
  ({ theme }) => `
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  border-bottom: 1px solid ${theme.primitives.colors.grey07};
`
);

export const StyledMapControlButtonMinus = styled(StyledMapControlButtonBase)(
  () => `
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
`
);

export const StyledMapControlButton = styled(StyledMapControlButtonBase)<{ isVisible: boolean }>(
  ({ theme, isVisible }) => `
  transform: scale(${isVisible ? 1 : 0});
  transition: ${theme.animations.transitionMedium('transform')};
`
);

export const StyledMenuBase = styled(MenuBase)(
  ({ theme }) => `
  border-radius: ${theme.shape.paperRadius02}px;

  ${until('phone')} {
    padding: 0 0 ${theme.spacing._100} 0;
  }
`
);

export const StyledMapOptionsCardWrapper = styled(Box)(
  ({ theme }) => `
  transition: ${theme.animations.transitionFast('opacity')};
  display: inline-block;
`
);

const MapOptionsSectionHeader = css(
  ({ theme }) => `
  position: relative;
  padding: ${theme.spacing._100};

  &::before {
    content: '';
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: 0px;
    border-bottom: 1px solid ${theme.primitives.colors.grey04};
  }
`
);

export const StyledMapOptionsCardHeader = styled.button(
  ({ theme }) => css`
    ${MapOptionsSectionHeader}
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    ${from('desktopSM')} {
      &:hover {
        color: ${theme.primitives.colors.grey06};
        path {
          fill: currentColor;
        }
      }
    }
  `
);

export const StyledMapOptionsCardSection = styled(Box)<{ $hideBorder?: boolean }>(
  ({ $hideBorder }) => css`
    ${MapOptionsSectionHeader}

    align-items: left;
    justify-content: left;

    &::before {
      opacity: ${$hideBorder ? 0 : 1};
    }
  `
);

export const StyledMapTypeOption = styled(Box)<{ $isClickable: boolean }>(
  ({ $isClickable }) => `
  cursor: ${$isClickable ? 'pointer' : 'default'};
`
);

export const StyledMapOptionsCardImage = styled.img<{ $isSelected: boolean }>(
  ({ theme, $isSelected }) => `
  cursor: pointer;
  width: 89px;
  height: 56px;
  border-radius: ${theme.borderRadius._050};
  border: 1px solid ${$isSelected ? theme.primitives.colors.focus02 : theme.primitives.colors.grey05};
`
);

export const StyledMapPane = styled.div(
  () => `
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
`
);

export const StyledMapPaneDrawer = styled.div<{ left: number; width: number; scrollable?: boolean }>(
  ({ theme, left, width, scrollable }) => `
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden ${scrollable ? 'auto' : 'hidden'};
  top: 0;
  bottom: 0;
  left: ${left}px;
  width: ${width}px;
  transition: ${theme.animations.transitionFast('left')};
  background-color: ${theme.primitives.colors.white};
  border-top: 1px solid ${theme.primitives.colors.grey04};
  z-index: ${theme.getZIndexOf('content')};
`
);

export const StyledMapPaneWrapper = styled.div<{ left: number }>(
  ({ theme, left }) => `
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: ${left}px;
  transition: ${theme.animations.transitionFast('left')};
  z-index: ${theme.getZIndexOf('base')};
`
);

export const StyledMapPaneToggleButton = styled(StyledMapControlButton)<{ isVisible: boolean; left: number }>(
  ({ theme, isVisible, left }) => `
  position: absolute;
  bottom: 2rem;
  left: ${theme.spacing._100};
  transition: ${theme.animations.transitionFast('transform')};
  opacity: ${isVisible ? 1 : 0};
  transform: translateX(${left}px);
  background-color: ${theme.colors.contentPrimary};
  padding: ${theme.spacing._025};
`
);

export const StyledMapPaneToggleIconWrapper = styled.div<{ open: boolean }>(
  ({ theme, open }) => `
  transition: ${theme.animations.transitionFast('transform')};
  transform: rotate(${open ? 180 : 0}deg);
`
);
