import React, { ComponentType, useEffect, useMemo, useState } from 'react';

import { Loading } from '@components/common/Loading';
import {
  DEFAULT_GOOGLE_MAPS_API_OPTIONS,
  DEFAULT_GOOGLE_MAPS_RETRY_INTERVAL,
} from '@components/common/Map/apis/GoogleMaps/constants';
import { Loader, LoaderOptions } from '@googlemaps/js-api-loader';

export interface GoogleMapsContextValue {
  google: typeof google;
  map: google.maps.Map | null;
  setMap: (map: google.maps.Map | null) => void;
}

export const GoogleMapsContext = React.createContext<GoogleMapsContextValue | null>(null);

export const GoogleApiWrapper = <P extends object>(options: LoaderOptions) => {
  return (Comp: ComponentType<P>) => {
    return (props: P & { google: typeof google }) => {
      // eslint-disable-next-line
      const [loading, setLoading] = useState(true);

      // Injects the map script.
      // eslint-disable-next-line
      useEffect(() => {
        let timeout: any = null;
        const load = async () => {
          try {
            // noinspection JSDeprecatedSymbols
            await new Loader(options).load();
            setLoading(false);
          } catch (_) {
            console.warn("Can't load google maps. Retrying...");
            timeout = setTimeout(load, DEFAULT_GOOGLE_MAPS_RETRY_INTERVAL);
          }
        };
        load().then(null);
        return () => {
          clearTimeout(timeout);
        };
      }, []);

      if (loading) {
        return <Loading />;
      }

      return <Comp {...props} google={google} />;
    };
  };
};

export const GoogleMapsContextProvider = GoogleApiWrapper<React.PropsWithChildren<GoogleMapsContextValue>>(
  DEFAULT_GOOGLE_MAPS_API_OPTIONS
)(({ google, children }) => {
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const contextValue = useMemo(() => ({ google, map, setMap }), [google, map]);
  return useMemo(
    () => <GoogleMapsContext.Provider value={contextValue}>{children}</GoogleMapsContext.Provider>,
    [children, contextValue]
  );
});
