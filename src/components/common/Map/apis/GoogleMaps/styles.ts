import styled, { css } from 'styled-components';

export const StyledMap = styled.div(
  () => `
  position: absolute;
  inset: 0;
  
  & > div:first-child {
    position: absolute;
    inset: 0;
  }
`
);

export const StyledPolygonTooltip = styled.div(
  ({ theme }) => `
  position: absolute;
  transform: translate(-100%, -50%);
  opacity: 0;
  pointer-events: none;
  transition: ${theme.animations.transitionFast('opacity')};
  z-index: ${theme.getZIndexOf('tooltip')};
`
);

export const StyledPolygonTooltipBackground = styled.div(
  ({ theme }) => `
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  border-radius: ${theme.shape.paperRadius01}px;
  background-color: ${theme.primitives.colors.grey08};
  opacity: 0.7;
`
);

export const StyledPolygonTooltipContent = styled.div(
  ({ theme }) => `
  position: relative;
  display: flex;
  align-items: center;
  color: ${theme.primitives.colors.white};
  padding: ${theme.spacing._050};
`
);

export const StyledSearchBoxDropdown = styled.div<{ visible: boolean }>(
  ({ theme, visible }) => `
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  font-size: 14px;
  font-weight: 400;
  font-family: ${theme.font.family};
  background-color: ${theme.primitives.colors.white};
  color: ${theme.primitives.colors.grey08};
  border-radius: ${theme.shape.paperRadius01}px;
  border-top: 1px solid ${theme.primitives.colors.grey04};
  z-index: ${theme.getZIndexOf('tooltip')};
  box-shadow: ${theme.shadows.boxShadow04};
  transition: ${theme.animations.transitionFast('opacity')};
  transform: translateY(${theme.spacing._025});
  opacity: ${visible ? 1 : 0};
  pointer-events: ${visible ? 'auto' : 'none'};
  
  strong {
    font-weight: 600;
  } 
  
  img {
    margin-left: auto;
    padding: ${theme.spacing._025};
  }
`
);

export const StyledSearchBoxDropdownItem = styled.button(
  ({ theme }) => `
  display: block;
  width: 100%;
  height: 48px;
  overflow: hidden;
  padding: 0 ${theme.spacing._050};
  border-radius: ${theme.shape.paperRadius01}px;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  & > * {
    margin: ${theme.spacing._050};
  }
  
  &:hover {
    color: ${theme.primitives.colors.grey06};
  }
  
  &:focus-visible {
    box-shadow: 0 0 4px 2px ${theme.primitives.colors.focus02};
  }
`
);

export const StyledMapError = styled.div(
  ({ theme }) => css`
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: ${theme.primitives.colors.background};
  `
);
