import { Color, PaperRadius } from 'styled-components';

import { BoxProps } from '@components/common/Box/types';

export interface LoadingSkeletonProps extends BaseProps {
  visible?: boolean;
  fitParent?: boolean;
  absolutelyFitParent?: boolean;
  enableNavbarMargin?: boolean;
}

export interface LoadingSkeletonRectProps extends LoadingSkeletonShapeProps, BaseProps {
  width?: string | number;
  height?: string | number;
  borderRadius?: PaperRadius;
}

export interface LoadingSkeletonCircleProps extends LoadingSkeletonShapeProps, BaseProps {
  size?: string | number;
}

export interface LoadingSkeletonShapeProps {
  backgroundColor?: Color;
}

type BaseProps = Omit<BoxProps<'div'>, 'children'>;
