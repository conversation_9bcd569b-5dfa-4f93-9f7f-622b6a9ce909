import styled, { css } from 'styled-components';

import { Button } from '@components/common/CTA';

export const StyledMediaItem = styled(Button).attrs({ suppressPadding: true })(
  ({ theme }) => css`
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: ${theme.borderRadius._050};
    transition: ${theme.animations.transitionFast('transform', 'background-color')};

    &:hover {
      transform: scale(0.9);
    }
  `
);

export const StyledIMG = styled.img(
  ({ theme }) => css`
    margin-top: ${theme.spacing._100};
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
  `
);
export const StyledAudio = styled.audio`
  width: 100%;
`;
