import { FC, useCallback, useMemo, useRef, useState } from 'react';

import { Box } from '@components/common/Box';
import { MusicalNote } from '@components/common/Icon/presets/MusicalNote.tsx';
import { StyledAudio, StyledIMG, StyledMediaItem } from '@components/common/MediaList/styles.ts';
import { Modal, ModalHeader } from '@components/common/Modal';
import { useDynamicModal } from '@components/common/ModalBase/hooks.tsx';
import { ModalContent } from '@components/common/ModalBase/ModalContent.tsx';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { ENDPOINTS } from '@config/api.ts';
import { Api } from '@helpers/Api';
import { Globals } from '@helpers/Globals';
import { URLUtil } from '@helpers/URL';
import { useTranslation } from '@hooks/useTranslation.ts';

import { AlertCard } from '../AlertCard';

export interface MediaListProps {
  media: Array<{ name: string }>;
}

export const MediaList: FC<MediaListProps> = ({ media }) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedAudio, setSelectedAudio] = useState<string | null>(null);
  const [hasErrorWithSelectedMedia, setHasErrorWithSelectedMedia] = useState(false);

  const imageRef = useRef<HTMLImageElement | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const t = useTranslation();
  const viewImageModal = useDynamicModal();
  const playAudioModal = useDynamicModal();

  const getFileURL = useCallback((name: string) => {
    return URLUtil.buildURL(ENDPOINTS.media, {
      name,
      'operation-id': Globals.operationId,
      token: Api.getAuthorizationToken(),
    });
  }, []);

  const openImage = useCallback(
    (name: string) => {
      setHasErrorWithSelectedMedia(false);
      setSelectedImage(name);
      viewImageModal.open();
    },
    [viewImageModal]
  );
  const openAudio = useCallback(
    (name: string) => {
      setHasErrorWithSelectedMedia(false);
      setSelectedAudio(name);
      playAudioModal.open();
    },
    [playAudioModal]
  );
  const isAudio = useCallback((name: string) => {
    const audioExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.wma', '.flac'];
    return audioExtensions.some((ext) => name.toLowerCase().endsWith(ext));
  }, []);

  const sortedMedia = useMemo(() => {
    return [...media].sort((a) => (isAudio(a.name) ? -1 : 1));
  }, [isAudio, media]);

  const handler = useCallback(() => setHasErrorWithSelectedMedia(true), [setHasErrorWithSelectedMedia]);
  const setAudioRef = useCallback(
    (node: HTMLAudioElement | null) => {
      if (audioRef.current) {
        audioRef.current.removeEventListener('error', handler);
      }
      if (node) {
        node.addEventListener('error', handler);
      }
      audioRef.current = node;
    },
    [handler]
  );
  const setImageRef = useCallback(
    (node: HTMLImageElement | null) => {
      if (imageRef.current) {
        imageRef.current.removeEventListener('error', handler);
      }
      if (node) {
        node.addEventListener('error', handler);
      }
      imageRef.current = node;
    },
    [handler]
  );

  const errorBanner = hasErrorWithSelectedMedia && (
    <AlertCard fullWidth marginBottom_050 error>
      {t('media_not_found')}
    </AlertCard>
  );

  return (
    <>
      <Box alignItems={'center'} gap_050 wrap>
        {sortedMedia.map((file, index) =>
          isAudio(file.name) ? (
            <StyledMediaItem
              aria-label={t('audio_media')}
              key={index}
              secondary
              iconOnly
              onClick={() => openAudio(file.name)}
            >
              <MusicalNote size={24} />
            </StyledMediaItem>
          ) : (
            <StyledMediaItem
              key={index}
              secondary
              onClick={() => openImage(file.name)}
              style={{
                background: `url("${getFileURL(file.name)}") center / cover`,
              }}
            />
          )
        )}
      </Box>
      <Modal width={'imageView'} {...viewImageModal.modalProps}>
        <ResponsiveRender from={'desktopSM'}>
          <ModalHeader title={t('view_media')} mobileTitle={t('view_media')} />
        </ResponsiveRender>
        <ResponsiveRender until={'tablet'}>
          <ModalHeader mobileTitle={t('view_media')} />
        </ResponsiveRender>
        <ModalContent>
          {errorBanner}
          <StyledIMG ref={setImageRef} src={selectedImage ? getFileURL(selectedImage) : undefined} />
        </ModalContent>
      </Modal>

      <Modal width={'small'} {...playAudioModal.modalProps}>
        <ResponsiveRender from={'desktopSM'}>
          <ModalHeader title={t('view_media')} mobileTitle={t('view_media')} />
        </ResponsiveRender>
        <ResponsiveRender until={'tablet'}>
          <ModalHeader mobileTitle={t('view_media')} />
        </ResponsiveRender>
        <ModalContent stretch>
          <ResponsiveRender from={'desktopSM'}>
            {errorBanner}
            <StyledAudio ref={setAudioRef} src={selectedAudio ? getFileURL(selectedAudio) : undefined} controls />
          </ResponsiveRender>

          <ResponsiveRender until={'tablet'}>
            <Box stretch paddingTop_100 paddingBottom_300>
              {errorBanner}
              <StyledAudio ref={setAudioRef} src={selectedAudio ? getFileURL(selectedAudio) : undefined} controls />
            </Box>
          </ResponsiveRender>
        </ModalContent>
      </Modal>
    </>
  );
};
