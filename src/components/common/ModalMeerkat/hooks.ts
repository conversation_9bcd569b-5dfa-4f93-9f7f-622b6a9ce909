import { useCallback, useLayoutEffect, useRef } from 'react';

import { DOM } from '@helpers/DOM';

/**
 * Which percentage of the modal height the user
 * has to swipe before dismissing the modal.
 * This will avoid accidental dismiss.
 * */
const SWIPE_TO_DISMISS_THRESHOLD = 0.3; // 30%;

export function useMeerkatDragToDismiss(options: { enabled: boolean; onDismissRequest?: () => void }) {
  const modalBodyRef = useRef<HTMLDivElement>(null);

  const translateModal = useCallback((delta: { x: number; y: number } | null) => {
    const modalBodyElement = modalBodyRef.current;
    if (!modalBodyElement) {
      return;
    }

    if (delta === null) {
      modalBodyElement.style.transform = '';
      modalBodyElement.style.transition = '';
      return;
    }

    const { x, y } = delta;
    modalBodyElement.style.transform = `translate(${x}px, ${y}px)`;
    modalBodyElement.style.transition = 'none';
  }, []);

  useLayoutEffect(() => {
    const modalBodyElement = modalBodyRef.current;
    if (!modalBodyElement || !options.enabled) {
      return;
    }

    const dragState = { dragging: false, startPos: { x: 0, y: 0 }, deltaY: 0 };

    const canSwipe = (e: TouchEvent) => {
      const scrollableElement = DOM.getNearestVerticallyScrollableParent(e.target as HTMLElement);
      return !scrollableElement?.scrollTop;
    };

    const canDismiss = () => {
      const modalHeight = modalBodyElement.getBoundingClientRect().height;
      return dragState.deltaY >= modalHeight * SWIPE_TO_DISMISS_THRESHOLD;
    };

    const getTouchPos = (e: TouchEvent) => {
      if (!e.touches.length) {
        return { x: 0, y: 0 };
      }
      const touch = e.touches[0];
      return { x: touch.pageX, y: touch.pageY };
    };

    const getSwipeDelta = (e: TouchEvent) => {
      const { x, y } = getTouchPos(e);
      const delta = { x: x - dragState.startPos.x, y: y - dragState.startPos.y };
      return { x: 0, y: Math.max(0, delta.y) };
    };

    const handleTouchStart = (e: TouchEvent) => {
      if (!canSwipe(e)) return;
      dragState.dragging = true;
      dragState.startPos = getTouchPos(e);
    };
    modalBodyElement.addEventListener('touchstart', handleTouchStart);

    const handleTouchMove = (e: TouchEvent) => {
      if (!dragState.dragging) return;

      const delta = getSwipeDelta(e);
      translateModal(delta);
      dragState.deltaY = delta.y;

      if (delta.y > 0) {
        e.stopPropagation();
        e.preventDefault();
      }
    };
    modalBodyElement.addEventListener('touchmove', handleTouchMove);

    const handleTouchEnd = () => {
      dragState.dragging = false;
      translateModal(null);
      if (canDismiss()) {
        options.onDismissRequest && options.onDismissRequest();
      }
    };
    modalBodyElement.addEventListener('touchend', handleTouchEnd);

    return () => {
      modalBodyElement.removeEventListener('touchstart', handleTouchStart);
      modalBodyElement.removeEventListener('touchmove', handleTouchMove);
      modalBodyElement.removeEventListener('touchend', handleTouchEnd);
    };
  }, [options, options.enabled, translateModal]);

  return { modalBodyRef };
}
