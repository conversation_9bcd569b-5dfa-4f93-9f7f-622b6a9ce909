import { css } from 'styled-components';
import styled from 'styled-components';

import { from } from '@style/mediaQueries';

import { Button } from '../CTA';

export const StyledAccordion = styled.div<{ $dark?: boolean }>(
  ({ theme, $dark }) => `
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background-color: ${$dark ? theme.colors.surfacePrimary : theme.primitives.colors.white};
  
`
);

const AccordionHeaderStyles = css(
  ({ theme }) => `
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: none;
  background: none;
  margin: ${theme.spacing._025} 10px;
  padding: 8px 0 8px 6px;

  &::before {
    content: '';
    position: absolute;
    
    left: 6px;
    right: 6px;
    bottom: -${theme.spacing._025};
    border-bottom: 1px solid currentColor;
    opacity: 0.15
    }

  `
);

export const StyledAccordionHeader = styled.div`
  ${AccordionHeaderStyles}
`;
export const StyledAccordionHeaderButton = styled.button(
  ({ theme }) => css`
    ${AccordionHeaderStyles}
    &:active {
      color: ${theme.primitives.colors.grey06};
      path {
        fill: currentColor;
      }
    }

    ${from('desktopSM')} {
      &:hover {
        color: ${theme.primitives.colors.grey06};
        path {
          fill: currentColor;
        }
      }
    }

    &:focus-visible {
      box-shadow: 0 0 4px 2px ${theme.primitives.colors.focus02};
      border-color: transparent;
      outline: none;
    }
  `
);

export const StyledAccordionHeaderIconWrapper = styled.span<{ expanded: boolean }>(
  ({ theme, expanded }) => `
  transition: ${theme.animations.transitionFast('transform')};
  transform: rotate(${expanded ? 180 : 0}deg) translateY(${expanded ? -2 : 0}px);
  padding: ${theme.spacing._025};
  display: block;
`
);

export const StyledAccordionContentHeader = styled.div<{ $expanded?: boolean }>(
  ({ theme, $expanded }) => css`
    margin: 0 0 ${theme.spacing._100};
    padding-bottom: ${theme.spacing._075};
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border: none;
    background: none;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      border-bottom: 1px solid ${theme.primitives.colors.grey04};
      opacity: ${$expanded ? 1 : 0};
      transition: ${theme.animations.transitionFast('opacity')};
      margin: 0 -${theme.spacing._100};
    }
  `
);

export const StyledAccordionContent = styled.div<{ height: number; expanded: boolean; showOverflow: boolean }>(
  ({ theme, height, expanded, showOverflow }) => `
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-sizing: content-box;
  height: ${expanded ? height : 0}px;
  margin: 0 ${theme.spacing._100};
  padding: ${expanded ? theme.spacing._100 : 0} 0;
  opacity: ${expanded ? 1 : 0};
  overflow: ${showOverflow ? 'visible' : 'hidden'};
  transition: 
    ${theme.animations.transitionMedium('height', 'padding', 'opacity')};
`
);

export const StyledAccordionContentInner = styled.div(
  () => `
  display: flex;
  flex-direction: column;
  align-items: stretch;
`
);

export const StyledAccordionFooter = styled.div<{ $expanded?: boolean }>(
  ({ theme, $expanded }) => css`
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    padding: ${theme.spacing._075} ${theme.spacing._150};
    align-items: center;
    gap: ${theme.spacing._100};
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      border-bottom: 1px solid currentColor;
      opacity: ${$expanded ? 0.15 : 0};
      transition: ${theme.animations.transitionFast('opacity')};
    }
  `
);

export const StyledFooterButton = styled(Button).attrs({ unstyled: true })(
  ({}) => css`
    display: flex;
    align-items: center;
  `
);
