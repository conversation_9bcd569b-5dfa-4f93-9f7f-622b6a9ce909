import { ReactElement } from 'react';

export interface AccordionProps {
  expanded?: boolean;
  initiallyExpanded?: boolean;
  onExpandedChange?: (expanded: boolean) => void;
  className?: string;
  /**
   * If Accordion should have a dark background
   */
  dark?: boolean;
  children:
    | [ReactElement<AccordionHeaderProps>, ReactElement<AccordionContentProps>]
    | [ReactElement<AccordionHeaderProps>, ReactElement<AccordionContentProps>, ReactElement<AccordionFooterProps>];
}

export interface AccordionHeaderProps {
  className?: string;
  /**
   * If the toggle should occur from the footer
   */
  suppressToggle?: boolean;
}

export interface AccordionContentProps {
  className?: string;
}
export interface AccordionFooterProps {
  expandedTitle: string;
  collapsedTitle: string;
  className?: string;
}

export interface AccordionContextValue {
  isExpanded: boolean;
  toggle: () => void;
  contentHeight: number;
  setContentHeight: (height: number) => void;
}
