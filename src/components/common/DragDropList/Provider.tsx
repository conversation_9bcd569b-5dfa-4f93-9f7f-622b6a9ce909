import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';

import { DragDropListContext } from '@components/common/DragDropList/context';
import {
  DragDropListContextValue,
  DragDropListProviderProps,
  DragEndEvent,
  ListCellSlotInfo,
} from '@components/common/DragDropList/types';

export const Provider: React.FCWithChildren<DragDropListProviderProps> = ({ onDragEnd, children }) => {
  const [isDragging, setDragging] = useState(false);
  const isDraggingStatelessRef = useRef(false);

  const slotsStatelessRef = useRef<Array<ListCellSlotInfo>>([]);

  const isDraggingStateless = useCallback(() => isDraggingStatelessRef.current, []);
  const slotsStateless = useCallback(() => slotsStatelessRef.current, []);

  const onDragEndStatelessRef = useRef(onDragEnd);
  const deferredActionsToNextRender = useRef<Array<() => void>>([]);

  const registerSlot = useCallback((slot) => {
    slotsStatelessRef.current.push(slot);
    return () => slotsStatelessRef.current.splice(slotsStatelessRef.current.indexOf(slot), 1);
  }, []);

  const notifyDragStart = useCallback(() => {
    setDragging(true);
    isDraggingStatelessRef.current = true;
  }, []);

  const notifyDragEnd = useCallback((event: DragEndEvent) => {
    setDragging(false);
    isDraggingStatelessRef.current = false;
    onDragEndStatelessRef.current?.(event);
  }, []);

  const deferredActionToNextRender = useCallback((action: () => void) => {
    deferredActionsToNextRender.current.push(action);
  }, []);

  const context = useMemo<DragDropListContextValue>(
    () => ({
      isDragging,
      isDraggingStateless,
      slotsStateless,
      registerSlot,
      notifyDragStart,
      notifyDragEnd,
      addDeferredActionToNextRender: deferredActionToNextRender,
    }),
    [
      isDragging,
      isDraggingStateless,
      slotsStateless,
      registerSlot,
      notifyDragStart,
      notifyDragEnd,
      deferredActionToNextRender,
    ]
  );

  useEffect(() => {
    onDragEndStatelessRef.current = onDragEnd;
  }, [onDragEnd]);

  const consumeScheduledActions = useCallback(() => {
    deferredActionsToNextRender.current.forEach((func) => func());
    deferredActionsToNextRender.current = [];
  }, []);

  useLayoutEffect(() => {
    consumeScheduledActions();
  }, [consumeScheduledActions, isDragging]);

  return <DragDropListContext.Provider value={context}>{children}</DragDropListContext.Provider>;
};
