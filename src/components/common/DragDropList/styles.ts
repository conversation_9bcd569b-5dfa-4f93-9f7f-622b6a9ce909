import styled, { css } from 'styled-components';

import { Box } from '@components/common/Box';

export const StyledList = styled(Box)(
  ({ theme }) => css`
    flex-grow: 1;
    margin-right: -${theme.spacing._050};
    margin-bottom: -${theme.spacing._050};
  `
);

export const StyledListCell = styled(Box).attrs({
  relative: true,
  borderRadius_050: true,
  borderWidth: 1,
  backgroundColor: 'surfaceSecondary',
  padding_050: true,
})<{ isPositive?: boolean }>(
  ({ theme, isPositive }) => css`
    margin-right: ${theme.spacing._050};
    margin-bottom: ${theme.spacing._050};
    border-color: ${isPositive ? theme.colors.contentPositive : theme.colors.borderSecondary};

    &:hover {
      ${StyledListCellDragHandler} {
        opacity: 1;
      }
    }
  `
);

export const StyledListCellSlot = styled(Box).attrs({
  relative: true,
  borderRadius_050: true,
})(
  ({ theme }) => css`
    &:first-child:last-child {
      flex: 1;
    }

    transition: ${theme.animations.transitionFast(
      'background-color',
      'min-width',
      'min-height',
      'margin-right',
      'margin-bottom'
    )};
  `
);

export const StyledListCellDragHandler = styled(Box).attrs({
  padding_025: true,
  borderRadius_050: true,
  backgroundColor: 'surfaceSecondary',
})(
  ({ theme }) => css`
    position: absolute;
    bottom: 0;
    right: 0;

    opacity: 0;
    transition: ${theme.animations.transitionFast('opacity')};

    &:hover {
      cursor: grab;
    }
    &:active {
      cursor: grabbing;
    }
  `
);
