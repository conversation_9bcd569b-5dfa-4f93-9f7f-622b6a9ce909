import React, { useCallback, useContext, useImperativeHandle, useLayoutEffect, useRef } from 'react';
import { useTheme } from 'styled-components';

import { BoxRef } from '@components/common/Box/types';
import { DragDropListContext, DragDropListInnerContext } from '@components/common/DragDropList/context';
import { StyledListCellSlot } from '@components/common/DragDropList/styles';
import { ListCellSlotProps, ListCellSlotRef } from '@components/common/DragDropList/types';

export const ListCellSlot = React.forwardRef<ListCellSlotRef, React.PropsWithChildren<ListCellSlotProps>>(
  ({ index, children }, ref) => {
    const theme = useTheme();
    const dragDropContext = useContext(DragDropListContext);
    const dragDropInnerContext = useContext(DragDropListInnerContext);

    const enabled = useRef(true);
    const slotRef = useRef<BoxRef<'div'>>(null);

    const performLayoutChange = useCallback(
      (animator: (element: HTMLElement) => void, options: { animated: boolean }) => {
        const element = slotRef.current;
        if (!element) return;

        if (!options.animated) {
          element.style.transition = 'none';
          animator(element);
          setTimeout(() => {
            element.style.transition = '';
          }, theme.animations.durationFast);
        } else {
          animator(element);
        }
      },
      [theme.animations.durationFast]
    );

    useLayoutEffect(() => {
      const isDisabled =
        dragDropInnerContext?.disabledIndexes?.includes(index) ||
        (dragDropInnerContext?.enabledIndexes && !dragDropInnerContext.enabledIndexes.includes(index));
      if (isDisabled) return;

      return dragDropContext?.registerSlot({
        listId: dragDropInnerContext?.id,
        index,
        isEnabled: () => enabled.current,
        getLayout: () => {
          const rect = slotRef.current?.getBoundingClientRect();
          return { x: rect?.x ?? 0.0, y: rect?.y ?? 0.0, width: rect?.width ?? 0.0, height: rect?.height ?? 0.0 };
        },
        requestLayoutPreviewOn: (layout, options) => {
          performLayoutChange((element) => {
            element.style.minWidth = `${layout.width}px`;
            element.style.minHeight = `${layout.height}px`;
            element.style.marginRight = theme.spacing._050;
            element.style.marginBottom = theme.spacing._050;
            element.style.backgroundColor = theme.colors.surfaceDefault;
          }, options);
        },
        requestLayoutPreviewOff: (options) => {
          performLayoutChange((element) => {
            element.style.minWidth = `0px`;
            element.style.minHeight = `0px`;
            element.style.marginRight = `0px`;
            element.style.marginBottom = `0px`;
            element.style.backgroundColor = 'transparent';
          }, options);
        },
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
      dragDropContext?.registerSlot,
      dragDropInnerContext?.id,
      dragDropInnerContext?.disabledIndexes,
      index,
      performLayoutChange,
      theme.colors.surfaceDefault,
      theme.spacing._050,
    ]);

    useImperativeHandle(ref, () => ({
      listId: dragDropInnerContext?.id,
      index,
      enable: () => (enabled.current = true),
      disable: () => (enabled.current = false),
    }));

    return <StyledListCellSlot ref={slotRef}>{children}</StyledListCellSlot>;
  }
);
