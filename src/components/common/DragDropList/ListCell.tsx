import React, { useContext, useLayoutEffect, useRef } from 'react';
import { useTheme } from 'styled-components';

import { BoxRef } from '@components/common/Box/types';
import { DragDropListContext, DragDropListInnerContext } from '@components/common/DragDropList/context';
import { ListCellSlot } from '@components/common/DragDropList/ListCellSlot';
import { StyledListCell, StyledListCellDragHandler } from '@components/common/DragDropList/styles';
import { ListCellProps, ListCellSlotRef } from '@components/common/DragDropList/types';
import { DragDropListUtil } from '@components/common/DragDropList/util';
import { Drag } from '@components/common/Icon/presets/Drag';

export const ListCell: React.FCWithChildren<ListCellProps> = ({ index, children, isPositive, disableDragging }) => {
  const theme = useTheme();
  const dragDropContext = useContext(DragDropListContext);
  const dragDropInnerContext = useContext(DragDropListInnerContext);

  const slotRef = useRef<ListCellSlotRef>(null);
  const cellRef = useRef<BoxRef<'div'>>(null);
  const dragRef = useRef<BoxRef<'div'>>(null);

  useLayoutEffect(() => {
    const cellElement = cellRef.current;
    const dragElement = dragRef.current;
    const slotElement = slotRef.current;

    if (!cellElement || !dragElement || !slotElement || !dragDropContext || !dragDropInnerContext) return;

    return DragDropListUtil.bindDraggingEvents(
      index,
      cellElement,
      dragElement,
      slotElement,
      dragDropContext,
      dragDropInnerContext,
      (action) => dragDropContext.addDeferredActionToNextRender(action),
      theme
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    index,
    dragDropContext?.notifyDragStart,
    dragDropContext?.notifyDragEnd,
    dragDropInnerContext?.id,
    dragDropInnerContext?.orientation,
    theme.animations,
    theme.shadows.boxShadow03,
    theme.shadows.boxShadow04,
  ]);

  return (
    <>
      <StyledListCell ref={cellRef} style={{ position: 'relative', top: 0, left: 0 }} isPositive={isPositive}>
        {children}
        {!disableDragging && (
          <StyledListCellDragHandler ref={dragRef}>
            <Drag />
          </StyledListCellDragHandler>
        )}
      </StyledListCell>
      <ListCellSlot ref={slotRef} index={index + 1} />
    </>
  );
};
