import React, { useMemo } from 'react';

import { DragDropListInnerContext } from '@components/common/DragDropList/context';
import { ListCellSlot } from '@components/common/DragDropList/ListCellSlot';
import { StyledList } from '@components/common/DragDropList/styles';
import { DragDropListInnerContextValue, DragDropListProps } from '@components/common/DragDropList/types';

export const List: React.FCWithChildren<DragDropListProps> = ({
  id,
  orientation,
  enabledIndexes,
  disabledIndexes,
  children,
  firstSlotIndex = 0,
}) => {
  const context = useMemo<DragDropListInnerContextValue>(
    () => ({
      id,
      orientation,
      enabledIndexes,
      disabledIndexes,
    }),
    [id, orientation, disabledIndexes, enabledIndexes]
  );

  return (
    <DragDropListInnerContext.Provider value={context}>
      <StyledList column={orientation === 'vertical'}>
        <ListCellSlot index={firstSlotIndex} />
        {children}
      </StyledList>
    </DragDropListInnerContext.Provider>
  );
};
