import { minBy, throttle } from 'lodash';
import { DefaultTheme } from 'styled-components';

import {
  DragDropListContextValue,
  DragDropListInnerContextValue,
  ListCellSlotInfo,
  ListCellSlotRef,
} from '@components/common/DragDropList/types';
import { DOM } from '@helpers/DOM';
import { Geometry } from '@helpers/Geometry';

const AUTO_SCROLL_THROTTLING_MS = 1000 / 60;
const AUTO_SCROLL_THRESHOLD_PX = 128;
const AUTO_SCROLL_DOUBLE_THRESHOLD_PX = 42;
const AUTO_SCROLL_SINGLE_STEP_PX = 1.0;
const AUTO_SCROLL_DOUBLE_STEP_PX = 2.0;
const UPDATE_SLOTS_THROTTLING_MS = 80;

const DRAGGING_DEFAULT_STATE = {
  isDragging: false,
  autoScrollIntervalId: null as any,
  autoHorizontalScrollElement: null as HTMLElement | null,
  autoHorizontalScrollStep: 0,
  autoVerticalScrollElement: null as HTMLElement | null,
  autoVerticalScrollStep: 0,
  dragStartPos: { x: 0, y: 0 },
  dragCurrentPos: { x: 0, y: 0 },
  cellStartPost: { x: 0, y: 0 },
};

export const DragDropListUtil = {
  bindDraggingEvents,
};

function bindDraggingEvents(
  index: number,
  cellElement: HTMLElement,
  dragElement: HTMLElement,
  slotElement: ListCellSlotRef,
  dragDropContext: DragDropListContextValue,
  dragDropInnerContext: DragDropListInnerContextValue,
  deferActionToNextRender: (action: () => void) => void,
  theme: DefaultTheme
) {
  const state = { ...DRAGGING_DEFAULT_STATE };

  const handleDragStart = (e: MouseEvent) => {
    const shouldCatchClick = dragElement === e.target || dragElement.contains(e.target as Node);
    const isAlreadyDragging = dragDropContext?.isDraggingStateless?.();
    const shouldHandleDrag = shouldCatchClick && !isAlreadyDragging;

    if (!shouldHandleDrag) return;

    dragDropContext?.notifyDragStart();

    state.isDragging = true;
    state.dragStartPos = { x: e.pageX, y: e.pageY };
    state.dragCurrentPos = { x: e.pageX, y: e.pageY };

    slotElement.disable();

    const cellRect = cellElement.getBoundingClientRect();
    state.cellStartPost = { x: cellRect.x, y: cellRect.y };

    updateDragPosition();
    updateSlots({ animated: false });

    cellElement.style.boxShadow = theme.shadows.boxShadow03;
    cellElement.style.position = 'fixed';
    cellElement.style.transition = theme.animations.transitionMedium('box-shadow');
    cellElement.style.zIndex = `${Number.MAX_SAFE_INTEGER}`;

    scheduleAutoScroll();
    disableTextSelection();
  };

  const handleDragMove = (e: MouseEvent) => {
    if (!state.isDragging) return;

    state.dragCurrentPos = { x: e.pageX, y: e.pageY };

    updateScroll();
    updateDragPosition();
    updateSlotsThrottle();
  };

  const handleDragEnd = () => {
    if (!state.isDragging) return;
    state.isDragging = false;

    const nearest = getNearestSlot();
    const nearestRect = nearest.getLayout();

    slotElement.enable();
    clearAutoScrolls();
    enableTextSelection();

    cellElement.style.left = `${nearestRect.x}px`;
    cellElement.style.top = `${nearestRect.y}px`;
    cellElement.style.transition = theme.animations.transitionFast('box-shadow', 'top', 'left');

    setTimeout(() => {
      deferActionToNextRender(() => {
        updateSlotsOff();
        cellElement.style.boxShadow = 'none';
        cellElement.style.transition = theme.animations.transitionFast('box-shadow');
        cellElement.style.position = 'relative';
        cellElement.style.left = `0px`;
        cellElement.style.top = `0px`;
        cellElement.style.zIndex = `unset`;
      });

      const isMovingToSameList = dragDropInnerContext?.id === nearest.listId;
      const isMovingForward = index < nearest.index;
      const indexOffset = isMovingToSameList && isMovingForward ? -1 : 0;

      dragDropContext?.notifyDragEnd({
        fromListId: dragDropInnerContext?.id,
        fromIndex: index,
        toListId: nearest.listId,
        toIndex: nearest.index + indexOffset,
      });
    }, theme.animations.durationFast);
  };

  const updateDragPosition = () => {
    const dragDelta = {
      x: state.dragCurrentPos.x - state.dragStartPos.x,
      y: state.dragCurrentPos.y - state.dragStartPos.y,
    };
    const x = state.cellStartPost.x + dragDelta.x;
    const y = state.cellStartPost.y + dragDelta.y;
    cellElement.style.left = `${x}px`;
    cellElement.style.top = `${y}px`;
  };

  const updateSlotsThrottle = throttle(
    () => state.isDragging && updateSlots({ animated: true }),
    UPDATE_SLOTS_THROTTLING_MS
  );

  const updateSlots = (options: { animated: boolean }) => {
    const nearest = getNearestSlot();
    const cellRect = cellElement.getBoundingClientRect();
    dragDropContext?.slotsStateless().forEach((slot) => {
      if (slot === nearest) slot.requestLayoutPreviewOn(cellRect, options);
      else slot.requestLayoutPreviewOff(options);
    });
  };

  const updateSlotsOff = () => {
    dragDropContext?.slotsStateless().forEach((slot) => slot.requestLayoutPreviewOff({ animated: false }));
  };

  const updateScroll = throttle(() => {
    const cellRect = cellElement.getBoundingClientRect();
    const cellCenter = { x: cellRect.x + cellRect.width / 2, y: cellRect.y + cellRect.height / 2 };

    const hoveredElements = Array.from(document.elementsFromPoint(cellCenter.x, cellCenter.y));
    const element = hoveredElements.find((el) => !cellElement.contains(el)) as HTMLElement;

    const vScroll = DOM.getNearestVerticallyScrollableParent(element);
    const vScrollRect = vScroll?.getBoundingClientRect();

    if (vScroll && vScrollRect) {
      state.autoVerticalScrollElement = vScroll;

      const topOffset = cellCenter.y - vScrollRect.top;
      const bottomOffset = vScrollRect.bottom - cellCenter.y;

      if (topOffset < AUTO_SCROLL_DOUBLE_THRESHOLD_PX) {
        state.autoVerticalScrollStep = -AUTO_SCROLL_DOUBLE_STEP_PX;
      } else if (topOffset <= AUTO_SCROLL_THRESHOLD_PX) {
        state.autoVerticalScrollStep = -AUTO_SCROLL_SINGLE_STEP_PX;
      } else if (bottomOffset <= AUTO_SCROLL_DOUBLE_THRESHOLD_PX) {
        state.autoVerticalScrollStep = AUTO_SCROLL_DOUBLE_STEP_PX;
      } else if (bottomOffset <= AUTO_SCROLL_THRESHOLD_PX) {
        state.autoVerticalScrollStep = AUTO_SCROLL_SINGLE_STEP_PX;
      } else {
        state.autoVerticalScrollStep = 0;
      }
    }

    const hScroll = DOM.getNearestHorizontallyScrollableParent(element);
    const hScrollRect = hScroll?.getBoundingClientRect();

    if (hScroll && hScrollRect) {
      state.autoHorizontalScrollElement = hScroll;

      const leftOffset = cellCenter.x - hScrollRect.left;
      const rightOffset = hScrollRect.right - cellCenter.x;

      if (leftOffset < AUTO_SCROLL_DOUBLE_THRESHOLD_PX) {
        state.autoHorizontalScrollStep = -AUTO_SCROLL_DOUBLE_STEP_PX;
      } else if (leftOffset <= AUTO_SCROLL_THRESHOLD_PX) {
        state.autoHorizontalScrollStep = -AUTO_SCROLL_SINGLE_STEP_PX;
      } else if (rightOffset <= AUTO_SCROLL_DOUBLE_THRESHOLD_PX) {
        state.autoHorizontalScrollStep = AUTO_SCROLL_DOUBLE_STEP_PX;
      } else if (rightOffset <= AUTO_SCROLL_THRESHOLD_PX) {
        state.autoHorizontalScrollStep = AUTO_SCROLL_SINGLE_STEP_PX;
      } else {
        state.autoHorizontalScrollStep = 0;
      }
    }
  }, AUTO_SCROLL_THROTTLING_MS);

  const scheduleAutoScroll = () => {
    state.autoScrollIntervalId = setInterval(() => {
      if (state.autoVerticalScrollElement && state.autoVerticalScrollStep) {
        state.autoVerticalScrollElement.scrollBy({ top: state.autoVerticalScrollStep, behavior: 'instant' });
      }
      if (state.autoHorizontalScrollElement && state.autoHorizontalScrollStep) {
        state.autoHorizontalScrollElement.scrollBy({ left: state.autoHorizontalScrollStep, behavior: 'instant' });
      }

      updateSlots({ animated: true });
    });
  };

  const clearAutoScrolls = () => {
    clearInterval(state.autoScrollIntervalId);
  };

  const getNearestSlot = () => {
    const cellRect = cellElement.getBoundingClientRect();

    const getDistance = (slot: ListCellSlotInfo) => {
      const vMultiplier = dragDropInnerContext.orientation === 'horizontal' ? 4 : 1;
      const hMultiplier = dragDropInnerContext.orientation === 'horizontal' ? 1 : 4;

      const slotLayout = slot.getLayout();
      const slotLayoutWidth = Math.max(slotLayout.width, cellRect.width);
      const slotLayoutHeight = Math.max(slotLayout.height, cellRect.height);
      const slotCenter = { x: slotLayout.x + slotLayoutWidth / 2, y: slotLayout.y + slotLayoutHeight / 2 };

      const cursorPosition = {
        x: state.dragCurrentPos.x - cellRect.width / 2,
        y: state.dragCurrentPos.y - cellRect.height / 2,
      };

      return Geometry.getEuclideanDistance(
        cursorPosition.x * hMultiplier,
        cursorPosition.y * vMultiplier,
        slotCenter.x * hMultiplier,
        slotCenter.y * vMultiplier
      );
    };

    const enabledSlots = dragDropContext?.slotsStateless().filter((s) => s.isEnabled()) ?? [];

    return minBy(enabledSlots, getDistance)!;
  };

  const enableTextSelection = () => {
    document.body.style.userSelect = 'default';
  };

  const disableTextSelection = () => {
    document.body.style.userSelect = 'none';
  };

  document.addEventListener('mousedown', handleDragStart);
  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);

  return () => {
    handleDragEnd();

    document.removeEventListener('mousedown', handleDragStart);
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
  };
}
