export interface DragDropListContextValue {
  isDragging: boolean;
  isDraggingStateless: () => boolean;

  slotsStateless: () => Array<ListCellSlotInfo>;
  registerSlot: (slot: ListCellSlotInfo) => () => void;

  notifyDragStart: () => void;
  notifyDragEnd: (event: DragEndEvent) => void;

  addDeferredActionToNextRender: (action: () => void) => void;
}

export interface DragDropListInnerContextValue {
  id: any;
  orientation: ListOrientation;
  enabledIndexes?: Array<number>;
  disabledIndexes?: Array<number>;
}

export interface DragDropListProviderProps {
  onDragEnd: (event: DragEndEvent) => void;
}

export interface DragDropListProps {
  id: any;
  orientation: ListOrientation;
  enabledIndexes?: Array<number>;
  disabledIndexes?: Array<number>;
  firstSlotIndex?: number;
}

export interface ListCellProps {
  index: number;
  isPositive?: boolean;
  disableDragging?: boolean;
}

export interface ListCellSlotProps {
  index: number;
}

export interface ListCellSlotInfo {
  listId: any;
  index: number;
  isEnabled: () => boolean;
  getLayout: () => ListCellSlotLayout;
  requestLayoutPreviewOn: (layout: ListCellSlotLayout, options: { animated: boolean }) => void;
  requestLayoutPreviewOff: (options: { animated: boolean }) => void;
}

export interface ListCellSlotRef {
  listId: any;
  index: number;
  enable: () => void;
  disable: () => void;
}

export interface ListCellSlotLayout {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DragEndEvent {
  fromListId: any;
  fromIndex: number;
  toListId: any;
  toIndex: number;
}

export type ListOrientation = 'horizontal' | 'vertical';
