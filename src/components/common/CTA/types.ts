import React from 'react';
import { NavLinkProps } from 'react-router-dom';
import { PrefixedStyledProps } from 'styled-components';

export interface CTAProps {
  /**
   * Applies a "flex: 1" style
   * */
  fit?: boolean;
  /**
   * No button styling besides cursor-pointer, useful for icon buttons
   */
  unstyled?: boolean;
  /**
   * Include primary styles
   */
  primary?: boolean;
  /**
   * Include secondary styles
   */
  secondary?: boolean;
  /**
   * Include tertiary styles
   */
  tertiary?: boolean;
  /**
   * Include elevated styles
   */
  elevated?: boolean;
  /**
   * Include elevated styles
   */
  flat?: boolean;
  /**
   * Include shortcut styled
   * */
  shortcut?: boolean;

  /**
   * Applies an attention-like style to the button.
   * Use it for critical actions.
   * */
  danger?: boolean;

  /**
   * Include styles for an icon only button
   * */
  iconOnly?: boolean;

  /**
   * Include styles for an svg icon on the left
   */
  withLeadingIcon?: boolean;
  /**
   * Include styles for an svg icon on the right
   */
  withTrailingIcon?: boolean;
  /**
   * Suppress all padding
   */
  suppressPadding?: boolean;
  /**
   * Suppress top/bottom padding
   */
  suppressPaddingVertical?: boolean;
  /**
   * Suppress left/right padding
   */
  suppressPaddingHorizontal?: boolean;
  /**
   * Add margin left for easy spacing between buttons usually
   */
  withMarginLeft?: boolean;
  /**
   * Add margin right for easy spacing between buttons usually
   */
  withMarginRight?: boolean;
  /**
   * Add margin left & right for easy spacing between buttons usually
   */
  withMargin?: boolean;
  /**
   * Make the button take all available space and center the text inside
   */
  grow?: boolean;
}

/** Same as CTAProps but with a $ before each prop. */
export type StyledCTAProps = PrefixedStyledProps<CTAProps>;

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, CTAProps {}

export interface AnchorProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    Pick<React.ButtonHTMLAttributes<HTMLButtonElement>, 'disabled'>,
    CTAProps {}

export interface NavRouterLinkProps extends NavLinkProps, CTAProps {}
