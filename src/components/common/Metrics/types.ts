import React, { ReactElement } from 'react';

export interface MetricCarrouselProps {
  /** Enables the loading animation. */
  isFetching?: boolean;

  /** Used to persist the collapsed state. */
  storageKey?: string;

  /** Children cards. */
  children?: ReactElement<MetricCardProps> | Array<ReactElement<MetricCardProps>>;
}

export interface MetricCardProps {
  title: string;
  tooltip?: string;

  /** Enables the loading animation. */
  isFetching?: boolean;

  onClick?: React.HTMLAttributes<HTMLButtonElement>['onClick'];
}

export interface MetricCardSimpleValueProps {
  currentValue: string;
  previousValue?: string;
  trendValue?: string;
  trendDown?: boolean;
  trendDanger?: boolean;
}
