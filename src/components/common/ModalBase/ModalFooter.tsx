import React from 'react';

import { Box } from '@components/common/Box';
import { useWindowMatchMQ } from '@hooks/useWindowMatchMedia';
import { until } from '@style/mediaQueries';

import { MODAL_BP_1 } from '../Modal/constants';

import { ModalFooterAcceptButton } from './ModalFooterAcceptButton';
import { ModalFooterRejectButton } from './ModalFooterRejectButton';
import { StyledModalFooter } from './styles';
import { ModalFooterProps } from './types';

export const ModalFooter: React.FCWithChildren<ModalFooterProps> = ({
  acceptText,
  rejectText,
  acceptButtonProps,
  rejectButtonProps,
  autoCloseOnAccept,
  autoCloseOnReject,
  onAcceptClick,
  onRejectClick,
  children,
}) => {
  const isUntilBP1 = useWindowMatchMQ(until(MODAL_BP_1));
  const showRejectButton = !isUntilBP1 && !!rejectText;

  return (
    <StyledModalFooter>
      <Box fit>{children}</Box>

      {(showRejectButton || !!acceptText) && (
        <Box center gap_100>
          <ModalFooterRejectButton
            rejectText={rejectText}
            rejectButtonProps={rejectButtonProps}
            autoCloseOnReject={autoCloseOnReject}
            onRejectClick={onRejectClick}
          />
          {!!acceptText && (
            <ModalFooterAcceptButton
              acceptText={acceptText}
              acceptButtonProps={acceptButtonProps}
              onAcceptClick={onAcceptClick}
              autoCloseOnAccept={autoCloseOnAccept}
            />
          )}
        </Box>
      )}
    </StyledModalFooter>
  );
};
