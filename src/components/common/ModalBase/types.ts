import { ReactNode } from 'react';
import { Color, ModalWidth } from 'styled-components';

import { BoxProps } from '@components/common/Box/types';
import { ButtonProps } from '@components/common/CTA/types';

export interface ModalBaseProps {
  isOpen: boolean;
  width?: ModalWidth;
  backgroundColor?: Color;

  /**
   * By default, modals are rendered to the DOM tree only while
   * open, and are removed from it otherwise. Enable this prop
   * to make them always in the DOM tree.
   * */
  alwaysRender?: boolean;

  /**
   * By default, the modal card has auto-scrolls.
   * Use this to prevent it.
   * */
  suppressAutoScroll?: boolean;

  /**
   * If enabled, the user won't be able to dismiss
   * the modal by clicking outside of it or swiping
   * it down.
   * */
  nonDismissible?: boolean;

  /**
   * Which element to render the modal in.
   * Defaults to the document body.
   * */
  appendTo?: HTMLElement | null;

  onRequestClose?: () => void;
  onFocus?: () => void;
}

export interface ModalHeaderProps extends Omit<BoxProps<'div'>, 'title'> {
  mobileTitle?: ReactNode;
  mobileLeftContent?: ReactNode;

  title?: ReactNode;
  subtitle?: ReactNode;

  linkText?: string;
  linkTo?: string;
  onLinkClick?: () => void;

  closeButtonProps?: ButtonProps;
  suppressCloseButton?: boolean;

  alert?: ReactNode;
}

export interface ModalFooterProps {
  acceptText?: string;
  rejectText?: string;
  acceptButtonProps?: Omit<ButtonProps, 'onClick'>;
  rejectButtonProps?: Omit<ButtonProps, 'onClick'>;
  autoCloseOnAccept?: boolean;
  autoCloseOnReject?: boolean;
  onAcceptClick?: () => void;
  onRejectClick?: () => void;
}

export interface ModalContextValue {
  addModalToStack: (modalId: string) => void;
  popModalFromStack: (modalId: string) => void;
  getModalIndex: (modalId: string) => number;
  getModalReverseIndex: (modalId: string) => number;
}

export interface ModalInnerContextValue {
  close: () => void;
}
