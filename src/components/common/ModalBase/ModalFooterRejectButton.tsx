import React, { useCallback, useContext } from 'react';

import { Button } from '@components/common/CTA';
import { ModalInnerContext } from '@components/common/ModalBase/context';
import { useWindowMatchMQ } from '@hooks/useWindowMatchMedia';
import { until } from '@style/mediaQueries';

import { MODAL_BP_1 } from '../Modal/constants';

import { ModalFooterProps } from './types';

export const ModalFooterRejectButton: React.FCWithChildren<
  Pick<ModalFooterProps, 'rejectText' | 'rejectButtonProps' | 'autoCloseOnReject' | 'onRejectClick'>
> = ({ rejectText, rejectButtonProps, autoCloseOnReject, onRejectClick }) => {
  const innerContext = useContext(ModalInnerContext);

  const isUntilBP1 = useWindowMatchMQ(until(MODAL_BP_1));
  const showRejectButton = !isUntilBP1 && !!rejectText;

  const handleReject = useCallback(() => {
    autoCloseOnReject && innerContext?.close();
    onRejectClick && onRejectClick();
  }, [autoCloseOnReject, innerContext, onRejectClick]);

  if (!showRejectButton) return null;

  return (
    <Button tertiary onClick={handleReject} {...rejectButtonProps}>
      {rejectText}
    </Button>
  );
};
