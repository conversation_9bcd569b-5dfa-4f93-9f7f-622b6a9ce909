import React, { useCallback, useContext } from 'react';

import { Button } from '@components/common/CTA';
import { ModalInnerContext } from '@components/common/ModalBase/context';

import { ModalFooterProps } from './types';

export const ModalFooterAcceptButton: React.FCWithChildren<
  Pick<ModalFooterProps, 'acceptText' | 'acceptButtonProps' | 'autoCloseOnAccept' | 'onAcceptClick'>
> = ({ acceptText, acceptButtonProps, autoCloseOnAccept, onAcceptClick }) => {
  const innerContext = useContext(ModalInnerContext);

  const handleAccept = useCallback(() => {
    autoCloseOnAccept && innerContext?.close();
    onAcceptClick && onAcceptClick();
  }, [autoCloseOnAccept, innerContext, onAcceptClick]);

  return (
    <Button primary onClick={handleAccept} {...acceptButtonProps}>
      {acceptText}
    </Button>
  );
};
