import React from 'react';

import { useWindowMatchMQ } from '@hooks/useWindowMatchMedia';
import { until } from '@style/mediaQueries';

import { Box } from '../Box';
import { BoxProps } from '../Box/types';

export const Gutter: React.FC<BoxProps<'div'>> = ({ children, ...boxProps }) => {
  const isPhone = useWindowMatchMQ(until('phone'));
  return (
    <Box paddingVertical_150={!isPhone} paddingHorizontal_150={!isPhone} padding_100={isPhone} block {...boxProps}>
      {children}
    </Box>
  );
};
