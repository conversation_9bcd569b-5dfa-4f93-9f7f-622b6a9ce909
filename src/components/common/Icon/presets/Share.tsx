import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Share = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.0611 2.62248C15.215 2.24508 15.5825 1.99881 15.99 2.00001C16.2597 1.99924 16.5183 2.10742 16.707 2.30001L20.707 6.30001C21.0974 6.69051 21.0974 7.32351 20.707 7.71401L16.707 11.714L16.678 11.743C16.4203 11.9904 16.0501 12.0815 15.7069 11.982C15.3638 11.8825 15.0998 11.6074 15.0144 11.2605C14.9291 10.9135 15.0353 10.5474 15.293 10.3L17.586 8.01001H17C14.6118 8.00575 12.3201 8.9526 10.6314 10.6413C8.94261 12.3301 7.99577 14.6218 8.00003 17.01V17.038C7.99229 17.5903 7.53831 18.0317 6.98603 18.024C6.43374 18.0163 5.99229 17.5623 6.00003 17.01C6.00829 10.9383 10.9283 6.01827 17 6.01001H17.583L15.29 3.71701C14.9978 3.43291 14.9072 2.99989 15.0611 2.62248ZM2.00003 9.00501C1.99722 8.45697 2.43605 8.00877 2.98403 8.00001L3.01403 7.99101C3.27925 7.9947 3.53215 8.10361 3.71706 8.29377C3.90198 8.48394 4.00377 8.73978 4.00003 9.00501V19.005C3.99566 19.2715 4.09961 19.5284 4.2881 19.7169C4.47659 19.9054 4.73349 20.0094 5.00003 20.005H19C19.2666 20.0094 19.5235 19.9054 19.712 19.7169C19.9004 19.5284 20.0044 19.2715 20 19.005V17.977C20.0078 17.4247 20.4617 16.9833 21.014 16.991C21.5663 16.9987 22.0078 17.4527 22 18.005V19.005C21.9918 20.6585 20.6535 21.9968 19 22.005H5.00003C3.34658 21.9968 2.00823 20.6585 2.00003 19.005V9.00501Z"
      />
    </IconSVGView>
  );
};
