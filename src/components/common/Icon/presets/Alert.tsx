import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Alert = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.555 10.928L13.072 1.44499C12.4796 0.852808 11.5194 0.852808 10.927 1.44499L1.44399 10.928C0.851801 11.5204 0.851801 12.4806 1.44399 13.073L10.927 22.556C11.5198 23.1471 12.4791 23.1471 13.072 22.556L22.555 13.073C22.8396 12.7886 22.9995 12.4028 22.9995 12.0005C22.9995 11.5982 22.8396 11.2123 22.555 10.928ZM13 16.5C13 16.7761 12.7761 17 12.5 17H11.5C11.2238 17 11 16.7761 11 16.5V15.5C11 15.2239 11.2238 15 11.5 15H12.5C12.7761 15 13 15.2239 13 15.5V16.5ZM11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12V7.99999C13 7.44771 12.5523 6.99999 12 6.99999C11.4477 6.99999 11 7.44771 11 7.99999V12Z"
      />
    </IconSVGView>
  );
};
