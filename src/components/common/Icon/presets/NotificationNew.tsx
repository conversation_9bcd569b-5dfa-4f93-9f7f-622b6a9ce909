import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const NotificationNew = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.5 17.25L19.7198 17.4697C19.9342 17.6842 19.9983 18.0068 19.8823 18.287C19.7662 18.5672 19.4928 18.7499 19.1895 18.75H4.8105C4.5072 18.7499 4.2338 18.5672 4.11774 18.287C4.00168 18.0068 4.06582 17.6842 4.28025 17.4697L4.5 17.25L6 15.75V9.75C6.00272 7.01624 7.85304 4.62994 10.5 3.9465V3.75C10.5 2.92157 11.1716 2.25 12 2.25C12.8284 2.25 13.5 2.92157 13.5 3.75V3.9465C16.147 4.62994 17.9973 7.01624 18 9.75V15.75L19.5 17.25ZM10.125 19.5H13.5H13.875V19.875C13.875 20.9105 13.0355 21.75 12 21.75C10.9645 21.75 10.125 20.9105 10.125 19.875V19.5Z"
      />
      <path
        fill={useColor(color)}
        d="M23.5 7.5C23.5 10.5376 21.0376 13 18 13C14.9624 13 12.5 10.5376 12.5 7.5C12.5 4.46243 14.9624 2 18 2C21.0376 2 23.5 4.46243 23.5 7.5Z"
      />
      <path
        fill={useColor(color)}
        d="M17.8066 9.84473H14.8125V9.21484L15.8877 8.12793C16.1045 7.90527 16.2783 7.71973 16.4092 7.57129C16.542 7.4209 16.6377 7.28418 16.6963 7.16113C16.7568 7.03809 16.7871 6.90625 16.7871 6.76562C16.7871 6.5957 16.7393 6.46875 16.6436 6.38477C16.5498 6.29883 16.4238 6.25586 16.2656 6.25586C16.0996 6.25586 15.9385 6.29395 15.7822 6.37012C15.626 6.44629 15.4629 6.55469 15.293 6.69531L14.8008 6.1123C14.9238 6.00684 15.0537 5.90723 15.1904 5.81348C15.3291 5.71973 15.4893 5.64453 15.6709 5.58789C15.8545 5.5293 16.0742 5.5 16.3301 5.5C16.6113 5.5 16.8525 5.55078 17.0537 5.65234C17.2568 5.75391 17.4131 5.89258 17.5225 6.06836C17.6318 6.24219 17.6865 6.43945 17.6865 6.66016C17.6865 6.89648 17.6396 7.1123 17.5459 7.30762C17.4521 7.50293 17.3154 7.69629 17.1357 7.8877C16.958 8.0791 16.7432 8.29102 16.4912 8.52344L15.9404 9.04199V9.08301H17.8066V9.84473Z"
      />
      <path
        fill={useColor(color)}
        d="M21.0732 6.51953C21.0732 6.7168 21.0322 6.8877 20.9502 7.03223C20.8682 7.17676 20.7568 7.29492 20.6162 7.38672C20.4775 7.47852 20.3213 7.5459 20.1475 7.58887V7.60645C20.4912 7.64941 20.752 7.75488 20.9297 7.92285C21.1094 8.09082 21.1992 8.31543 21.1992 8.59668C21.1992 8.84668 21.1377 9.07031 21.0146 9.26758C20.8936 9.46484 20.7061 9.62012 20.4521 9.7334C20.1982 9.84668 19.8711 9.90332 19.4707 9.90332C19.2344 9.90332 19.0137 9.88379 18.8086 9.84473C18.6055 9.80762 18.4141 9.75 18.2344 9.67188V8.90137C18.418 8.99512 18.6104 9.06641 18.8115 9.11523C19.0127 9.16211 19.2002 9.18555 19.374 9.18555C19.6982 9.18555 19.9248 9.12988 20.0537 9.01855C20.1846 8.90527 20.25 8.74707 20.25 8.54395C20.25 8.4248 20.2197 8.32422 20.1592 8.24219C20.0986 8.16016 19.9932 8.09766 19.8428 8.05469C19.6943 8.01172 19.4863 7.99023 19.2188 7.99023H18.8936V7.2959H19.2246C19.4883 7.2959 19.6885 7.27148 19.8252 7.22266C19.9639 7.17188 20.0576 7.10352 20.1064 7.01758C20.1572 6.92969 20.1826 6.83008 20.1826 6.71875C20.1826 6.56641 20.1357 6.44727 20.042 6.36133C19.9482 6.27539 19.792 6.23242 19.5732 6.23242C19.4365 6.23242 19.3115 6.25 19.1982 6.28516C19.0869 6.31836 18.9863 6.35938 18.8965 6.4082C18.8066 6.45508 18.7275 6.50098 18.6592 6.5459L18.2402 5.92188C18.4082 5.80078 18.6045 5.7002 18.8291 5.62012C19.0557 5.54004 19.3252 5.5 19.6377 5.5C20.0791 5.5 20.4287 5.58887 20.6865 5.7666C20.9443 5.94434 21.0732 6.19531 21.0732 6.51953Z"
      />
    </IconSVGView>
  );
};
