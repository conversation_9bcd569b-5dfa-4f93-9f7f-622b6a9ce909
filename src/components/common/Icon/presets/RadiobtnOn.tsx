import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const RadiobtnOn = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C21.9917 6.48058 17.5194 2.00826 12 2ZM12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C3.99601 9.87705 4.83758 7.8399 6.33874 6.33874C7.8399 4.83758 9.87705 3.99601 12 4ZM6 12C6 8.68629 8.68629 6 12 6C13.5913 6 15.1174 6.63214 16.2426 7.75736C17.3679 8.88258 18 10.4087 18 12C18 15.3137 15.3137 18 12 18C8.68629 18 6 15.3137 6 12Z"
      />
    </IconSVGView>
  );
};
