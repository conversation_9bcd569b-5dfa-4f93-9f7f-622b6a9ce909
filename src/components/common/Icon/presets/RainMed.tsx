import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const RainMed = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.87 8.09991C19.4083 5.73918 18.1745 3.33262 15.9435 2.39164C13.7125 1.45067 11.1279 2.24675 9.813 4.27991C7.5592 3.56613 5.09635 4.24236 3.52304 6.00696C1.94974 7.77156 1.5593 10.2955 2.52588 12.453C3.49247 14.6105 5.63587 15.9993 8 15.9999H8.75V13.7499C8.75215 12.5328 9.72329 11.5389 10.94 11.5086C12.1567 11.4782 13.1762 12.4225 13.239 13.6379C13.4788 13.549 13.7322 13.5024 13.988 13.4999H14.012C15.2473 13.5016 16.2483 14.5026 16.25 15.7379V15.9999H18C20.0325 15.9904 21.7348 14.4582 21.9573 12.4378C22.1798 10.4175 20.8518 8.55146 18.87 8.09991ZM11 12.9999C10.5862 13.001 10.2511 13.3362 10.25 13.7499V18.2499C10.25 18.6641 10.5858 18.9999 11 18.9999C11.4142 18.9999 11.75 18.6641 11.75 18.2499V13.7499C11.7489 13.3362 11.4138 13.001 11 12.9999ZM13.988 14.9999H14.012C14.4191 15.001 14.7489 15.3308 14.75 15.7379V20.2619C14.7489 20.669 14.4191 20.9988 14.012 20.9999H13.988C13.5809 20.9988 13.2511 20.669 13.25 20.2619V15.7379C13.2511 15.3308 13.5809 15.001 13.988 14.9999Z"
      />
    </IconSVGView>
  );
};
