import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const DataLow = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 4.5H21C21.8284 4.5 22.5 5.17157 22.5 6V19.5C22.5 20.3284 21.8284 21 21 21H18C17.1716 21 16.5 20.3284 16.5 19.5V6C16.5 5.17157 17.1716 4.5 18 4.5ZM18 19.5H21V6H18V19.5ZM10.5 9H13.5C14.3284 9 15 9.67157 15 10.5V19.5C15 20.3284 14.3284 21 13.5 21H10.5C9.67157 21 9 20.3284 9 19.5V10.5C9 9.67157 9.67157 9 10.5 9ZM10.5 19.5H13.5V10.5H10.5V19.5ZM2.5 13.5C1.94772 13.5 1.5 13.9477 1.5 14.5V20C1.5 20.5523 1.94772 21 2.5 21H6.5C7.05228 21 7.5 20.5523 7.5 20V14.5C7.5 13.9477 7.05228 13.5 6.5 13.5H2.5Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 4.5H21C21.8284 4.5 22.5 5.17157 22.5 6V19.5C22.5 20.3284 21.8284 21 21 21H18C17.1716 21 16.5 20.3284 16.5 19.5V6C16.5 5.17157 17.1716 4.5 18 4.5ZM18 19.5H21V6H18V19.5ZM10.5 9H13.5C14.3284 9 15 9.67157 15 10.5V19.5C15 20.3284 14.3284 21 13.5 21H10.5C9.67157 21 9 20.3284 9 19.5V10.5C9 9.67157 9.67157 9 10.5 9ZM10.5 19.5H13.5V10.5H10.5V19.5ZM2.5 13.5C1.94772 13.5 1.5 13.9477 1.5 14.5V20C1.5 20.5523 1.94772 21 2.5 21H6.5C7.05228 21 7.5 20.5523 7.5 20V14.5C7.5 13.9477 7.05228 13.5 6.5 13.5H2.5Z"
      />
      <rect fill={useColor(color)} x="-30" y="-30" width="96" height="96" />
    </IconSVGView>
  );
};
