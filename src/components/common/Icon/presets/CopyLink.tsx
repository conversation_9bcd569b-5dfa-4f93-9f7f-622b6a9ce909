import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const CopyLink = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 6.99999C14.4477 6.99999 14 7.4477 14 7.99999C14 8.55227 14.4477 8.99999 15 8.99999H17.83C19.4676 8.96687 20.8507 10.2084 20.994 11.84C21.0379 12.6619 20.7421 13.4658 20.176 14.0633C19.6099 14.6609 18.8231 14.9995 18 15H15C14.4477 15 14 15.4477 14 16C14 16.5523 14.4477 17 15 17H18C19.4115 17.0003 20.7574 16.404 21.7054 15.3582C22.6535 14.3125 23.1153 12.9147 22.977 11.51C22.6524 8.90112 20.4136 6.95638 17.785 6.99999H15ZM5.99999 6.99999C4.58847 6.99969 3.24259 7.59601 2.29455 8.64176C1.34652 9.68752 0.884661 11.0853 1.02299 12.49C1.34755 15.0989 3.58637 17.0436 6.21499 17H8.99999C9.55227 17 9.99999 16.5523 9.99999 16C9.99999 15.4477 9.55227 15 8.99999 15H6.16999C4.53242 15.0331 3.14929 13.7916 3.00599 12.16C2.96209 11.338 3.25784 10.5342 3.82397 9.93664C4.3901 9.33912 5.17687 9.00046 5.99999 8.99999H8.99999C9.55227 8.99999 9.99999 8.55227 9.99999 7.99999C9.99999 7.4477 9.55227 6.99999 8.99999 6.99999H5.99999ZM6.99999 12C6.99999 11.4477 7.44771 11 7.99999 11H16C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13H7.99999C7.44771 13 6.99999 12.5523 6.99999 12Z"
      />
    </IconSVGView>
  );
};
