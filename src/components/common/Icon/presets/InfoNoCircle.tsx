import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const InfoNoCircle = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 5C9 3.34315 10.3431 2 12 2C13.6569 2 15 3.34315 15 5C15 6.65685 13.6569 8 12 8C10.3431 8 9 6.65685 9 5ZM8.12697 10.4879C8.30994 10.181 8.64273 9.995 9 10H13C13.5523 10 14 10.4477 14 11V20H15.028C15.5803 20.0077 16.0217 20.4617 16.014 21.014C16.0063 21.5663 15.5523 22.0077 15 22H8.972C8.61473 21.995 8.28727 21.7998 8.11297 21.4879C7.93867 21.176 7.94401 20.7948 8.12697 20.4879C8.30994 20.181 8.64273 19.995 9 20H10V12H8.972C8.61473 11.995 8.28727 11.7998 8.11297 11.4879C7.93867 11.176 7.94401 10.7948 8.12697 10.4879Z"
      />
    </IconSVGView>
  );
};
