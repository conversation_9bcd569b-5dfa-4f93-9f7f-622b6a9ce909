import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const _FullScreen = ({ color, size, ...svgProps }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size} {...svgProps}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.28571 14.2857H4V20H9.71429V17.7143H6.28571V14.2857ZM4 9.71429H6.28571V6.28571H9.71429V4H4V9.71429ZM17.7143 17.7143H14.2857V20H20V14.2857H17.7143V17.7143ZM14.2857 4V6.28571H17.7143V9.71429H20V4H14.2857Z"
      />
    </IconSVGView>
  );
};
