import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const PartlyCloudyDay = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.424 8.25H21.25V8.251C21.6642 8.251 22 8.58679 22 9.001C22 9.41521 21.6642 9.751 21.25 9.751H20.424C20.3133 10.4174 20.052 11.0498 19.66 11.6L20.244 12.182C20.4335 12.3715 20.5076 12.6478 20.4382 12.9067C20.3688 13.1656 20.1666 13.3678 19.9077 13.4372C19.6488 13.5065 19.3725 13.4325 19.183 13.243L18.6 12.66C18.076 13.0322 17.4782 13.2876 16.847 13.409C16.841 13.433 16.8363 13.458 16.8315 13.483C16.8268 13.508 16.822 13.533 16.816 13.557C18.7564 13.8465 20.1438 15.5855 19.995 17.5418C19.8462 19.498 18.2119 21.0072 16.25 21H7.25001C4.43597 21.0118 2.11585 18.7972 1.99672 15.9857C1.87759 13.1742 4.00206 10.7713 6.80701 10.545C7.52782 8.42888 9.5135 7.00451 11.749 7C11.7866 7 11.8233 7.00554 11.8597 7.01106C11.896 7.01654 11.9321 7.022 11.969 7.022C12.0757 6.80554 12.1994 6.59789 12.339 6.401L11.756 5.818C11.463 5.52501 11.463 5.04999 11.756 4.757C12.049 4.46401 12.524 4.46401 12.817 4.757L13.4 5.34C13.9508 4.94774 14.5839 4.68643 15.251 4.576V3.75C15.251 3.33579 15.5868 3 16.001 3C16.4152 3 16.751 3.33579 16.751 3.75V4.576C17.4174 4.68672 18.0498 4.94802 18.6 5.34L19.182 4.756C19.475 4.46301 19.95 4.46301 20.243 4.756C20.536 5.04899 20.536 5.52401 20.243 5.817L19.66 6.4C20.0521 6.9505 20.3134 7.58323 20.424 8.25ZM16.958 11.829C17.395 11.6857 17.793 11.4434 18.121 11.121V11.129C18.9231 10.3214 19.1991 9.12941 18.8336 8.05146C18.468 6.9735 17.5239 6.19527 16.396 6.04217C15.2681 5.88908 14.1507 6.38749 13.511 7.329C15.4453 8.02015 16.7945 9.78145 16.958 11.829Z"
      />
    </IconSVGView>
  );
};
