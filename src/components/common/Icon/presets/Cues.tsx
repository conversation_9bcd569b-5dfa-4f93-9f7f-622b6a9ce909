import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Cues = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <rect fill={useColor(color)} width="24" height="24" fillOpacity="0.01" />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.72935 13.7797C8.12914 14.9083 8.45335 16.9104 7.48126 18.423L10.8373 21.78C11.0321 21.9684 11.1102 22.2473 11.0414 22.5095C10.9727 22.7717 10.7678 22.9764 10.5055 23.0449C10.2433 23.1134 9.96447 23.0351 9.77626 22.84L6.42026 19.483C4.90669 20.4537 2.90487 20.1276 1.77763 18.7268C0.650395 17.3259 0.760152 15.3007 2.03218 14.0298C3.30421 12.759 5.32955 12.6512 6.72935 13.7797ZM2.53826 16.54C2.53826 17.6446 3.43369 18.54 4.53826 18.54C5.64283 18.54 6.53826 17.6446 6.53826 16.54C6.53826 15.4355 5.64283 14.54 4.53826 14.54C3.43369 14.54 2.53826 15.4355 2.53826 16.54Z"
      />
      <path
        fill={useColor(color)}
        d="M11.0659 22.3325C10.0813 22.028 9.24382 21.3392 8.76 20.4H9.45761L10.8373 21.78C10.9886 21.9264 11.0695 22.1273 11.0659 22.3325Z"
      />
      <path
        fill={useColor(color)}
        d="M1.56339 13.101C1.52189 12.9156 1.5 12.7229 1.5 12.525C1.5 11.0753 2.67525 9.9 4.125 9.9C5.78802 9.9478 7.41582 10.3905 8.874 11.1915C8.8716 11.1591 8.86728 11.1272 8.86296 11.0953C8.85648 11.0474 8.85 10.9995 8.85 10.95C8.85 9.2103 10.2603 7.8 12 7.8C13.7397 7.8 15.15 9.2103 15.15 10.95C15.15 10.9914 15.1439 11.0317 15.1379 11.0719C15.1319 11.1116 15.126 11.1512 15.126 11.1915C16.5842 10.3905 18.212 9.9478 19.875 9.9C21.3247 9.9 22.5 11.0753 22.5 12.525C22.5 13.9747 21.3247 15.15 19.875 15.15C18.4027 15.1144 16.9561 14.756 15.6375 14.1H8.3625C8.35642 14.103 8.35034 14.106 8.34426 14.109C8.08485 13.6975 7.75473 13.3219 7.35699 13.0012C5.64451 11.6206 3.20294 11.681 1.56339 13.101Z"
      />
      <path
        fill={useColor(color)}
        d="M1.99833 14.0641C2.30967 14.4936 2.74791 14.825 3.25801 15.0034C3.60488 14.7141 4.05124 14.54 4.53826 14.54C5.0075 14.54 5.43899 14.7017 5.78013 14.9722C6.33586 14.8655 6.88196 14.7119 7.41254 14.513C7.22513 14.243 6.99688 13.9954 6.72935 13.7797C5.32955 12.6512 3.30421 12.759 2.03218 14.0298C2.0208 14.0412 2.00952 14.0526 1.99833 14.0641Z"
      />
      <path
        fill={useColor(color)}
        d="M8.02548 16.2H7.818C7.8075 16.3725 7.8 16.548 7.8 16.725C7.80016 17.0334 7.81866 17.3412 7.85533 17.647C8.01474 17.1759 8.06971 16.6827 8.02548 16.2Z"
      />
      <path
        fill={useColor(color)}
        d="M10.8716 20.4H15.24C14.6176 21.6082 13.41 22.402 12.057 22.4964C12.1105 21.9712 11.9237 21.4439 11.5397 21.0683L10.8716 20.4Z"
      />
      <path
        fill={useColor(color)}
        d="M8.77224 18.3H16.038C16.1454 17.7819 16.1997 17.2541 16.2 16.725C16.2 16.548 16.1925 16.3725 16.182 16.2H9.02876C9.07744 16.882 8.97217 17.5776 8.70399 18.2317L8.77224 18.3Z"
      />
      <path
        fill={useColor(color)}
        d="M10.9785 2.30878C11.0903 1.83496 11.5132 1.50017 12 1.5C12.4857 1.50155 12.9069 1.83595 13.0185 2.3086C13.1302 2.78125 12.9031 3.26879 12.4695 3.4875L13.05 4.65L15.204 3.9315C15.0602 3.4992 15.2115 3.02348 15.5787 2.75376C15.9459 2.48405 16.4451 2.4819 16.8146 2.74845C17.1841 3.01499 17.3395 3.4894 17.1994 3.92292C17.0593 4.35644 16.6556 4.65013 16.2 4.65C16.0892 4.64899 15.9793 4.63025 15.8745 4.5945L15.396 6.0315C15.2533 6.46043 14.8521 6.74987 14.4 6.75H9.6C9.15016 6.7473 8.75202 6.45834 8.61 6.0315L8.1315 4.5945C7.6695 4.74892 7.16196 4.5653 6.90569 4.15103C6.64943 3.73676 6.71174 3.20064 7.05619 2.85619C7.40064 2.51174 7.93676 2.44943 8.35103 2.70569C8.7653 2.96196 8.94892 3.4695 8.7945 3.9315L10.95 4.65L11.5305 3.489C11.0951 3.27114 10.8666 2.78259 10.9785 2.30878Z"
      />
    </IconSVGView>
  );
};
