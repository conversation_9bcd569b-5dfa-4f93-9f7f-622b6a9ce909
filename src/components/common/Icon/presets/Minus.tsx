import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Minus = ({ color, size, ...svgProps }: IconSVGProps) => {
  return (
    <IconSVGView size={size} fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" {...svgProps}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.5 6c0-.276.237-.5.53-.5h7.94c.293 0 .53.224.53.5s-.237.5-.53.5H2.03c-.293 0-.53-.224-.53-.5Z"
        fill={useColor(color)}
      />
    </IconSVGView>
  );
};
