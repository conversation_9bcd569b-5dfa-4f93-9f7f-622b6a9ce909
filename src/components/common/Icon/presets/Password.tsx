import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Password = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 1C8.67619 1 6 3.67619 6 7V8C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8V7C18 3.67619 15.3238 1 12 1ZM12 3C14.2762 3 16 4.72381 16 7V8H8V7C8 4.72381 9.72381 3 12 3ZM9 15C9 14.45 8.55 14 8 14C7.45 14 7 14.45 7 15C7 15.55 7.45 16 8 16C8.55 16 9 15.55 9 15ZM12 14C12.55 14 13 14.45 13 15C13 15.55 12.55 16 12 16C11.45 16 11 15.55 11 15C11 14.45 11.45 14 12 14ZM17 15C17 14.45 16.55 14 16 14C15.45 14 15 14.45 15 15C15 15.55 15.45 16 16 16C16.55 16 17 15.55 17 15Z"
      />
    </IconSVGView>
  );
};
