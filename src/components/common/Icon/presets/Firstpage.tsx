import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Firstpage = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.00531 17C6.00531 17.5523 6.45302 18 7.00531 18C7.55759 18 8.00531 17.5523 8.00531 17L8.00531 7C8.00531 6.44771 7.55759 6 7.00531 6C6.45302 6 6.00531 6.44771 6.00531 7L6.00531 17ZM13.8243 12L17.7143 15.89C18.0948 16.2828 18.0953 16.9066 17.7153 17.3C17.3253 17.6877 16.6953 17.6877 16.3053 17.3L11.7153 12.7C11.3276 12.31 11.3276 11.68 11.7153 11.29L16.3053 6.7C16.7024 6.36324 17.2914 6.38752 17.6595 6.75581C18.0275 7.1241 18.0513 7.71315 17.7143 8.11L13.8243 12Z"
      />
    </IconSVGView>
  );
};
