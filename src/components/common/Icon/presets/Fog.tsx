import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Fog = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.2655 3.22135C16.9618 4.03282 18.5313 6.83358 17.816 9.55693C19.7564 9.84646 21.1437 11.5855 20.995 13.5417C20.8462 15.4979 19.2119 17.0071 17.25 16.9999H4.75C4.33579 16.9999 4 16.6641 4 16.2499C4 15.8357 4.33579 15.4999 4.75 15.4999H12.25C12.6642 15.4999 13 15.1641 13 14.7499C13 14.3357 12.6642 13.9999 12.25 13.9999H3.75C3.33579 13.9999 3 13.6641 3 13.2499C3 12.8357 3.33579 12.4999 3.75 12.4999H12.25C12.6642 12.4999 13 12.1641 13 11.7499C13 11.3357 12.6642 10.9999 12.25 10.9999H3.072C3.42257 8.59427 5.38542 6.74788 7.808 6.54493C8.71458 3.87914 11.5692 2.40989 14.2655 3.22135ZM7.715 18.9999C7.32012 18.9999 7 19.32 7 19.7149V19.7849C7 20.1798 7.32012 20.4999 7.715 20.4999H16.285C16.6799 20.4999 17 20.1798 17 19.7849V19.7149C17 19.32 16.6799 18.9999 16.285 18.9999H7.715Z"
      />
    </IconSVGView>
  );
};
