import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const _FullScreenExit = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 16.5714H7.42857V20H9.71429V14.2857H4V16.5714ZM7.42857 7.42857H4V9.71429H9.71429V4H7.42857V7.42857ZM14.2857 20H16.5714V16.5714H20V14.2857H14.2857V20ZM16.5714 7.42857V4H14.2857V9.71429H20V7.42857H16.5714Z"
      />
    </IconSVGView>
  );
};
