import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const RainLow = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9421 4.39287C18.1731 5.33384 19.4069 7.7404 18.8686 10.1011C20.8504 10.5527 22.1785 12.4187 21.956 14.439C21.7335 16.4594 20.0312 17.9917 17.9986 18.0011H16.2486V17.7511C16.2475 16.5089 15.2408 15.5022 13.9986 15.5011C13.7386 15.5034 13.481 15.5515 13.2376 15.6431C13.1749 14.4284 12.1566 13.4843 10.9406 13.5136C9.72458 13.5428 8.75284 14.5348 8.74862 15.7511V17.2511C8.75116 17.5072 8.79784 17.7609 8.88662 18.0011H7.99862C5.6345 18.0005 3.49109 16.6118 2.52451 14.4543C1.55793 12.2968 1.94837 9.77278 3.52167 8.00818C5.09498 6.24359 7.55783 5.56735 9.81162 6.28113C11.1266 4.24797 13.7111 3.45189 15.9421 4.39287ZM10.9986 15.0011C11.4124 15.0022 11.7475 15.3374 11.7486 15.7511V17.2511C11.747 17.6647 11.4122 17.9995 10.9986 18.0011C10.8986 18.0013 10.7997 17.9812 10.7076 17.9421C10.5278 17.8652 10.3845 17.722 10.3076 17.5421C10.2686 17.4501 10.2485 17.3511 10.2486 17.2511V15.7511C10.2485 15.6511 10.2686 15.5522 10.3076 15.4601C10.3845 15.2803 10.5278 15.137 10.7076 15.0601C10.7997 15.0211 10.8986 15.001 10.9986 15.0011ZM13.7076 17.0601C13.7997 17.0211 13.8986 17.001 13.9986 17.0011C14.4124 17.0022 14.7475 17.3374 14.7486 17.7511V19.2511C14.747 19.6647 14.4122 19.9995 13.9986 20.0011C13.8986 20.0013 13.7997 19.9812 13.7076 19.9421C13.5278 19.8652 13.3845 19.722 13.3076 19.5421C13.2686 19.4501 13.2485 19.3511 13.2486 19.2511V17.7511C13.2485 17.6511 13.2686 17.5522 13.3076 17.4601C13.3845 17.2803 13.5278 17.137 13.7076 17.0601Z"
      />
    </IconSVGView>
  );
};
