import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Nfc = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 1.00586C9.43871 1.00586 6.87823 1.98114 4.92968 3.92969C4.66845 4.18051 4.56322 4.55295 4.65458 4.90339C4.74594 5.25383 5.0196 5.5275 5.37004 5.61885C5.72048 5.71021 6.09293 5.60498 6.34375 5.34375C9.47666 2.21084 14.5233 2.21084 17.6562 5.34375C17.9071 5.60499 18.2795 5.71022 18.63 5.61886C18.9804 5.5275 19.2541 5.25383 19.3454 4.90339C19.4368 4.55295 19.3315 4.1805 19.0703 3.92969C17.1218 1.98114 14.5613 1.00586 12 1.00586ZM12 5.05664C10.4752 5.05664 8.95054 5.6354 7.79296 6.79297C7.53173 7.04379 7.4265 7.41623 7.51786 7.76667C7.60921 8.11711 7.88288 8.39078 8.23332 8.48214C8.58376 8.5735 8.95621 8.46827 9.20703 8.20703C10.7579 6.65618 13.2421 6.65618 14.793 8.20703C15.0438 8.46827 15.4162 8.5735 15.7667 8.48214C16.1171 8.39079 16.3908 8.11712 16.4821 7.76667C16.5735 7.41623 16.4683 7.04379 16.207 6.79297C15.0495 5.6354 13.5248 5.05664 12 5.05664ZM10.5859 9.58594C10.9722 9.19965 11.4858 9.00582 12 9.00586C12.5142 9.0059 13.028 9.19891 13.4141 9.58594C13.8045 9.97696 13.8041 10.6105 13.4131 11.001C13.0221 11.3915 12.3885 11.391 11.998 11C11.7493 11.2704 11.3723 11.3826 11.0162 11.2923C10.66 11.202 10.3821 10.9237 10.2923 10.5674C10.2024 10.2111 10.3152 9.83431 10.5859 9.58594ZM5.89062 9.00781C5.42128 9.06298 5 9.44508 5 10.002V20.5C5 21.881 6.119 23 7.5 23H16.5C17.881 23 19 21.881 19 20.5V10.002C19 9.11095 17.923 8.66492 17.293 9.29492C17.105 9.48192 17 9.73695 17 10.002V18H7V10.002C7 9.73695 6.89503 9.48192 6.70703 9.29492C6.47078 9.05867 6.17222 8.97471 5.89062 9.00781ZM13.125 20C13.125 19.378 12.622 18.875 12 18.875C11.378 18.875 10.875 19.378 10.875 20C10.875 20.622 11.378 21.125 12 21.125C12.622 21.125 13.125 20.622 13.125 20Z"
      />
    </IconSVGView>
  );
};
