import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Tag = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.28573 10.2452V12.3024H4.22858V10.2452H6.28573Z"
      />

      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.22858 15.731H6.28573V17.7881H4.22858V15.731Z"
      />

      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.7143 10.2452H11.7714V12.3024H9.7143V10.2452Z"
      />

      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 2.75C0 1.64543 0.895431 0.75 2 0.75H14C15.1046 0.75 16 1.64543 16 2.75V21.0167C16 22.1212 15.1046 23.0167 14 23.0167H2C0.895431 23.0167 0 22.1212 0 21.0167V2.75ZM2.13336 5.70001C2.13336 5.2858 2.46915 4.95001 2.88336 4.95001H13.1167C13.5309 4.95001 13.8667 5.2858 13.8667 5.70001C13.8667 6.11423 13.5309 6.45001 13.1167 6.45001H2.88336C2.46915 6.45001 2.13336 6.11423 2.13336 5.70001ZM7.3143 9.21667H3.20001V13.331H7.3143V9.21667ZM7.3143 14.7024H3.20001V18.8167H7.3143V14.7024ZM12.8 9.21667H8.68573V13.331H12.8V9.21667ZM9.7143 14.7024H8.68573V15.731H9.7143V16.7595H8.68573V17.7881H9.7143V18.8167H10.7429V17.7881H11.7714V18.8167H12.8V17.7881H11.7714V16.7595H12.8V15.731H11.7714V14.7024H10.7429V15.731H9.7143V14.7024Z"
      />
    </IconSVGView>
  );
};
