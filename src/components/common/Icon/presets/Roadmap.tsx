import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Roadmap = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 16 16" size={size}>
      <path
        fill={useColor(color)}
        d="M4.904 4.838a1.524 1.524 0 1 1 0-1.295h6.239a2.552 2.552 0 0 1 0 5.105H9.475a1.524 1.524 0 0 1-2.76 0H4.667a1.257 1.257 0 1 0 0 2.514h6.62a1.524 1.524 0 1 1 0 1.295h-6.62a2.552 2.552 0 1 1 0-5.105h2.048a1.524 1.524 0 0 1 2.76 0h1.668a1.257 1.257 0 0 0 0-2.514h-6.24Z"
      />
    </IconSVGView>
  );
};
