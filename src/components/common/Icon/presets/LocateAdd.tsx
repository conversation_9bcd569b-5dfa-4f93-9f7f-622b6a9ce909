import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const LocateAdd = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0001 2C7.68305 2 5.00024 4.6828 5.00024 7.99988C5.00024 12.4998 11.0001 19.1425 11.0001 19.1425C11.0001 19.1425 17 12.4998 17 7.99988C17 4.6828 14.3172 2 11.0001 2ZM11.0001 10.1426C9.81728 10.1426 8.8573 9.1826 8.8573 7.99976C8.8573 6.81693 9.81728 5.85695 11.0001 5.85695C12.183 5.85695 13.1429 6.81693 13.1429 7.99976C13.1429 9.1826 12.183 10.1426 11.0001 10.1426ZM18.75 16.5H20.25V18.75H22.5V20.25H20.25V22.5H18.75V20.25H16.5V18.75H18.75V16.5Z"
      />
    </IconSVGView>
  );
};
