import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Notification = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5 3.5C10.5 2.67157 11.1716 2 12 2C12.8284 2 13.5 2.67157 13.5 3.5V4.2C16.1457 4.88311 17.9957 7.26754 18 10V16L19.537 17.156C19.9124 17.3946 20.0851 17.8525 19.9608 18.2796C19.8364 18.7067 19.4448 19.0003 19 19H4.99999C4.55552 18.9994 4.16479 18.7055 4.04094 18.2786C3.91709 17.8518 4.08986 17.3944 4.46499 17.156L5.99999 16V10C6.00431 7.26754 7.85429 4.88311 10.5 4.2V3.5ZM12 22C10.8954 22 9.99999 21.1046 9.99999 20H14C14 21.1046 13.1046 22 12 22Z"
      />
    </IconSVGView>
  );
};
