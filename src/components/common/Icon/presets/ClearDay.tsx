import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const ClearDay = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.25 2.75C11.25 2.33579 11.5858 2 12 2C12.4142 2 12.75 2.33579 12.75 2.75V4.25C12.75 4.66421 12.4142 5 12 5C11.5858 5 11.25 4.66421 11.25 4.25V2.75ZM4.92948 5.99C4.63659 5.69711 4.63659 5.22223 4.92948 4.92934C5.22237 4.63645 5.69725 4.63645 5.99014 4.92934L7.0508 5.99C7.34369 6.28289 7.34369 6.75777 7.0508 7.05066C6.75791 7.34355 6.28303 7.34355 5.99014 7.05066L4.92948 5.99ZM2 12C2 12.4142 2.33579 12.75 2.75 12.75H4.25C4.66421 12.75 5 12.4142 5 12C5 11.5858 4.66421 11.25 4.25 11.25H2.75C2.33579 11.25 2 11.5858 2 12ZM5.98942 19.0711C5.69653 19.364 5.22166 19.364 4.92876 19.0711C4.63587 18.7782 4.63587 18.3033 4.92876 18.0104L5.98942 16.9498C6.28232 16.6569 6.75719 16.6569 7.05008 16.9498C7.34298 17.2427 7.34298 17.7175 7.05008 18.0104L5.98942 19.0711ZM12 22C12.4142 22 12.75 21.6642 12.75 21.25V19.75C12.75 19.3358 12.4142 19 12 19C11.5858 19 11.25 19.3358 11.25 19.75V21.25C11.25 21.6642 11.5858 22 12 22ZM19.0717 18.011C19.3646 18.3039 19.3646 18.7788 19.0717 19.0717C18.7788 19.3646 18.3039 19.3646 18.011 19.0717L16.9504 18.011C16.6575 17.7181 16.6575 17.2432 16.9504 16.9503C17.2432 16.6574 17.7181 16.6574 18.011 16.9503L19.0717 18.011ZM22 12C22 11.5858 21.6642 11.25 21.25 11.25H19.75C19.3358 11.25 19 11.5858 19 12C19 12.4142 19.3358 12.75 19.75 12.75H21.25C21.6642 12.75 22 12.4142 22 12ZM18.0094 4.9289C18.3023 4.63601 18.7772 4.63601 19.0701 4.9289C19.363 5.2218 19.363 5.69667 19.0701 5.98956L18.0094 7.05022C17.7165 7.34312 17.2417 7.34312 16.9488 7.05022C16.6559 6.75733 16.6559 6.28246 16.9488 5.98956L18.0094 4.9289ZM12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z"
      />
    </IconSVGView>
  );
};
