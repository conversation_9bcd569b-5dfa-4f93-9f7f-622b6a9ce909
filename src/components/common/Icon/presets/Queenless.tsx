import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Queenless = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.279 7.00007C14.71 7.0006 15.0928 6.72496 15.229 6.31607L15.684 4.94707C15.7857 4.98213 15.8925 5.00004 16 5.00007C16.4339 4.99996 16.8182 4.72008 16.9514 4.30717C17.0847 3.89425 16.9364 3.44252 16.5845 3.18883C16.2325 2.93513 15.7571 2.93736 15.4075 3.19434C15.0579 3.45132 14.9139 3.90442 15.051 4.31607L13 5.00007L12.447 3.89307C12.8619 3.68574 13.0798 3.22033 12.9733 2.7689C12.8668 2.31747 12.4638 1.99854 12 1.99854C11.5362 1.99854 11.1332 2.31747 11.0267 2.7689C10.9202 3.22033 11.1381 3.68574 11.553 3.89307L11 5.00007L8.947 4.31607C9.04846 4.01143 8.99747 3.6766 8.80993 3.41597C8.62238 3.15535 8.32109 3.00063 8 3.00007C7.69122 3.00305 7.40187 3.15125 7.219 3.40007L10.819 7.00007H14.279ZM12 8.00007C11.9675 8.00007 11.9362 8.00447 11.905 8.00886L11.905 8.00886C11.8805 8.01231 11.856 8.01575 11.831 8.01707L14.983 11.1711C14.9922 11.1145 14.9979 11.0574 15 11.0001C15 9.34321 13.6569 8.00007 12 8.00007ZM2.002 2.42107C2.39357 2.0306 3.02753 2.0315 3.418 2.42307L21.148 20.1631C21.5385 20.5538 21.5382 21.1871 21.1475 21.5776C20.7568 21.968 20.1235 21.9678 19.733 21.5771L15.888 17.7301C15.882 17.7659 15.8769 17.8024 15.8718 17.839L15.8718 17.839L15.8718 17.8391C15.8643 17.8933 15.8567 17.9476 15.846 18.0001H8.154C8.05206 17.5066 8.00046 17.004 8 16.5001C8 16.3311 8.008 16.1651 8.018 16.0001H14.16L12.16 14.0001H8.535C7.27948 14.6249 5.90201 14.9663 4.5 15.0001C3.11929 15.0001 2 13.8808 2 12.5001C2 11.1194 3.11929 10.0001 4.5 10.0001C6.0828 10.0464 7.63197 10.468 9.02 11.2301C9.01764 11.1999 9.0136 11.1701 9.00955 11.1404C9.00328 11.0943 8.997 11.0481 8.997 11.0001C8.997 10.9769 9.00063 10.9531 9.00432 10.9288L9.00432 10.9288C9.00813 10.9038 9.012 10.8785 9.012 10.8531L2 3.83707C1.60953 3.4455 1.61043 2.81153 2.002 2.42107ZM12 22.0001C13.3106 21.928 14.4849 21.1669 15.086 20.0001H8.914C9.51509 21.1669 10.6894 21.928 12 22.0001ZM19.5 15.0001C19.2509 14.9945 19.0022 14.9761 18.755 14.9451L15.02 11.2091C16.3973 10.4589 17.9323 10.0446 19.5 10.0001C20.8807 10.0001 22 11.1194 22 12.5001C22 13.8808 20.8807 15.0001 19.5 15.0001Z"
      />
    </IconSVGView>
  );
};
