import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Completed = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2 12C2 6.47715 6.47715 2 12 2C17.5194 2.00826 21.9917 6.48058 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12ZM4 12C4 16.4183 7.58172 20 12 20C14.123 20.004 16.1601 19.1624 17.6613 17.6613C19.1624 16.1601 20.004 14.123 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12Z"
      />
      <path
        fill={useColor(color)}
        d="M16.2141 8.78586C15.833 8.40471 15.2209 8.40471 14.8398 8.78586L10.4748 13.1508L9.16023 11.8362C8.77908 11.4551 8.167 11.4551 7.78586 11.8362C7.40471 12.2173 7.40471 12.8294 7.78586 13.2106L9.78288 15.2076C10.164 15.5887 10.7809 15.5886 11.162 15.2076L16.2141 10.1602C16.5953 9.77908 16.5953 9.167 16.2141 8.78586Z"
      />
    </IconSVGView>
  );
};
