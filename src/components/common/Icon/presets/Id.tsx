import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Id = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 2C2.895 2 2 2.895 2 4V18C2 19.105 2.895 20 4 20H15.1719L12.293 17.1211C12.105 16.9341 12 16.6791 12 16.4141V13C12 12.448 12.448 12 13 12H16.4141C16.6791 12 16.9341 12.105 17.1211 12.293L20 15.1719V4C20 2.895 19.105 2 18 2H4ZM6 5H16C16.552 5 17 5.448 17 6C17 6.552 16.552 7 16 7H6C5.448 7 5 6.552 5 6C5 5.448 5.448 5 6 5ZM9 9H6C5.448 9 5 9.448 5 10C5 10.552 5.448 11 6 11H9C9.552 11 10 10.552 10 10C10 9.448 9.552 9 9 9ZM14 14V16L19.1465 21.1465L21.1465 19.1465L16 14H14ZM19.8535 21.8535L21.8535 19.8535L22.5859 20.5859C23.1379 21.1379 23.1379 22.0339 22.5859 22.5859C22.0339 23.1379 21.1379 23.1379 20.5859 22.5859L19.8535 21.8535Z"
      />
    </IconSVGView>
  );
};
