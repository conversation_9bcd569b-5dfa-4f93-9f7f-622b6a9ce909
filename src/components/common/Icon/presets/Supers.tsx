import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Supers = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.8598 10.79L19.1098 7.79004C18.9298 7.48004 18.5998 7.29004 18.2498 7.29004H15.6498C15.5798 8.03004 15.2898 8.73004 14.8298 9.29004H17.6798L18.8498 11.29H5.15977L6.32977 9.29004H9.17977C8.71977 8.73004 8.41976 8.03004 8.35977 7.29004H5.75977C5.39977 7.29004 5.07977 7.48004 4.89977 7.79004L3.14977 10.79C3.05977 10.94 3.00977 11.11 3.00977 11.29V19.29C3.00977 19.84 3.45977 20.29 4.00977 20.29H20.0098C20.5598 20.29 21.0098 19.84 21.0098 19.29V11.29C21.0098 11.11 20.9598 10.94 20.8698 10.79H20.8598ZM13.9998 15.29H9.99977C9.44977 15.29 8.99977 14.84 8.99977 14.29C8.99977 13.74 9.44977 13.29 9.99977 13.29H13.9998C14.5498 13.29 14.9998 13.74 14.9998 14.29C14.9998 14.84 14.5498 15.29 13.9998 15.29Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9999 10.09C13.5499 10.09 14.7999 8.83996 14.7999 7.28996C14.7999 6.80996 14.6699 6.36996 14.4599 5.97996L14.3799 5.82996C14.3799 5.82996 14.3299 5.73996 14.3099 5.69996L13.2299 3.83996L11.9899 1.70996L10.7499 3.83996L9.52994 5.96996C9.31994 6.35996 9.18994 6.79996 9.18994 7.27996C9.18994 8.82996 10.4399 10.08 11.9899 10.08L11.9999 10.09Z"
      />
    </IconSVGView>
  );
};
