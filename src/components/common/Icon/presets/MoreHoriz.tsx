import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const MoreHoriz = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.5 12.75C7.5 13.9926 6.49264 15 5.25 15C4.00736 15 3 13.9926 3 12.75C3 11.5074 4.00736 10.5 5.25 10.5C6.49264 10.5 7.5 11.5074 7.5 12.75ZM14.25 12.75C14.25 13.9926 13.2426 15 12 15C10.7574 15 9.75 13.9926 9.75 12.75C9.75 11.5074 10.7574 10.5 12 10.5C13.2426 10.5 14.25 11.5074 14.25 12.75ZM18.75 15C19.9926 15 21 13.9926 21 12.75C21 11.5074 19.9926 10.5 18.75 10.5C17.5074 10.5 16.5 11.5074 16.5 12.75C16.5 13.9926 17.5074 15 18.75 15Z"
      />
    </IconSVGView>
  );
};
