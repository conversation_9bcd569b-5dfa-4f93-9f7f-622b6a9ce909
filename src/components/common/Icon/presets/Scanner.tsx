import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Scanner = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.33 3.95998C2.48 3.60998 2.36 3.23998 2.03 3.06998C1.95 3.02998 1.86 2.99998 1.78 2.98998C1.52 2.95998 1.26 3.07998 1.14 3.33998C0.85 3.94998 0.58 4.57998 0.36 5.21998C0.1 6.05998 0 6.90998 0 7.94998C0.01 9.32998 0.37 10.79 1.11 12.15C1.24 12.39 1.42 12.53 1.64 12.55H1.87C2.14 12.5 2.39 12.25 2.39 11.97C2.39 11.79 2.33 11.6 2.25 11.44C1.61 10.2 1.31 8.88998 1.36 7.49998C1.4 6.37998 1.67 5.30998 2.17 4.29998C2.19137 4.25297 2.21458 4.20777 2.23804 4.16206C2.26947 4.10084 2.30138 4.03868 2.33 3.96998V3.95998Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.20001 4.51998C5.15001 4.44998 5.08001 4.37998 4.99001 4.32998C4.65001 4.11998 4.27001 4.24998 4.02001 4.64998C3.44001 5.59998 3.14001 6.66998 3.14001 7.94998C3.15001 8.90998 3.44001 9.94998 4.03001 10.91C4.26001 11.29 4.64001 11.42 4.98001 11.23C5.33001 11.03 5.42001 10.63 5.19001 10.24C4.25001 8.60998 4.25001 6.96998 5.19001 5.32998C5.36001 5.02998 5.36001 4.72998 5.20001 4.51998Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24 7.77C24 6.08 22.64 4.68 20.94 4.64C20.5996 4.63333 20.2548 4.63556 19.9115 4.63779C19.7404 4.6389 19.5696 4.64 19.4 4.64H10.23C9.77999 4.64 9.67999 4.77 9.79999 5.21C10.31 6.98 10.82 8.76 11.32 10.53C11.4 10.81 11.54 10.92 11.84 10.92C12.8537 10.9133 13.8718 10.9156 14.8885 10.9178C15.3963 10.9189 15.9037 10.92 16.41 10.92C16.9137 10.92 17.4163 10.9211 17.9185 10.9222C18.9218 10.9244 19.9237 10.9267 20.93 10.92C22.65 10.9 24.01 9.49 24.01 7.78L24 7.77Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.46 12.3L21.43 12.25H16.95C16.9709 12.3169 16.9883 12.375 17.0044 12.4288C17.0268 12.5037 17.0467 12.5702 17.07 12.64C17.65 14.38 18.23 16.12 18.82 17.86C18.88 18.03 18.86 18.15 18.76 18.3C18.573 18.565 18.392 18.8421 18.2124 19.1172C18.1615 19.1951 18.1108 19.2728 18.06 19.35C17.85 19.66 17.81 19.99 18 20.33C18.18 20.65 18.47 20.79 18.84 20.79H23.07C23.74 20.79 24.17 20.22 23.96 19.59C23.14 17.16 22.31 14.74 21.48 12.32L21.46 12.3Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.11001 8.90001C7.95001 8.71001 7.83001 8.53001 7.75001 8.34001C7.51001 7.78001 7.63001 7.22001 8.12001 6.65001C8.32001 6.41001 8.36001 6.13001 8.25001 5.89001C8.21001 5.81001 8.16001 5.74001 8.09001 5.67001C7.81001 5.42001 7.42001 5.44001 7.14001 5.73001C6.58001 6.29001 6.31001 6.98001 6.29001 7.77001C6.30001 8.36001 6.46001 8.89001 6.77001 9.36001C6.87001 9.52001 6.99001 9.67001 7.13001 9.81001C7.42001 10.11 7.82001 10.13 8.10001 9.87001C8.24001 9.74001 8.31001 9.57001 8.31001 9.40001C8.31001 9.23001 8.25001 9.05001 8.11001 8.89001V8.90001Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.98 12.27C14.72 12.29 14.58 12.47 14.58 12.72V14.03C14.58 14.3 14.75 14.48 15.01 14.49C15.2023 14.4966 15.3989 14.4944 15.6 14.4922C15.7022 14.4911 15.8055 14.49 15.91 14.49V12.25C15.8022 12.25 15.6967 12.2477 15.5922 12.2455C15.3867 12.241 15.1856 12.2367 14.98 12.25V12.27Z"
      />
    </IconSVGView>
  );
};
