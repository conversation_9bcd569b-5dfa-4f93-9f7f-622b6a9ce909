import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const NotificationDisabled = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.9969 9.75V13.641L8.95486 4.59C9.4419 4.30085 9.96769 4.08265 10.5164 3.942C10.5057 3.8785 10.4992 3.81436 10.4969 3.75C10.4969 2.92157 11.1684 2.25 11.9969 2.25C12.8253 2.25 13.4969 2.92157 13.4969 3.75C13.4946 3.81436 13.488 3.8785 13.4774 3.942C16.1337 4.61836 17.9939 7.00893 17.9969 9.75ZM21.4004 19.1542L5.77561 3.51375C5.59444 3.29235 5.30309 3.19329 5.02452 3.25838C4.74594 3.32347 4.52865 3.54138 4.46435 3.82014C4.40005 4.0989 4.49994 4.38996 4.72186 4.5705L6.84436 6.69825C6.29016 7.61975 5.9972 8.67469 5.99686 9.75V15.75L4.49686 17.25L4.27711 17.4697C4.06268 17.6842 3.99854 18.0068 4.1146 18.287C4.23066 18.5672 4.50406 18.7499 4.80736 18.75H18.8841L20.3429 20.2103C20.5251 20.4272 20.8141 20.5229 21.0898 20.4576C21.3656 20.3923 21.581 20.1772 21.6466 19.9016C21.7123 19.626 21.617 19.3368 21.4004 19.1542ZM13.4969 19.5H10.1219V19.875C10.1219 20.9105 10.9613 21.75 11.9969 21.75C13.0324 21.75 13.8719 20.9105 13.8719 19.875V19.5H13.4969Z"
      />
    </IconSVGView>
  );
};
