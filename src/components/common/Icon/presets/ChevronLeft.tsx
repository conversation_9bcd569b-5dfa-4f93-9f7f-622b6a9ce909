import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const ChevronLeft = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        d="M14.7068 17.6947C15.0972 17.3042 15.0972 16.6712 14.7068 16.2807L10.4138 11.9877L14.7068 7.69471C15.0858 7.30233 15.0803 6.67863 14.6946 6.29289C14.3089 5.90716 13.6852 5.90174 13.2928 6.28071L8.29279 11.2807C7.9024 11.6712 7.9024 12.3042 8.29279 12.6947L13.2928 17.6947C13.6833 18.0851 14.3163 18.0851 14.7068 17.6947V17.6947Z"
      />
    </IconSVGView>
  );
};
