import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Checked = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5 3H19C20.1046 3 21 3.89543 21 5V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3ZM6 13.414L9.293 16.707C9.6835 17.0974 10.3165 17.0974 10.707 16.707L18 9.414C18.3904 9.0235 18.3904 8.3905 18 8C17.6095 7.60962 16.9765 7.60962 16.586 8L10 14.586L7.414 12C7.0235 11.6096 6.3905 11.6096 6 12C5.60962 12.3905 5.60962 13.0235 6 13.414Z"
      />
    </IconSVGView>
  );
};
