import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Mic = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.99 11L14.999 5C14.999 3.342 13.657 2 12 2C10.344 2 9 3.342 9 5V11C9 12.656 10.344 14 12 14C13.657 14 14.99 12.656 14.99 11ZM17.299 11C17.299 14 14.762 16.1 12 16.1C9.239 16.1 6.7 14 6.7 11H5C5 14.415 7.719 17.233 11 17.718V21H13V17.718C16.279 17.233 19 14.415 19 11H17.299Z"
      />
    </IconSVGView>
  );
};
