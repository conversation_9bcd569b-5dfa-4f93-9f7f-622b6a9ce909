import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Reehub = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7 2.27996C19.4434 2.03008 19.0728 1.9365 18.7283 2.03462C18.3839 2.13275 18.1182 2.4076 18.0318 2.75518C17.9454 3.10277 18.0516 3.47 18.31 3.71796C20.0308 5.35426 21.005 7.62487 21.005 9.99946C21.005 12.374 20.0308 14.6447 18.31 16.281C18.0516 16.5289 17.9454 16.8962 18.0318 17.2437C18.1182 17.5913 18.3839 17.8662 18.7283 17.9643C19.0728 18.0624 19.4434 17.9688 19.7 17.719C21.8106 15.7058 23.0049 12.9162 23.0049 9.99946C23.0049 7.0827 21.8106 4.29313 19.7 2.27996ZM13 17.586V11.723C13.7839 11.2704 14.1661 10.3476 13.9319 9.47327C13.6976 8.5989 12.9052 7.99091 12 7.99091C11.0948 7.99091 10.3024 8.5989 10.0681 9.47327C9.83386 10.3476 10.2161 11.2704 11 11.723V17.586L8.293 20.293C8.03304 20.544 7.92879 20.9158 8.0203 21.2655C8.11182 21.6151 8.38486 21.8881 8.73449 21.9797C9.08412 22.0712 9.45593 21.9669 9.707 21.707L12 19.414L14.293 21.707C14.6854 22.0859 15.3091 22.0805 15.6948 21.6948C16.0806 21.309 16.086 20.6853 15.707 20.293L13 17.586ZM5.7 3.71796C3.9783 5.35427 3.00246 7.62472 3 9.99996C3.00309 12.3748 3.97885 14.6447 5.7 16.281C6.09709 16.6662 6.10672 17.3004 5.7215 17.6975C5.33628 18.0946 4.70209 18.1042 4.305 17.719C2.19439 15.7058 1.00012 12.9162 1.00012 9.99946C1.00012 7.0827 2.19439 4.29313 4.305 2.27996C4.56188 2.03077 4.93218 1.93754 5.27642 2.03541C5.62067 2.13327 5.88655 2.40735 5.97392 2.75441C6.06129 3.10146 5.95688 3.46877 5.7 3.71796ZM8.666 13.273C7.69258 12.465 7.12939 11.2655 7.12939 10.0005C7.12939 8.73538 7.69258 7.53597 8.666 6.72796C8.87049 6.55308 8.99181 6.30012 9.00018 6.03118C9.00856 5.76224 8.90321 5.50223 8.71 5.31496C8.29662 4.91393 7.64518 4.89569 7.21 5.27296C5.82185 6.45293 5.01527 8.17813 5 9.99996C5.01519 11.821 5.81963 13.5459 7.205 14.728C7.64173 15.1057 8.29449 15.0879 8.71 14.687C8.90297 14.4994 9.00813 14.2393 8.99976 13.9703C8.99139 13.7013 8.87026 13.4482 8.666 13.273ZM15.295 5.31496C15.7093 4.91136 16.3639 4.89309 16.8 5.27296H16.802C18.1995 6.44547 19.0066 8.17623 19.0066 10.0005C19.0066 11.8247 18.1995 13.5554 16.802 14.728C16.3649 15.1058 15.7119 15.0881 15.296 14.687C15.103 14.4994 14.9979 14.2393 15.0062 13.9703C15.0146 13.7013 15.1357 13.4482 15.34 13.273C16.3134 12.465 16.8766 11.2655 16.8766 10.0005C16.8766 8.73538 16.3134 7.53597 15.34 6.72796C15.1354 6.55322 15.0139 6.30034 15.0053 6.0314C14.9967 5.76246 15.1019 5.50236 15.295 5.31496Z"
      />
    </IconSVGView>
  );
};
