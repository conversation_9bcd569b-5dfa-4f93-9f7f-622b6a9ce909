import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Replace = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 3C11 2.44772 11.4477 2 12 2C12.5523 2 13 2.44772 13 3C13 3.55228 12.5523 4 12 4C11.4477 4 11 3.55228 11 3ZM15 3C15 2.44772 15.4477 2 16 2H20.5C20.628 2 20.7565 2.04848 20.8535 2.14648C21.0485 2.34148 21.0485 2.65852 20.8535 2.85352L18.9101 4.79688C19.5645 5.42314 20.1378 6.13159 20.6035 6.91406C20.6654 6.99908 20.7135 7.09334 20.7461 7.19336C21.5364 8.62148 22 10.2552 22 12C22.0051 12.3606 21.8156 12.6961 21.5041 12.8779C21.1926 13.0597 20.8073 13.0597 20.4958 12.8779C20.1843 12.6961 19.9949 12.3606 20 12C20 9.71182 19.041 7.65511 17.5078 6.19922L15.8535 7.85352C15.6585 8.04852 15.3415 8.04852 15.1465 7.85352C15.0495 7.75552 15 7.628 15 7.5V3ZM7.49997 3.20508C6.94769 3.20508 6.49997 3.65279 6.49997 4.20508C6.49997 4.75736 6.94769 5.20508 7.49997 5.20508C8.05226 5.20508 8.49997 4.75736 8.49997 4.20508C8.49997 3.65279 8.05226 3.20508 7.49997 3.20508ZM3.20505 7.5C3.20505 6.94772 3.65277 6.5 4.20505 6.5C4.75734 6.5 5.20505 6.94772 5.20505 7.5C5.20505 8.05228 4.75734 8.5 4.20505 8.5C3.65277 8.5 3.20505 8.05228 3.20505 7.5ZM2.98435 10.9863C2.43283 10.9949 1.99242 11.4485 1.99997 12C1.99997 13.7448 2.46353 15.3785 3.25388 16.8066C3.28867 16.9144 3.34151 17.0154 3.41013 17.1055C3.8735 17.8799 4.44121 18.5823 5.08982 19.2031L3.14646 21.1465C2.95146 21.3415 2.95146 21.6585 3.14646 21.8535C3.24346 21.9515 3.37197 22 3.49997 22H7.99997C8.55226 22 8.99997 21.5523 8.99997 21V16.5C8.99997 16.372 8.95049 16.2445 8.85349 16.1465C8.65849 15.9515 8.34146 15.9515 8.14646 16.1465L6.49216 17.8008C4.95893 16.3449 3.99997 14.2882 3.99997 12C4.00367 11.7297 3.8978 11.4694 3.70646 11.2784C3.51513 11.0875 3.25464 10.9821 2.98435 10.9863ZM18.7949 16.5C18.7949 15.9477 19.2426 15.5 19.7949 15.5C20.3472 15.5 20.7949 15.9477 20.7949 16.5C20.7949 17.0523 20.3472 17.5 19.7949 17.5C19.2426 17.5 18.7949 17.0523 18.7949 16.5ZM16.5 18.7949C15.9477 18.7949 15.5 19.2426 15.5 19.7949C15.5 20.3472 15.9477 20.7949 16.5 20.7949C17.0523 20.7949 17.5 20.3472 17.5 19.7949C17.5 19.2426 17.0523 18.7949 16.5 18.7949ZM11 21C11 20.4477 11.4477 20 12 20C12.5523 20 13 20.4477 13 21C13 21.5523 12.5523 22 12 22C11.4477 22 11 21.5523 11 21Z"
      />
    </IconSVGView>
  );
};
