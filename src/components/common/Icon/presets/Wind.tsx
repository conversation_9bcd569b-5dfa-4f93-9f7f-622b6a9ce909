import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Wind = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.816 8.5569C17.9324 8.13077 17.9942 7.69159 18 7.2499C18.0053 4.68654 16.1539 2.49626 13.6255 2.07452C11.097 1.65279 8.63501 3.1236 7.808 5.5499C6.24709 5.65379 4.81807 6.46167 3.92424 7.74552C3.03041 9.02938 2.76871 10.65 3.213 12.1499C3.46404 12.0525 3.73073 12.0017 4 11.9999H9.587C8.84432 11.4013 8.56008 10.3992 8.8778 9.4998C9.19553 8.60037 10.0461 7.99921 11 7.9999C13.3461 8.00265 15.2472 9.90383 15.25 12.2499C15.2458 13.2614 14.8777 14.2375 14.213 14.9999H14.25C15.2416 15.0042 16.1999 15.3584 16.956 15.9999H17.25C19.2119 16.0071 20.8462 14.4979 20.995 12.5417C21.1437 10.5854 19.7564 8.84643 17.816 8.5569ZM14.25 16.4999H7.25C6.83579 16.4999 6.5 16.8357 6.5 17.2499C6.5 17.6641 6.83579 17.9999 7.25 17.9999H14.25C14.9404 17.9999 15.5 18.5595 15.5 19.2499C15.5 19.9403 14.9404 20.4999 14.25 20.4999C13.8358 20.4999 13.5 20.8357 13.5 21.2499C13.5 21.6641 13.8358 21.9999 14.25 21.9999C15.7688 21.9999 17 20.7687 17 19.2499C17 17.7311 15.7688 16.4999 14.25 16.4999ZM13.75 12.2499C13.75 13.7687 12.5188 14.9999 11 14.9999H4C3.80109 14.9999 3.61032 14.9209 3.46967 14.7802C3.32902 14.6396 3.25 14.4488 3.25 14.2499C3.25287 13.9106 3.48617 13.6167 3.816 13.5369C3.87542 13.5164 3.93729 13.504 4 13.4999H11C11.6904 13.4999 12.25 12.9403 12.25 12.2499C12.25 11.5595 11.6904 10.9999 11 10.9999C10.5858 10.9999 10.25 10.6641 10.25 10.2499C10.25 9.83568 10.5858 9.4999 11 9.4999C12.5188 9.4999 13.75 10.7311 13.75 12.2499Z"
      />
    </IconSVGView>
  );
};
