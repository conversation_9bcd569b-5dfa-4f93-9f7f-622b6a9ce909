import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Beecon = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.027 2.26C11.3231 2.08998 11.6585 2.00034 12 2C12.3406 2.00286 12.6746 2.09455 12.969 2.266L19.969 6.266C20.6096 6.6387 21.0026 7.32489 21 8.066V15.934C21.0043 16.6735 20.6145 17.3593 19.977 17.734L12.977 21.734C12.3746 22.0806 11.6334 22.0806 11.031 21.734L4.031 17.734C3.39041 17.3613 2.99738 16.6751 3 15.934V8.066C2.9944 7.32357 3.38609 6.63478 4.027 6.26L11.027 2.26ZM18.976 8L12 4.007L5.024 8C5.00729 8.01778 4.99861 8.04164 5 8.066V15.934C4.99753 15.9578 5.00473 15.9816 5.02 16L12.02 20L18.976 16.002C18.9932 15.9837 19.002 15.959 19 15.934V8.066C19.0014 8.04164 18.9927 8.01778 18.976 8Z"
      />
    </IconSVGView>
  );
};
