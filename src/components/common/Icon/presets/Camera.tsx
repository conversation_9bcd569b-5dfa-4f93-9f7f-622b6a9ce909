import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Camera = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.1191 2.99805L9.87695 3.00195C9.31795 3.00195 8.7833 3.23644 8.4043 3.64844L7.16406 5H4C2.9 5 2 5.9 2 7V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V7C22 5.9 21.1 5 20 5H16.8418L15.5938 3.64453C15.2148 3.23253 14.6791 2.99805 14.1191 2.99805ZM12 8C14.8 8 17 10.2 17 13C17 15.8 14.8 18 12 18C9.2 18 7 15.8 7 13C7 10.2 9.2 8 12 8ZM9 13C9 11.3431 10.3431 10 12 10C13.6569 10 15 11.3431 15 13C15 14.6569 13.6569 16 12 16C10.3431 16 9 14.6569 9 13Z"
      />
    </IconSVGView>
  );
};
