import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const ReplaceID = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 3C11 2.44772 11.4477 2 12 2C12.2652 2 12.5196 2.10536 12.7071 2.29289C12.8946 2.48043 13 2.73478 13 3C13 3.55228 12.5523 4 12 4C11.4477 4 11 3.55228 11 3ZM15 7.5V3C15 2.44772 15.4477 2 16 2H20.5C20.6329 2.00015 20.7602 2.05387 20.853 2.149C20.947 2.2428 20.9999 2.37017 20.9999 2.503C20.9999 2.63583 20.947 2.7632 20.853 2.857L18.91 4.8C19.5672 5.42498 20.1367 6.13612 20.603 6.914C20.6649 6.99899 20.7131 7.09312 20.746 7.193C21.5658 8.66271 21.9974 10.3171 22 12V12.028C21.9923 12.5803 21.5383 13.0217 20.986 13.014C20.4337 13.0063 19.9923 12.5523 20 12C20.0027 9.80616 19.1013 7.70815 17.508 6.2L15.853 7.854C15.7099 7.99666 15.495 8.03916 15.3083 7.9617C15.1217 7.88425 15 7.70207 15 7.5ZM7.5 3.205C6.94772 3.205 6.5 3.65272 6.5 4.205C6.5 4.75728 6.94772 5.205 7.5 5.205C8.05229 5.205 8.5 4.75728 8.5 4.205C8.5 3.65272 8.05229 3.205 7.5 3.205ZM3.205 7.5C3.205 6.94772 3.65272 6.5 4.205 6.5C4.75729 6.5 5.205 6.94772 5.205 7.5C5.205 8.05228 4.75729 8.5 4.205 8.5C3.65272 8.5 3.205 8.05228 3.205 7.5ZM2.984 10.986C2.43248 10.9948 1.99221 11.4485 2 12C2.00264 13.6829 2.43423 15.3373 3.254 16.807C3.289 16.915 3.34172 17.0163 3.41 17.107C3.8734 17.8791 4.43846 18.5854 5.09 19.207L3.147 21.146C3.05296 21.2398 3.00011 21.3672 3.00011 21.5C3.00011 21.6328 3.05296 21.7602 3.147 21.854C3.24033 21.948 3.36752 22.0006 3.5 22H8C8.55229 22 9 21.5523 9 21V16.5C8.99996 16.2979 8.87829 16.1158 8.69166 16.0383C8.50502 15.9608 8.29011 16.0033 8.147 16.146L6.492 17.8C4.89875 16.2919 3.99733 14.1938 4 12C4.00374 11.7348 3.90195 11.4789 3.71704 11.2888C3.53212 11.0986 3.27923 10.9897 3.014 10.986H2.984ZM18.8 16.5C18.8 15.9477 19.2477 15.5 19.8 15.5C20.3523 15.5 20.8 15.9477 20.8 16.5C20.8 17.0523 20.3523 17.5 19.8 17.5C19.2477 17.5 18.8 17.0523 18.8 16.5ZM16.5 18.8C15.9477 18.8 15.5 19.2477 15.5 19.8C15.5 20.3523 15.9477 20.8 16.5 20.8C17.0523 20.8 17.5 20.3523 17.5 19.8C17.5 19.2477 17.0523 18.8 16.5 18.8ZM11 21C11 20.4477 11.4477 20 12 20C12.5523 20 13 20.4477 13 21C13 21.5523 12.5523 22 12 22C11.4477 22 11 21.5523 11 21Z"
      />
    </IconSVGView>
  );
};
