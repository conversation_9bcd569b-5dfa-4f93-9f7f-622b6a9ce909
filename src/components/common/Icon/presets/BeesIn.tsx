import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const BeesIn = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        d="M9.03674 4.41263C9.0721 4.30615 9.09125 4.19226 9.09125 4.07391C9.09125 3.4808 8.61044 3 8.01734 3C7.42424 3 6.94344 3.4808 6.94344 4.07391C6.94344 4.66701 7.42424 5.14781 8.01734 5.14781C8.09155 5.14781 8.164 5.14028 8.23396 5.12595L9.25508 6.27476C8.7191 6.87329 8.39321 7.6639 8.39321 8.53079C8.39321 8.57374 8.39857 8.61669 8.40394 8.65964C8.40931 8.7026 8.41468 8.74557 8.41468 8.78852C6.84678 7.9294 5.09632 7.45688 3.31363 7.40318C1.76721 7.40318 0.5 8.67039 0.5 10.2168C0.5 11.7632 1.76721 13.0304 3.31363 13.0304C4.89227 12.9875 6.44944 12.6116 7.86699 11.9028H15.6743C15.8508 11.9911 16.0294 12.0742 16.21 12.1521C16.6238 12.0527 17.0557 12 17.5 12C18.6758 12 19.7655 12.369 20.6595 12.9975C22.0107 12.7889 23.0413 11.6174 23.0413 10.2168C23.0413 8.67039 21.7741 7.40318 20.2276 7.40318C18.445 7.45688 16.6945 7.9294 15.1266 8.78852C15.1266 8.74557 15.132 8.70263 15.1373 8.65968C15.1427 8.61673 15.1481 8.57373 15.1481 8.53079C15.1481 7.6012 14.7733 6.75932 14.1666 6.14794L15.075 5.12595C15.145 5.14028 15.2175 5.14781 15.2917 5.14781C15.8848 5.14781 16.3656 4.66701 16.3656 4.07391C16.3656 3.4808 15.8848 3 15.2917 3C14.6986 3 14.2178 3.4808 14.2178 4.07391C14.2178 4.19226 14.2369 4.30614 14.2723 4.41263L13.2946 5.51249C12.8369 5.28013 12.3191 5.14884 11.7706 5.14799L11.776 5.14798H11.7653L11.7706 5.14799C11.1586 5.14894 10.5847 5.31234 10.0898 5.59738L9.03674 4.41263Z"
      />
      <path
        fill={useColor(color)}
        d="M7.28708 14.1688H13.1232C12.6279 14.8186 12.2739 15.5821 12.1073 16.4133H7.43743C7.3193 15.8656 7.26561 15.2964 7.26561 14.7272C7.26561 14.5447 7.27634 14.3514 7.28708 14.1688Z"
      />
      <path
        fill={useColor(color)}
        d="M8.29656 18.6792H12.1267C12.2863 19.4096 12.5909 20.0857 13.009 20.6758C12.6192 20.821 12.2044 20.9096 11.776 20.9344C10.294 20.8485 8.97311 20.0001 8.29656 18.6792Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5 13C19.9853 13 22 15.0147 22 17.5C22 19.9853 19.9853 22 17.5 22C15.0147 22 13 19.9853 13 17.5C13 15.0147 15.0147 13 17.5 13ZM14.647 17.854L16.647 19.854C16.8423 20.0492 17.1588 20.0492 17.354 19.854C17.5492 19.6588 17.5492 19.3423 17.354 19.147L16.207 18H19.5C19.7761 18 20 17.7761 20 17.5C20 17.2239 19.7761 17 19.5 17H16.208L17.354 15.854C17.484 15.7285 17.5361 15.5426 17.4903 15.3677C17.4446 15.1929 17.3081 15.0564 17.1333 15.0107C16.9584 14.9649 16.7725 15.017 16.647 15.147L14.647 17.147C14.4518 17.3422 14.4518 17.6588 14.647 17.854Z"
      />
    </IconSVGView>
  );
};
