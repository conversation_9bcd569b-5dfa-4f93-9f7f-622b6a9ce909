import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Introduced = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.316 4.94853C8.21433 4.9836 8.10754 5.00151 8 5.00153C7.56594 5.0023 7.18094 4.72295 7.04702 4.31006C6.9131 3.89717 7.06084 3.44503 7.41272 3.19088C7.7646 2.93672 8.24025 2.93859 8.59012 3.19551C8.93999 3.45243 9.08416 3.90571 8.947 4.31753L11 5.00153L11.553 3.89453C11.1381 3.68721 10.9202 3.22179 11.0267 2.77036C11.1332 2.31894 11.5362 2 12 2C12.4638 2 12.8668 2.31894 12.9733 2.77036C13.0798 3.22179 12.8619 3.68721 12.447 3.89453L13 5.00153L15.051 4.31753C14.9139 3.90588 15.0579 3.45279 15.4075 3.19581C15.7571 2.93882 16.2325 2.9366 16.5845 3.19029C16.9365 3.44399 17.0847 3.89572 16.9514 4.30863C16.8182 4.72155 16.4339 5.00143 16 5.00153C15.8925 5.00151 15.7857 4.9836 15.684 4.94853L15.229 6.31753C15.0928 6.72643 14.71 7.00207 14.279 7.00153H9.721C9.29002 7.00207 8.90719 6.72643 8.771 6.31753L8.316 4.94853ZM13 17.5015C13 15.0163 15.0147 13.0015 17.5 13.0015C19.9853 13.0015 22 15.0163 22 17.5015C22 19.9868 19.9853 22.0015 17.5 22.0015C15.0147 22.0015 13 19.9868 13 17.5015ZM12.607 20.0015H8.914C9.5151 21.1684 10.6894 21.9294 12 22.0015C12.6083 21.9966 13.2018 21.8134 13.707 21.4745C13.2605 21.0485 12.8887 20.5506 12.607 20.0015ZM12.213 16.0015C12.0727 16.4892 12.001 16.9941 12 17.5015C12 17.6705 12.01 17.8365 12.025 18.0015H8.154C8.05206 17.508 8.00046 17.0055 8 16.5015C8 16.3325 8.008 16.1665 8.018 16.0015H12.213ZM12 14.0015H13.261C14.2804 12.7623 15.7913 12.0316 17.3957 12.0022C19.0001 11.9728 20.5369 12.6475 21.601 13.8485C22.097 13.0805 22.133 12.1026 21.6947 11.3001C21.2565 10.4977 20.4143 9.99937 19.5 10.0015C17.9162 10.0474 16.3659 10.469 14.977 11.2315C14.9794 11.2014 14.9834 11.1716 14.9874 11.1419L14.9874 11.1418L14.9874 11.1418C14.9937 11.0957 15 11.0495 15 11.0015C15 9.34468 13.6569 8.00153 12 8.00153C10.3431 8.00153 9 9.34468 9 11.0015C9 11.0496 9.00628 11.0957 9.01255 11.1418C9.0166 11.1716 9.02065 11.2013 9.023 11.2315C7.63407 10.469 6.08382 10.0474 4.5 10.0015C3.11929 10.0015 2 11.1208 2 12.5015C2 13.8822 3.11929 15.0015 4.5 15.0015C5.90201 14.9678 7.27949 14.6264 8.535 14.0015H12ZM17 15.5C17 15.2239 17.2239 15 17.5 15C17.7761 15 18 15.2239 18 15.5V17H19.5C19.7761 17 20 17.2239 20 17.5C20 17.7761 19.7761 18 19.5 18H17.5C17.2239 18 17 17.7761 17 17.5V15.5Z"
      />
    </IconSVGView>
  );
};

export const Queenright = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.76 20.4C9.3911 21.6251 10.624 22.4242 12 22.5C13.376 22.4242 14.6089 21.6251 15.24 20.4H8.76ZM7.818 16.2C7.8075 16.3725 7.8 16.548 7.8 16.725C7.80027 17.2541 7.85455 17.7819 7.962 18.3H16.038C16.1454 17.7819 16.1997 17.2541 16.2 16.725C16.2 16.548 16.1925 16.3725 16.182 16.2H7.818ZM12 7.8C10.2603 7.8 8.85 9.2103 8.85 10.95C8.85 11.0325 8.868 11.1105 8.874 11.1915C7.41582 10.3905 5.78802 9.9478 4.125 9.9C2.67525 9.9 1.5 11.0753 1.5 12.525C1.5 13.9747 2.67525 15.15 4.125 15.15C5.59733 15.1144 7.0439 14.756 8.3625 14.1H15.6375C16.9561 14.756 18.4027 15.1144 19.875 15.15C21.3247 15.15 22.5 13.9747 22.5 12.525C22.5 11.0753 21.3247 9.9 19.875 9.9C18.212 9.9478 16.5842 10.3905 15.126 11.1915C15.126 11.1105 15.15 11.0325 15.15 10.95C15.15 9.2103 13.7397 7.8 12 7.8ZM12 1.5C11.5132 1.50017 11.0903 1.83496 10.9785 2.30878C10.8666 2.78259 11.0951 3.27114 11.5305 3.489L10.95 4.65L8.7945 3.9315C8.94892 3.4695 8.7653 2.96196 8.35103 2.70569C7.93676 2.44943 7.40064 2.51174 7.05619 2.85619C6.71174 3.20064 6.64943 3.73676 6.90569 4.15103C7.16196 4.5653 7.6695 4.74892 8.1315 4.5945L8.61 6.0315C8.75202 6.45834 9.15016 6.7473 9.6 6.75H14.4C14.8521 6.74987 15.2533 6.46043 15.396 6.0315L15.8745 4.5945C15.9793 4.63025 16.0892 4.64899 16.2 4.65C16.6556 4.65013 17.0593 4.35644 17.1994 3.92292C17.3395 3.4894 17.1841 3.01499 16.8146 2.74845C16.4451 2.4819 15.9459 2.48405 15.5787 2.75376C15.2115 3.02348 15.0602 3.4992 15.204 3.9315L13.05 4.65L12.4695 3.4875C12.9031 3.26879 13.1302 2.78125 13.0185 2.3086C12.9069 1.83595 12.4857 1.50155 12 1.5Z"
      />
    </IconSVGView>
  );
};

export const Queenless = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.279 7.00007C14.71 7.0006 15.0928 6.72496 15.229 6.31607L15.684 4.94707C15.7857 4.98213 15.8925 5.00004 16 5.00007C16.4339 4.99996 16.8182 4.72008 16.9514 4.30717C17.0847 3.89425 16.9364 3.44252 16.5845 3.18883C16.2325 2.93513 15.7571 2.93736 15.4075 3.19434C15.0579 3.45132 14.9139 3.90442 15.051 4.31607L13 5.00007L12.447 3.89307C12.8619 3.68574 13.0798 3.22033 12.9733 2.7689C12.8668 2.31747 12.4638 1.99854 12 1.99854C11.5362 1.99854 11.1332 2.31747 11.0267 2.7689C10.9202 3.22033 11.1381 3.68574 11.553 3.89307L11 5.00007L8.947 4.31607C9.04846 4.01143 8.99747 3.6766 8.80993 3.41597C8.62238 3.15535 8.32109 3.00063 8 3.00007C7.69122 3.00305 7.40187 3.15125 7.219 3.40007L10.819 7.00007H14.279ZM12 8.00007C11.9675 8.00007 11.9362 8.00447 11.905 8.00886L11.905 8.00886C11.8805 8.01231 11.856 8.01575 11.831 8.01707L14.983 11.1711C14.9922 11.1145 14.9979 11.0574 15 11.0001C15 9.34321 13.6569 8.00007 12 8.00007ZM2.002 2.42107C2.39357 2.0306 3.02753 2.0315 3.418 2.42307L21.148 20.1631C21.5385 20.5538 21.5382 21.1871 21.1475 21.5776C20.7568 21.968 20.1235 21.9678 19.733 21.5771L15.888 17.7301C15.882 17.7659 15.8769 17.8024 15.8718 17.839L15.8718 17.839L15.8718 17.8391C15.8643 17.8933 15.8567 17.9476 15.846 18.0001H8.154C8.05206 17.5066 8.00046 17.004 8 16.5001C8 16.3311 8.008 16.1651 8.018 16.0001H14.16L12.16 14.0001H8.535C7.27948 14.6249 5.90201 14.9663 4.5 15.0001C3.11929 15.0001 2 13.8808 2 12.5001C2 11.1194 3.11929 10.0001 4.5 10.0001C6.0828 10.0464 7.63197 10.468 9.02 11.2301C9.01764 11.1999 9.0136 11.1701 9.00955 11.1404C9.00328 11.0943 8.997 11.0481 8.997 11.0001C8.997 10.9769 9.00063 10.9531 9.00432 10.9288L9.00432 10.9288C9.00813 10.9038 9.012 10.8785 9.012 10.8531L2 3.83707C1.60953 3.4455 1.61043 2.81153 2.002 2.42107ZM12 22.0001C13.3106 21.928 14.4849 21.1669 15.086 20.0001H8.914C9.51509 21.1669 10.6894 21.928 12 22.0001ZM19.5 15.0001C19.2509 14.9945 19.0022 14.9761 18.755 14.9451L15.02 11.2091C16.3973 10.4589 17.9323 10.0446 19.5 10.0001C20.8807 10.0001 22 11.1194 22 12.5001C22 13.8808 20.8807 15.0001 19.5 15.0001Z"
      />
    </IconSVGView>
  );
};

export const Deadout = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 5H21C21.5523 5 22 5.44772 22 6C22 6.55228 21.5523 7 21 7H20V8C20 9.65685 18.6569 11 17 11C15.3431 11 14 9.65685 14 8V7H13C12.4477 7 12 6.55228 12 6C12 5.44772 12.4477 5 13 5H14V4C14 2.34315 15.3431 1 17 1C18.6569 1 20 2.34315 20 4V5ZM17 9C17.5523 9 18 8.55228 18 8V7H16V8C16 8.55228 16.4477 9 17 9ZM18 5H16V4C16 3.44772 16.4477 3 17 3C17.5523 3 18 3.44772 18 4V5ZM3.418 2.423C3.02753 2.03143 2.39357 2.03053 2.002 2.421C1.61043 2.81147 1.60953 3.44543 2 3.837L5.555 7.392C4.60251 7.91548 4.00764 8.91317 4 10V11H3C2.44771 11 2 11.4477 2 12C2 12.5523 2.44771 13 3 13H4V14C4 15.6569 5.34314 17 7 17C8.65685 17 10 15.6569 10 14V13H11C11.0452 12.994 11.09 12.985 11.134 12.973L14.015 15.856C14.015 15.8812 14.011 15.9068 14.0072 15.9318L14.0072 15.9318L14.0072 15.9319C14.0035 15.9555 14 15.9786 14 16V17H13C12.4477 17 12 17.4477 12 18C12 18.5523 12.4477 19 13 19H14V20C13.9937 21.3667 14.9158 22.5633 16.239 22.9056C17.5622 23.2478 18.9488 22.6483 19.606 21.45L19.733 21.577C20.1235 21.9677 20.7568 21.968 21.1475 21.5775C21.5382 21.187 21.5385 20.5537 21.148 20.163L3.418 2.423ZM8 14C8 14.5523 7.55228 15 7 15C6.44771 15 6 14.5523 6 14V13H8V14ZM8 11V10C7.99311 9.93179 7.97938 9.86445 7.959 9.799L7.2 9.039C7.13476 9.01952 7.06777 9.00646 7 9C6.73347 8.99563 6.47656 9.09958 6.28807 9.28807C6.09958 9.47657 5.99563 9.73347 6 10V11H8ZM18 20C18 20.5523 17.5523 21 17 21C16.4477 21 16 20.5523 16 20V19H17.157L18 19.843V20Z"
      />
    </IconSVGView>
  );
};

export const Retired = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.1564 12.5279C13.2308 13.4349 12.0014 15.3715 12 17.5C11.9991 18.7781 12.4452 20.0162 13.261 21H5C3.89883 20.9918 3.00817 20.1012 3 19V7C3.00063 6.82415 3.04791 6.65161 3.137 6.5L4.887 3.5C5.06509 3.19153 5.39382 3.00108 5.75 3H18.25C18.6062 3.00108 18.9349 3.19153 19.113 3.5L20.863 6.5C20.9521 6.65161 20.9994 6.82415 21 7V13.261C19.3581 11.9065 17.082 11.621 15.1564 12.5279ZM17.676 5H6.324L5.158 7H18.842L17.676 5ZM9 10C9 9.44772 9.44772 9 10 9H14C14.5523 9 15 9.44772 15 10C15 10.5523 14.5523 11 14 11H10C9.44772 11 9 10.5523 9 10ZM17.5 13C15.0147 13 13 15.0147 13 17.5C13 19.9853 15.0147 22 17.5 22C19.9853 22 22 19.9853 22 17.5C22 15.0147 19.9853 13 17.5 13ZM15.5 18C15.2239 18 15 17.7761 15 17.5C15 17.2239 15.2239 17 15.5 17H19.5C19.7761 17 20 17.2239 20 17.5C20 17.7761 19.7761 18 19.5 18H15.5Z"
      />
    </IconSVGView>
  );
};

export const hiveStatusIcon: Record<HiveStatus, ({ color, size }: IconSVGProps) => JSX.Element> = {
  introduced: Introduced,
  queenright: Queenright,
  queenless: Queenless,
  deadout: Deadout,
  retired: Retired,
  removed: Retired,
};
