import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Lastpage = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 7C18 6.44772 17.5523 6 17 6C16.4477 6 16 6.44772 16 7V17C16 17.5523 16.4477 18 17 18C17.5523 18 18 17.5523 18 17V7ZM10.181 12L6.291 8.11C5.91048 7.71718 5.91003 7.09336 6.29 6.7C6.68004 6.31228 7.30997 6.31228 7.7 6.7L12.29 11.3C12.6777 11.69 12.6777 12.32 12.29 12.71L7.7 17.3C7.30292 17.6368 6.71388 17.6125 6.34585 17.2442C5.97782 16.8759 5.95396 16.2868 6.291 15.89L10.181 12Z"
      />
    </IconSVGView>
  );
};
