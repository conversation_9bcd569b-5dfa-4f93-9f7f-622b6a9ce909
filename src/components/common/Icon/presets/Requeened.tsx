import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Requeened = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 5.00153C8.10754 5.00151 8.21433 4.9836 8.316 4.94853L8.771 6.31753C8.90719 6.72643 9.29002 7.00207 9.721 7.00153H14.279C14.71 7.00207 15.0928 6.72643 15.229 6.31753L15.684 4.94853C15.7857 4.9836 15.8925 5.00151 16 5.00153C16.4339 5.00143 16.8182 4.72155 16.9514 4.30863C17.0847 3.89572 16.9365 3.44399 16.5845 3.19029C16.2325 2.9366 15.7571 2.93882 15.4075 3.19581C15.0579 3.45279 14.9139 3.90588 15.051 4.31753L13 5.00153L12.447 3.89453C12.8619 3.68721 13.0798 3.22179 12.9733 2.77036C12.8668 2.31894 12.4638 2 12 2C11.5362 2 11.1332 2.31894 11.0267 2.77036C10.9202 3.22179 11.1381 3.68721 11.553 3.89453L11 5.00153L8.947 4.31753C9.08416 3.90571 8.93999 3.45243 8.59012 3.19551C8.24025 2.93859 7.7646 2.93672 7.41272 3.19088C7.06084 3.44503 6.9131 3.89717 7.04702 4.31006C7.18094 4.72295 7.56594 5.0023 8 5.00153ZM13 17.5015C13 15.0163 15.0147 13.0015 17.5 13.0015C19.9853 13.0015 22 15.0163 22 17.5015C22 19.9868 19.9853 22.0015 17.5 22.0015C15.0147 22.0015 13 19.9868 13 17.5015ZM18 18.0015H19.5C19.7761 18.0015 20 17.7777 20 17.5015C20 17.2254 19.7761 17.0015 19.5 17.0015H18V15.5015C18 15.2254 17.7761 15.0015 17.5 15.0015C17.2239 15.0015 17 15.2254 17 15.5015V17.0015H15.5C15.2239 17.0015 15 17.2254 15 17.5015C15 17.7777 15.2239 18.0015 15.5 18.0015H17V19.5015C17 19.7777 17.2239 20.0015 17.5 20.0015C17.7761 20.0015 18 19.7777 18 19.5015V18.0015ZM8.914 20.0015H12.607C12.8887 20.5506 13.2605 21.0485 13.707 21.4745C13.2018 21.8134 12.6083 21.9966 12 22.0015C10.6894 21.9294 9.5151 21.1684 8.914 20.0015ZM12 17.5015C12.001 16.9941 12.0727 16.4892 12.213 16.0015H8.018C8.008 16.1665 8 16.3325 8 16.5015C8.00046 17.0055 8.05206 17.508 8.154 18.0015H12.025C12.01 17.8365 12 17.6705 12 17.5015ZM13.261 14.0015H12H8.535C7.27949 14.6264 5.90201 14.9678 4.5 15.0015C3.11929 15.0015 2 13.8822 2 12.5015C2 11.1208 3.11929 10.0015 4.5 10.0015C6.08382 10.0474 7.63407 10.469 9.023 11.2315C9.02065 11.2013 9.0166 11.1716 9.01255 11.1418C9.00628 11.0957 9 11.0496 9 11.0015C9 9.34468 10.3431 8.00153 12 8.00153C13.6569 8.00153 15 9.34468 15 11.0015C15 11.0496 14.9937 11.0957 14.9874 11.1418C14.9834 11.1716 14.9794 11.2013 14.977 11.2315C16.3659 10.469 17.9162 10.0474 19.5 10.0015C20.4143 9.99937 21.2565 10.4977 21.6947 11.3001C22.133 12.1026 22.097 13.0805 21.601 13.8485C20.5369 12.6475 19.0001 11.9728 17.3957 12.0022C15.7913 12.0316 14.2804 12.7623 13.261 14.0015Z"
      />
    </IconSVGView>
  );
};
