import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Snow = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 6.2499C17.9942 6.69159 17.9324 7.13077 17.816 7.5569C19.7564 7.84643 21.1437 9.58543 20.995 11.5417C20.8462 13.4979 19.2119 15.0071 17.25 14.9999H12.9C13.298 14.304 13.296 13.4489 12.8948 12.7549C12.4936 12.0608 11.7537 11.6323 10.952 11.6299C10.5509 10.9318 9.80714 10.5014 9.002 10.5014C8.19686 10.5014 7.45314 10.9318 7.052 11.6299C6.24853 11.6308 5.50616 12.0589 5.103 12.7539C4.93851 13.043 4.84079 13.3652 4.817 13.6969C3.21848 12.3255 2.60312 10.1264 3.25773 8.12451C3.91233 6.12263 5.70805 4.71194 7.808 4.5499C8.63501 2.1236 11.097 0.652787 13.6255 1.07452C16.1539 1.49626 18.0052 3.68654 18 6.2499ZM11.324 15.4749L10.5 14.9999L11.322 14.5249C11.5542 14.3909 11.6973 14.1432 11.6974 13.8751C11.6975 13.607 11.5546 13.3593 11.3224 13.2251C11.0903 13.091 10.8042 13.0909 10.572 13.2249L9.75 13.6999V12.7499C9.75 12.3357 9.41421 11.9999 9 11.9999C8.58579 11.9999 8.25 12.3357 8.25 12.7499V13.6999L7.427 13.2249C7.19478 13.0909 6.90872 13.091 6.67658 13.2251C6.44445 13.3593 6.3015 13.607 6.30158 13.8751C6.30167 14.1432 6.44478 14.3909 6.677 14.5249L7.5 14.9999L6.675 15.4759C6.31736 15.6835 6.1945 16.141 6.4 16.4999C6.46231 16.6026 6.54884 16.6884 6.652 16.7499C6.6811 16.766 6.71116 16.7804 6.742 16.7929C6.81987 16.8289 6.90348 16.8509 6.989 16.8579C7.00013 16.8579 7.01049 16.862 7.02073 16.866C7.03073 16.87 7.04062 16.8739 7.051 16.8739C7.06296 16.8739 7.06908 16.871 7.07435 16.8685C7.07834 16.8666 7.08183 16.8649 7.087 16.8649C7.20504 16.8623 7.32068 16.831 7.424 16.7739L8.25 16.2999V17.2509C8.25 17.6651 8.58579 18.0009 9 18.0009C9.41421 18.0009 9.75 17.6651 9.75 17.2509V16.2999L10.574 16.7759C10.6774 16.8327 10.793 16.8636 10.911 16.8659C10.917 16.8659 10.923 16.8681 10.929 16.8704C10.935 16.8726 10.941 16.8749 10.947 16.8749C10.9526 16.8749 10.9616 16.8719 10.9718 16.8685C10.9837 16.8645 10.9972 16.86 11.009 16.8589C11.0946 16.8524 11.1783 16.8304 11.256 16.7939C11.2868 16.7814 11.3169 16.767 11.346 16.7509C11.4494 16.6897 11.536 16.6038 11.598 16.5009C11.8056 16.1419 11.6829 15.6826 11.324 15.4749ZM18.324 20.4749L17.5 19.9999L18.322 19.5249C18.5542 19.3909 18.6973 19.1432 18.6974 18.8751C18.6975 18.607 18.5546 18.3593 18.3224 18.2251C18.0903 18.091 17.8042 18.0909 17.572 18.2249L16.749 18.7009V17.7499C16.749 17.3357 16.4132 16.9999 15.999 16.9999C15.5848 16.9999 15.249 17.3357 15.249 17.7499V18.6999L14.426 18.2249C14.067 18.0178 13.6081 18.1409 13.401 18.4999C13.1939 18.8589 13.317 19.3178 13.676 19.5249L14.5 19.9999L13.675 20.4759C13.4428 20.6099 13.2997 20.8576 13.2996 21.1257C13.2995 21.3938 13.4424 21.6415 13.6746 21.7757C13.9067 21.9098 14.1928 21.9099 14.425 21.7759L15.249 21.3009V22.2519C15.249 22.6661 15.5848 23.0019 15.999 23.0019C16.4132 23.0019 16.749 22.6661 16.749 22.2519V21.2999L17.573 21.7759C17.8052 21.9099 18.0913 21.9098 18.3234 21.7757C18.5556 21.6415 18.6985 21.3938 18.6984 21.1257C18.6983 20.8576 18.5552 20.6099 18.323 20.4759L18.324 20.4749Z"
      />
    </IconSVGView>
  );
};
