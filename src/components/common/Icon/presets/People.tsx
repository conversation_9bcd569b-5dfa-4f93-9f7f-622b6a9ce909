import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const People = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        d="M10.6747 7.70951C10.6747 10.0022 8.81612 11.8608 6.52343 11.8608C4.23075 11.8608 2.37216 10.0022 2.37216 7.70951C2.37216 5.41682 4.23075 3.55824 6.52343 3.55824C8.81612 3.55824 10.6747 5.41682 10.6747 7.70951Z"
        fill={useColor(color)}
      />
      <path
        d="M18.9773 4.15127C18.9773 6.44396 17.1187 8.30255 14.826 8.30255C12.5333 8.30255 10.6747 6.44396 10.6747 4.15127C10.6747 1.85859 12.5333 0 14.826 0C17.1187 0 18.9773 1.85859 18.9773 4.15127Z"
        fill={useColor(color)}
      />
      <path
        d="M13.5588 15.419C12.5984 14.0041 11.2025 12.9091 9.56351 12.3266C10.8222 10.6061 12.8562 9.48863 15.1513 9.48863C18.6368 9.48863 21.5204 12.0661 22 15.419H13.5588Z"
        fill={useColor(color)}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 18.9773H13.6974C13.2178 15.6243 10.3343 13.0469 6.84873 13.0469C3.36316 13.0469 0.4796 15.6243 0 18.9773Z"
        fill={useColor(color)}
      />
    </IconSVGView>
  );
};
