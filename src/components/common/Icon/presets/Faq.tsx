import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Faq = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17 4V11C17 12.1 16.1 13 15 13H6L2 17V4C2 2.9 2.9 2 4 2H15C16.1 2 17 2.9 17 4ZM19 6H20C21.1 6 22 6.9 22 8V22L18 18H8C6.9 18 6 17.1 6 16V15H18C18.55 15 19 14.55 19 14V6Z"
      />
    </IconSVGView>
  );
};
