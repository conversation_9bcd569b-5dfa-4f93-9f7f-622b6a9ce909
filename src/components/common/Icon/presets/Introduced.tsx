import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Introduced = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.316 4.94853C8.21433 4.9836 8.10754 5.00151 8 5.00153C7.56594 5.0023 7.18094 4.72295 7.04702 4.31006C6.9131 3.89717 7.06084 3.44503 7.41272 3.19088C7.7646 2.93672 8.24025 2.93859 8.59012 3.19551C8.93999 3.45243 9.08416 3.90571 8.947 4.31753L11 5.00153L11.553 3.89453C11.1381 3.68721 10.9202 3.22179 11.0267 2.77036C11.1332 2.31894 11.5362 2 12 2C12.4638 2 12.8668 2.31894 12.9733 2.77036C13.0798 3.22179 12.8619 3.68721 12.447 3.89453L13 5.00153L15.051 4.31753C14.9139 3.90588 15.0579 3.45279 15.4075 3.19581C15.7571 2.93882 16.2325 2.9366 16.5845 3.19029C16.9365 3.44399 17.0847 3.89572 16.9514 4.30863C16.8182 4.72155 16.4339 5.00143 16 5.00153C15.8925 5.00151 15.7857 4.9836 15.684 4.94853L15.229 6.31753C15.0928 6.72643 14.71 7.00207 14.279 7.00153H9.721C9.29002 7.00207 8.90719 6.72643 8.771 6.31753L8.316 4.94853ZM13 17.5015C13 15.0163 15.0147 13.0015 17.5 13.0015C19.9853 13.0015 22 15.0163 22 17.5015C22 19.9868 19.9853 22.0015 17.5 22.0015C15.0147 22.0015 13 19.9868 13 17.5015ZM12.607 20.0015H8.914C9.5151 21.1684 10.6894 21.9294 12 22.0015C12.6083 21.9966 13.2018 21.8134 13.707 21.4745C13.2605 21.0485 12.8887 20.5506 12.607 20.0015ZM12.213 16.0015C12.0727 16.4892 12.001 16.9941 12 17.5015C12 17.6705 12.01 17.8365 12.025 18.0015H8.154C8.05206 17.508 8.00046 17.0055 8 16.5015C8 16.3325 8.008 16.1665 8.018 16.0015H12.213ZM12 14.0015H13.261C14.2804 12.7623 15.7913 12.0316 17.3957 12.0022C19.0001 11.9728 20.5369 12.6475 21.601 13.8485C22.097 13.0805 22.133 12.1026 21.6947 11.3001C21.2565 10.4977 20.4143 9.99937 19.5 10.0015C17.9162 10.0474 16.3659 10.469 14.977 11.2315C14.9794 11.2014 14.9834 11.1716 14.9874 11.1419L14.9874 11.1418L14.9874 11.1418C14.9937 11.0957 15 11.0495 15 11.0015C15 9.34468 13.6569 8.00153 12 8.00153C10.3431 8.00153 9 9.34468 9 11.0015C9 11.0496 9.00628 11.0957 9.01255 11.1418C9.0166 11.1716 9.02065 11.2013 9.023 11.2315C7.63407 10.469 6.08382 10.0474 4.5 10.0015C3.11929 10.0015 2 11.1208 2 12.5015C2 13.8822 3.11929 15.0015 4.5 15.0015C5.90201 14.9678 7.27949 14.6264 8.535 14.0015H12ZM17 15.5C17 15.2239 17.2239 15 17.5 15C17.7761 15 18 15.2239 18 15.5V17H19.5C19.7761 17 20 17.2239 20 17.5C20 17.7761 19.7761 18 19.5 18H17.5C17.2239 18 17 17.7761 17 17.5V15.5Z"
      />
    </IconSVGView>
  );
};
