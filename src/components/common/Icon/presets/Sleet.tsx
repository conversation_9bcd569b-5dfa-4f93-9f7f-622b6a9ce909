import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Sleet = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.0005 8.24721C17.9947 8.68891 17.9329 9.12809 17.8165 9.55421C19.7569 9.84375 21.1442 11.5827 20.9955 13.539C20.8467 15.4952 19.2123 17.0044 17.2505 16.9972H12.9005C13.2985 16.3013 13.2965 15.4463 12.8953 14.7522C12.4941 14.0581 11.7542 13.6297 10.9525 13.6272C10.5513 12.9291 9.80762 12.4987 9.00247 12.4987C8.19733 12.4987 7.45361 12.9291 7.05247 13.6272C6.249 13.6281 5.50663 14.0562 5.10347 14.7512C4.93898 15.0403 4.84126 15.3625 4.81747 15.6942C3.21895 14.3228 2.6036 12.1237 3.2582 10.1218C3.9128 8.11994 5.70853 6.70925 7.80847 6.54721C8.63548 4.12092 11.0975 2.6501 13.6259 3.07184C16.1544 3.49357 18.0057 5.68385 18.0005 8.24721ZM11.3245 17.4722L10.5005 16.9972L11.3225 16.5222C11.5547 16.3882 11.6978 16.1405 11.6979 15.8725C11.698 15.6044 11.555 15.3566 11.3229 15.2225C11.0908 15.0883 10.8047 15.0882 10.5725 15.2222L9.75047 15.6972V14.7472C9.75047 14.333 9.41469 13.9972 9.00047 13.9972C8.58626 13.9972 8.25047 14.333 8.25047 14.7472V15.6972L7.42747 15.2222C7.19525 15.0882 6.90919 15.0883 6.67706 15.2225C6.44492 15.3566 6.30197 15.6044 6.30206 15.8725C6.30214 16.1405 6.44525 16.3882 6.67747 16.5222L7.50047 16.9972L6.67547 17.4732C6.31783 17.6809 6.19498 18.1383 6.40047 18.4972C6.46279 18.5999 6.54931 18.6857 6.65247 18.7472C6.68157 18.7634 6.71163 18.7777 6.74247 18.7902C6.82034 18.8262 6.90396 18.8482 6.98947 18.8552C7.0006 18.8552 7.01096 18.8593 7.0212 18.8634C7.03121 18.8673 7.04109 18.8712 7.05147 18.8712C7.06343 18.8712 7.06955 18.8683 7.07482 18.8658C7.07881 18.8639 7.08231 18.8622 7.08747 18.8622C7.20551 18.8596 7.32115 18.8284 7.42447 18.7712L8.25047 18.2972V19.2482C8.25047 19.6624 8.58626 19.9982 9.00047 19.9982C9.41469 19.9982 9.75047 19.6624 9.75047 19.2482V18.2972L10.5745 18.7732C10.6779 18.83 10.7935 18.8609 10.9115 18.8632C10.9175 18.8632 10.9235 18.8655 10.9295 18.8677C10.9355 18.87 10.9415 18.8722 10.9475 18.8722C10.953 18.8722 10.9621 18.8692 10.9723 18.8658C10.9842 18.8618 10.9977 18.8573 11.0095 18.8562C11.0951 18.8497 11.1788 18.8277 11.2565 18.7912C11.2873 18.7787 11.3174 18.7644 11.3465 18.7482C11.4499 18.687 11.5365 18.6011 11.5985 18.4982C11.8061 18.1392 11.6834 17.6799 11.3245 17.4722ZM14.2505 19.7472C14.2516 19.3335 14.5867 18.9983 15.0005 18.9972C15.4142 18.9983 15.7494 19.3335 15.7505 19.7472V21.2472C15.7505 21.6614 15.4147 21.9972 15.0005 21.9972C14.5863 21.9972 14.2505 21.6614 14.2505 21.2472V19.7472ZM17.2505 19.7472C17.2516 19.3335 17.5867 18.9983 18.0005 18.9972C18.4142 18.9983 18.7494 19.3335 18.7505 19.7472V21.2472C18.7505 21.6614 18.4147 21.9972 18.0005 21.9972C17.5863 21.9972 17.2505 21.6614 17.2505 21.2472V19.7472Z"
      />
    </IconSVGView>
  );
};
