import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Phone = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.66 14.96L19.2 15.25C20.21 15.36 20.97 16.22 20.96 17.23V18.96C20.96 19.5 20.75 20.03 20.35 20.4C19.96 20.78 19.43 20.98 18.89 20.96C10.35 20.42 3.54 13.61 3 5.07C2.98 4.53 3.18 4 3.56 3.61C3.94 3.21 4.46 3 5 3H6.73C7.75 3 8.6 3.76 8.72 4.77L9.01 7.29C9.08 7.9 8.87 8.5 8.44 8.93L6.59 10.78C8.03 13.62 10.34 15.93 13.18 17.37L15.02 15.53C15.45 15.1 16.05 14.89 16.66 14.96V14.96Z"
      />
    </IconSVGView>
  );
};
