import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Help = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView
      viewBox="0 0 20 20"
      size={size}
      fill="none"
      style={{ /** Visual adjustment since this icon seems too large. */ transform: 'scale(0.85)' }}
    >
      <path
        d="M10 20c5.523 0 10-4.477 10-10S15.523 0 10 0 0 4.477 0 10s4.477 10 10 10Zm.61-15.96a4.075 4.075 0 0 1 3.353 3.477c.228 1.613-.664 2.392-1.525 3.143-.159.138-.316.275-.465.417a3.52 3.52 0 0 0-.333.333c-.005.005-.01.012-.015.02l-.015.02a2.755 2.755 0 0 0-.33.48A2.126 2.126 0 0 0 11 13H9a3.718 3.718 0 0 1 .2-1.25.634.634 0 0 1 .02-.07c.005-.015.013-.027.02-.04.008-.013.015-.025.02-.04a3.329 3.329 0 0 1 .28-.55c0-.005.003-.008.005-.01.003-.002.005-.005.005-.01.34-.513.798-.866 1.225-1.196.614-.473 1.166-.898 1.225-1.684a2.023 2.023 0 0 0-1.57-2.097 2 2 0 0 0-2.3 1.28.963.963 0 0 1-.88.667h-.2a.907.907 0 0 1-.87-1.167 4 4 0 0 1 4.43-2.793ZM9 16v-2h2v2H9Z"
        fill={useColor(color)}
      />
    </IconSVGView>
  );
};
