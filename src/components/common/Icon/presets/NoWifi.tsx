import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const NoWifi = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.99023 1.99023C2.5833 1.99034 2.21702 2.23699 2.0639 2.614C1.91077 2.99102 2.00135 3.42322 2.29296 3.70703L5.21679 6.63086C4.26217 7.11626 3.36042 7.69107 2.5371 8.36133C1.8941 8.88433 1.85735 9.85736 2.44335 10.4434C2.93135 10.9314 3.70385 10.994 4.25585 10.582C5.22171 9.86026 6.29766 9.2888 7.44335 8.85742L9.0332 10.4473C8.04474 10.7197 7.1051 11.1084 6.25195 11.6367C5.42795 12.1457 5.29155 13.2916 5.97655 13.9766C6.47355 14.4736 7.23984 14.5418 7.83984 14.1738C8.94749 13.4939 10.237 13.0941 11.6133 13.0273L13.8984 15.3125C13.3018 15.1114 12.6639 15 12 15C11.094 15 10.2368 15.2034 9.46679 15.5664C8.83879 15.8614 8.69454 16.6945 9.18554 17.1855L10.9668 18.9668C11.5378 19.5378 12.4622 19.5378 13.0332 18.9668L14.8144 17.1855C15.0022 16.9978 15.0969 16.76 15.1074 16.5215L20.293 21.707C20.5438 21.9683 20.9162 22.0735 21.2667 21.9821C21.6171 21.8908 21.8908 21.6171 21.9821 21.2667C22.0735 20.9162 21.9683 20.5438 21.707 20.293L3.70702 2.29297C3.51875 2.09944 3.26023 1.99025 2.99023 1.99023ZM12 5C11.135 5 10.2888 5.0768 9.46484 5.2168L12.2578 8.01172C15.0618 8.06772 17.6451 9.01403 19.7441 10.582C20.2971 10.995 21.0686 10.9294 21.5566 10.4414C22.1426 9.85541 22.1049 8.88237 21.4629 8.35938C18.8819 6.25938 15.588 5 12 5ZM18.1133 13.8652L14.5566 10.3086C15.7086 10.5866 16.7924 11.0414 17.7734 11.6504C18.5484 12.1324 18.6963 13.1802 18.1133 13.8652Z"
      />
    </IconSVGView>
  );
};
