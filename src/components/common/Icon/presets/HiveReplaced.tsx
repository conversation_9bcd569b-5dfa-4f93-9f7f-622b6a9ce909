import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const HiveReplaced = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <rect fill={useColor(color)} width="24" height="24" fillOpacity="0.01" />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.1564 12.5279C13.2308 13.4349 12.0014 15.3715 12 17.5C11.9991 18.7781 12.4452 20.0162 13.261 21H5C3.89883 20.9918 3.00817 20.1012 3 19V7C3.00063 6.82415 3.04791 6.65161 3.137 6.5L4.887 3.5C5.06509 3.19153 5.39382 3.00108 5.75 3H18.25C18.6062 3.00108 18.9349 3.19153 19.113 3.5L20.863 6.5C20.9521 6.65161 20.9994 6.82415 21 7V13.261C19.3581 11.9065 17.082 11.621 15.1564 12.5279ZM17.676 5H6.324L5.158 7H18.842L17.676 5ZM9 10C9 9.44772 9.44772 9 10 9H14C14.5523 9 15 9.44772 15 10C15 10.5523 14.5523 11 14 11H10C9.44772 11 9 10.5523 9 10ZM13 17.5C13 15.0147 15.0147 13 17.5 13C19.9853 13 22 15.0147 22 17.5C22 19.9853 19.9853 22 17.5 22C15.0147 22 13 19.9853 13 17.5ZM18.1251 19.9393C18.983 19.7473 19.6823 19.1283 19.977 18.3C20.0199 18.1874 20.0049 18.0609 19.9369 17.9614C19.8689 17.8618 19.7565 17.8019 19.636 17.801C19.4821 17.8 19.3449 17.8974 19.295 18.043C19.0852 18.6369 18.5855 19.0818 17.9714 19.2216C17.3572 19.3614 16.7142 19.1766 16.268 18.732L16.887 18.113C16.9394 18.0603 16.9547 17.9811 16.9258 17.9127C16.8968 17.8442 16.8293 17.8001 16.755 17.801H15.183C15.1344 17.8007 15.0877 17.8199 15.0533 17.8543C15.0189 17.8887 14.9997 17.9354 15 17.984V19.557C14.9993 19.6312 15.0438 19.6985 15.1125 19.7268C15.1811 19.7552 15.26 19.739 15.312 19.686L15.751 19.246C16.3707 19.8696 17.2672 20.1314 18.1251 19.9393ZM20 17.016C20.0003 17.0646 19.9811 17.1113 19.9467 17.1457C19.9123 17.1801 19.8656 17.1993 19.817 17.199H18.245C18.1707 17.1999 18.1032 17.1558 18.0742 17.0873C18.0453 17.0189 18.0606 16.9397 18.113 16.887L18.732 16.268C18.2858 15.8234 17.6428 15.6386 17.0286 15.7784C16.4145 15.9182 15.9148 16.3631 15.705 16.957C15.6551 17.1026 15.5179 17.2 15.364 17.199C15.2432 17.1982 15.1305 17.1381 15.0625 17.0384C14.9944 16.9386 14.9797 16.8117 15.023 16.699C15.318 15.8709 16.0173 15.2521 16.8752 15.0603C17.7331 14.8684 18.6294 15.1304 19.249 15.754L19.688 15.314C19.7398 15.2611 19.8186 15.2449 19.8871 15.273C19.9556 15.3011 20.0003 15.3679 20 15.442V17.016Z"
      />
    </IconSVGView>
  );
};
