import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Sms = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13 3H20C20.5523 3 21 3.44772 21 4V9C21 9.55228 20.5523 10 20 10H15L12 13V4C12 3.44772 12.4477 3 13 3ZM16.69 14.97L19.23 15.26C20.2364 15.3734 20.9954 16.2273 20.99 17.24V18.97C20.9946 19.5141 20.7757 20.0363 20.3843 20.4144C19.993 20.7924 19.4636 20.9933 18.92 20.97C10.3771 20.4318 3.56821 13.623 3.03 5.08C3.00668 4.53636 3.20755 4.00699 3.58564 3.61567C3.96373 3.22434 4.48588 3.00539 5.03 3.01H6.76C7.77684 3.00834 8.63307 3.76991 8.75 4.78L9.04 7.3C9.11092 7.90508 8.90091 8.50933 8.47 8.94L6.62 10.79C8.06378 13.6288 10.3712 15.9362 13.21 17.38L15.05 15.54C15.4807 15.1091 16.0849 14.8991 16.69 14.97Z"
      />
    </IconSVGView>
  );
};
