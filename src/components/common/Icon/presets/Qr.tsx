import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Qr = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21 7C20.45 7 20 6.55 20 6V4H18C17.45 4 17 3.55 17 3C17 2.45 17.45 2 18 2H21C21.55 2 22 2.45 22 3V6C22 6.55 21.55 7 21 7ZM5 5H11V11H5V5ZM9.5 9.5V6.5H6.5V9.5H9.5ZM9.5 14.5V17.5H6.5V14.5H9.5ZM5 13H11V19H5V13ZM17.5 6.5V9.5H14.5V6.5H17.5ZM13 5H19V11H13V5ZM13 13H14.5V14.5H13V13ZM16 14.5H14.5V16H13V17.5H14.5V19H16V17.5H17.5V19H19V17.5H17.5V16H19V14.5H17.5V13H16V14.5ZM16 16H17.5V14.5H16V16ZM16 16H14.5V17.5H16V16ZM22 18V21C22 21.55 21.55 22 21 22H18C17.45 22 17 21.55 17 21C17 20.45 17.45 20 18 20H20V18C20 17.45 20.45 17 21 17C21.55 17 22 17.45 22 18ZM3 22H6C6.55 22 7 21.55 7 21C7 20.45 6.55 20 6 20H4V18C4 17.45 3.55 17 3 17C2.45 17 2 17.45 2 18V21C2 21.55 2.45 22 3 22ZM2 6V3C2 2.45 2.45 2 3 2H6C6.55 2 7 2.45 7 3C7 3.55 6.55 4 6 4H4V6C4 6.55 3.55 7 3 7C2.45 7 2 6.55 2 6Z"
      />
    </IconSVGView>
  );
};
