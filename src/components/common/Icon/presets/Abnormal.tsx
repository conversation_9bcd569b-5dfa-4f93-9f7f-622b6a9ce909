import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Abnormal = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 4C9 2.89543 9.89543 2 11 2H13C14.1046 2 15 2.89543 15 4V6H16C16.5523 6 17 6.44772 17 7C17 7.55228 16.5523 8 16 8H15V10H16C16.5523 10 17 10.4477 17 11C17 11.5523 16.5523 12 16 12H15V13.022C16.8991 14.4411 17.5376 17.0005 16.5275 19.1454C15.5174 21.2902 13.1376 22.4282 10.834 21.868C9.61564 21.5834 8.55231 20.8435 7.862 19.8C6.36412 17.6007 6.86598 14.6116 9 13.022V4ZM13 4H11V6H13V4Z"
      />
    </IconSVGView>
  );
};
