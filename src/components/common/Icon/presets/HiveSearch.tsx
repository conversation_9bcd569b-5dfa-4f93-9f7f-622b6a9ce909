import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const HiveSearch = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <rect fill={useColor(color)} width="24" height="24" fillOpacity="0.01" />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.9013 6.5C20.9903 6.65161 21.0376 6.82415 21.0383 7V19C21.0301 20.1012 20.1394 20.9918 19.0383 21H11.5123L8.70326 18.191C8.92437 17.6546 9.03819 17.0801 9.03826 16.5C9.0355 14.0159 7.0224 12.0028 4.53826 12C4.02609 12.0049 3.51862 12.0983 3.03826 12.276V7C3.03889 6.82415 3.08616 6.65161 3.17526 6.5L4.92526 3.5C5.10335 3.19153 5.43207 3.00108 5.78826 3H18.2883C18.6444 3.00108 18.9732 3.19153 19.1513 3.5L20.9013 6.5ZM10.0383 11H14.0383C14.5905 11 15.0383 10.5523 15.0383 10C15.0383 9.44772 14.5905 9 14.0383 9H10.0383C9.48597 9 9.03826 9.44772 9.03826 10C9.03826 10.5523 9.48597 11 10.0383 11ZM5.19626 7L6.36226 5H17.7143L18.8803 7H5.19626ZM7.48126 18.383C8.45335 16.8704 8.12914 14.8682 6.72935 13.7397C5.32955 12.6111 3.30421 12.719 2.03218 13.9898C0.760152 15.2606 0.650395 17.2859 1.77763 18.6867C2.90487 20.0876 4.90669 20.4137 6.42026 19.443L9.77626 22.8C9.96447 22.9951 10.2433 23.0734 10.5055 23.0049C10.7678 22.9363 10.9727 22.7317 11.0414 22.4695C11.1102 22.2073 11.0321 21.9284 10.8373 21.74L7.48126 18.383ZM4.53826 18.5C3.43369 18.5 2.53826 17.6046 2.53826 16.5C2.53826 15.3954 3.43369 14.5 4.53826 14.5C5.64283 14.5 6.53826 15.3954 6.53826 16.5C6.53826 17.6046 5.64283 18.5 4.53826 18.5Z"
      />
    </IconSVGView>
  );
};
