import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const RainHigh = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9435 2.39164C18.1745 3.33262 19.4083 5.73918 18.87 8.09991C20.8518 8.55146 22.1798 10.4175 21.9573 12.4378C21.7348 14.4582 20.0326 15.9904 18 15.9999H17.25V15.7379C17.2484 14.5026 16.2473 13.5016 15.012 13.4999H14.988C14.7322 13.5024 14.4788 13.549 14.239 13.6379C14.1883 12.4381 13.2009 11.4916 12 11.4916C10.7992 11.4916 9.81173 12.4381 9.76101 13.6379C9.52117 13.549 9.26776 13.5024 9.01201 13.4999H8.98801C7.75268 13.5016 6.75166 14.5026 6.75001 15.7379V15.8679C4.58582 15.4065 2.8513 13.7913 2.23701 11.6654C1.62272 9.53957 2.22839 7.24817 3.81289 5.70351C5.39739 4.15884 7.70347 3.6117 9.813 4.27991C11.1279 2.24675 13.7125 1.45067 15.9435 2.39164ZM12 12.9999C11.5862 13.001 11.2511 13.3362 11.25 13.7499V18.2499C11.25 18.6641 11.5858 18.9999 12 18.9999C12.4142 18.9999 12.75 18.6641 12.75 18.2499V13.7499C12.7489 13.3362 12.4138 13.001 12 12.9999ZM8.98801 14.9999H9.01201C9.41914 15.001 9.74891 15.3308 9.75001 15.7379V20.2619C9.74891 20.669 9.41914 20.9988 9.01201 20.9999H8.98801C8.58087 20.9988 8.2511 20.669 8.25001 20.2619V15.7379C8.2511 15.3308 8.58087 15.001 8.98801 14.9999ZM14.988 14.9999H15.012C15.4191 15.001 15.7489 15.3308 15.75 15.7379V20.2619C15.7489 20.669 15.4191 20.9988 15.012 20.9999H14.988C14.5809 20.9988 14.2511 20.669 14.25 20.2619V15.7379C14.2511 15.3308 14.5809 15.001 14.988 14.9999Z"
      />
    </IconSVGView>
  );
};
