import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Equal = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.5 10.125H19.5C20.1213 10.125 20.625 9.62132 20.625 9C20.625 8.37868 20.1213 7.875 19.5 7.875H4.5C3.87868 7.875 3.375 8.37868 3.375 9C3.375 9.62132 3.87868 10.125 4.5 10.125ZM4.5 17.625H19.5C20.1213 17.625 20.625 17.1213 20.625 16.5C20.625 15.8787 20.1213 15.375 19.5 15.375H4.5C3.87868 15.375 3.375 15.8787 3.375 16.5C3.375 17.1213 3.87868 17.625 4.5 17.625Z"
      />
    </IconSVGView>
  );
};
