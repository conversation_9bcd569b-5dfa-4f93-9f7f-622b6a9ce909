import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Email = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 4H20C21.1032 4.00329 21.9967 4.8968 22 6V18C21.9967 19.1032 21.1032 19.9967 20 20H4C2.8968 19.9967 2.00329 19.1032 2 18L2.01 6C2.00999 4.89932 2.89934 4.0055 4 4ZM12.53 12.67L19.6 8.25C19.8572 8.08923 20.0092 7.80344 19.9985 7.50029C19.9879 7.19713 19.8164 6.92267 19.5485 6.78029C19.2807 6.6379 18.9572 6.64923 18.7 6.81L12 11L5.3 6.81C5.04277 6.64923 4.71931 6.6379 4.45146 6.78029C4.18362 6.92267 4.01208 7.19713 4.00146 7.50029C3.99085 7.80344 4.14277 8.08923 4.4 8.25L11.47 12.67C11.795 12.8701 12.205 12.8701 12.53 12.67Z"
      />
    </IconSVGView>
  );
};
