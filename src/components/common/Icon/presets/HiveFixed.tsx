import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const HiveFixed = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <rect fill={useColor(color)} width="24" height="24" fillOpacity="0.01" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 17.5C12.0014 15.3715 13.2308 13.4349 15.1564 12.5279C17.082 11.621 19.3581 11.9065 21 13.261V7C20.9994 6.82415 20.9521 6.65161 20.863 6.5L19.113 3.5C18.9349 3.19153 18.6062 3.00108 18.25 3H5.75C5.39382 3.00108 5.06509 3.19153 4.887 3.5L3.137 6.5C3.04791 6.65161 3.00063 6.82415 3 7V19C3.00817 20.1012 3.89883 20.9918 5 21H13.261C12.4452 20.0162 11.9991 18.7781 12 17.5ZM6.324 5H17.676L18.842 7H5.158L6.324 5ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9H14C14.5523 9 15 9.44772 15 10C15 10.5523 14.5523 11 14 11H10Z"
        fill={useColor(color)}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13 17.5C13 15.0147 15.0147 13 17.5 13C19.9853 13 22 15.0147 22 17.5C22 19.9853 19.9853 22 17.5 22C15.0147 22 13 19.9853 13 17.5ZM20.2636 15.8364C20.0699 15.6396 19.7533 15.6371 19.5565 15.8308L16.9316 18.4147L15.8508 17.3508C15.654 17.157 15.3374 17.1595 15.1437 17.3563C14.95 17.5531 14.9525 17.8697 15.1492 18.0634L16.5746 19.4664C16.6736 19.5639 16.803 19.6117 16.9318 19.6101C17.0604 19.6116 17.1896 19.5638 17.2886 19.4664L20.258 16.5435C20.4548 16.3498 20.4573 16.0332 20.2636 15.8364Z"
        fill={useColor(color)}
      />
    </IconSVGView>
  );
};
