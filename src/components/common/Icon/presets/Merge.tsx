import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Merge = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 4H4V8H2V3.25C2.00165 2.56033 2.56033 2.00165 3.25 2H8V4ZM21 11H18V9.5C17.9999 9.30584 17.8873 9.12932 17.7114 9.04723C17.5354 8.96515 17.3279 8.99235 17.179 9.117L14.179 11.617C14.0654 11.712 13.9998 11.8524 13.9998 12.0005C13.9998 12.1486 14.0654 12.289 14.179 12.384L17.179 14.884C17.2691 14.9591 17.3827 15.0002 17.5 15C17.5732 15.0001 17.6455 14.9844 17.712 14.954C17.8881 14.8715 18.0005 14.6945 18 14.5V13H21C21.5523 13 22 12.5523 22 12C22 11.4477 21.5523 11 21 11ZM6.82 9.117L9.82 11.617C9.93356 11.712 9.99918 11.8524 9.99918 12.0005C9.99918 12.1486 9.93356 12.289 9.82 12.384L6.82 14.884C6.67097 15.0081 6.4636 15.0349 6.28795 14.9526C6.11231 14.8704 6.00007 14.694 6 14.5V13H3C2.44772 13 2 12.5523 2 12C2 11.4477 2.44772 11 3 11H6V9.5C6.00046 9.30623 6.11284 9.13017 6.2884 9.04817C6.46397 8.96616 6.67111 8.99298 6.82 9.117ZM4 16V20H8V22H3.25C2.56056 21.9978 2.0022 21.4394 2 20.75V16H4ZM20.75 2H16V4H20V8H22V3.25C21.9984 2.56033 21.4397 2.00165 20.75 2ZM16 22V20H20V16H22V20.75C21.9978 21.4394 21.4394 21.9978 20.75 22H16Z"
      />
    </IconSVGView>
  );
};
