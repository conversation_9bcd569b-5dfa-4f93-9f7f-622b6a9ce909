import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const BatteryLow = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 3H16.5C17.3284 3 18 3.67157 18 4.5V21C18 21.8284 17.3284 22.5 16.5 22.5H7.5C6.67157 22.5 6 21.8284 6 21V4.5C6 3.67157 6.67157 3 7.5 3H9V1.5H15V3ZM7.5 18H16.5V4.5H13.5V3H10.5V4.5H7.5V18Z"
      />
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 3H16.5C17.3284 3 18 3.67157 18 4.5V21C18 21.8284 17.3284 22.5 16.5 22.5H7.5C6.67157 22.5 6 21.8284 6 21V4.5C6 3.67157 6.67157 3 7.5 3H9V1.5H15V3ZM7.5 18H16.5V4.5H13.5V3H10.5V4.5H7.5V18Z"
      />
      <rect fill={useColor(color)} x="-58.5" y="-76.5" width="156" height="156" />
    </IconSVGView>
  );
};
