import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Cloudy = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        d="M18.87 11.0999C19.4083 8.73918 18.1745 6.33262 15.9435 5.39164C13.7125 4.45067 11.1279 5.24675 9.813 7.27991C7.5592 6.56613 5.09635 7.24236 3.52304 9.00696C1.94974 10.7716 1.5593 13.2955 2.52588 15.453C3.49247 17.6105 5.63587 18.9993 8 18.9999H18C20.0325 18.9904 21.7348 17.4582 21.9573 15.4378C22.1798 13.4175 20.8518 11.5515 18.87 11.0999Z"
      />
    </IconSVGView>
  );
};
