import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Reasons = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5 22C19.9853 22 22 19.9853 22 17.5C22 15.0147 19.9853 13 17.5 13C15.0147 13 13 15.0147 13 17.5C13 19.9853 15.0147 22 17.5 22ZM17.7106 15.0207C16.8261 14.886 15.9769 15.4214 15.7171 16.2777C15.6801 16.4013 15.7038 16.5351 15.7809 16.6386C15.8581 16.742 15.9796 16.8029 16.1086 16.8027H16.1986C16.3805 16.7957 16.5387 16.6759 16.5946 16.5027C16.7469 16.0787 17.1891 15.8326 17.6296 15.9267C18.0644 16.0257 18.3634 16.4251 18.3361 16.8702C18.3096 17.2238 18.0612 17.4154 17.7851 17.6282C17.5928 17.7765 17.387 17.9352 17.2336 18.1662C17.2336 18.1684 17.2325 18.1696 17.2314 18.1707C17.2303 18.1718 17.2291 18.1729 17.2291 18.1752C17.2291 18.1777 17.2263 18.1817 17.223 18.1862C17.2205 18.1898 17.2176 18.1937 17.2156 18.1977C17.1719 18.2694 17.1343 18.3447 17.1031 18.4227C17.1009 18.4294 17.0975 18.4351 17.0941 18.4407C17.0908 18.4463 17.0874 18.4519 17.0851 18.4587C17.0815 18.469 17.0785 18.4795 17.0761 18.4902C17.0142 18.6712 16.9838 18.8614 16.9861 19.0527H17.8861C17.8849 18.8839 17.9284 18.7177 18.0121 18.5712C18.0166 18.5644 18.02 18.5577 18.0234 18.5509C18.0268 18.5442 18.0301 18.5374 18.0346 18.5307C18.0715 18.4686 18.1136 18.4099 18.1606 18.3552C18.1629 18.3529 18.1651 18.3496 18.1674 18.3462C18.1696 18.3428 18.1719 18.3394 18.1741 18.3372C18.2207 18.2839 18.2708 18.2338 18.3241 18.1872C18.3912 18.1236 18.4619 18.0619 18.5331 17.9998C18.9207 17.6617 19.3223 17.3113 19.2196 16.5852C19.1132 15.7896 18.5018 15.1557 17.7106 15.0207ZM16.9861 19.5026H17.8861V20.4026H16.9861V19.5026Z"
      />
      <path
        fill={useColor(color)}
        d="M12 1.5C11.5132 1.50017 11.0903 1.83496 10.9785 2.30878C10.8666 2.78259 11.0951 3.27114 11.5305 3.489L10.95 4.65L8.7945 3.9315C8.94892 3.4695 8.7653 2.96196 8.35103 2.70569C7.93676 2.44943 7.40064 2.51174 7.05619 2.85619C6.71174 3.20064 6.64943 3.73676 6.90569 4.15103C7.16196 4.5653 7.6695 4.74892 8.1315 4.5945L8.61 6.0315C8.75202 6.45834 9.15016 6.7473 9.6 6.75H14.4C14.8521 6.74987 15.2533 6.46043 15.396 6.0315L15.8745 4.5945C15.9793 4.63025 16.0892 4.64899 16.2 4.65C16.6556 4.65013 17.0593 4.35644 17.1994 3.92292C17.3395 3.4894 17.1841 3.01499 16.8146 2.74845C16.4451 2.4819 15.9459 2.48405 15.5787 2.75376C15.2115 3.02348 15.0602 3.4992 15.204 3.9315L13.05 4.65L12.4695 3.4875C12.9031 3.26879 13.1302 2.78125 13.0185 2.3086C12.9069 1.83595 12.4857 1.50155 12 1.5Z"
      />
      <path
        fill={useColor(color)}
        d="M21.8984 14.1974C22.2742 13.7432 22.5 13.1605 22.5 12.525C22.5 11.0753 21.3247 9.9 19.875 9.9C18.212 9.9478 16.5842 10.3905 15.126 11.1915C15.126 11.1512 15.1319 11.1116 15.1379 11.0719C15.1439 11.0317 15.15 10.9914 15.15 10.95C15.15 9.2103 13.7397 7.8 12 7.8C10.2603 7.8 8.85 9.2103 8.85 10.95C8.85 10.9995 8.85648 11.0474 8.86296 11.0953C8.86728 11.1272 8.8716 11.1591 8.874 11.1915C7.41582 10.3905 5.78802 9.9478 4.125 9.9C2.67525 9.9 1.5 11.0753 1.5 12.525C1.5 13.9747 2.67525 15.15 4.125 15.15C5.59733 15.1144 7.0439 14.756 8.3625 14.1H13.1765C14.1836 12.8211 15.7459 12 17.5 12C19.2982 12 20.8949 12.863 21.8984 14.1974Z"
      />
      <path
        fill={useColor(color)}
        d="M12 22.5C10.624 22.4242 9.3911 21.6251 8.76 20.4H12.8258C13.1529 20.9262 13.5664 21.393 14.0465 21.7808C13.4561 22.203 12.7493 22.4587 12 22.5Z"
      />
      <path
        fill={useColor(color)}
        d="M17.5 13C16.3724 13 15.3417 13.4147 14.552 14.1H15.6375C16.9561 14.756 18.4027 15.1144 19.875 15.15C20.3277 15.15 20.7536 15.0354 21.1254 14.8337C20.3061 13.7215 18.9873 13 17.5 13Z"
      />
      <path
        fill={useColor(color)}
        d="M12.1545 16.2H7.818C7.8075 16.3725 7.8 16.548 7.8 16.725C7.80027 17.2541 7.85455 17.7819 7.962 18.3H12.0578C12.0197 18.0388 12 17.7717 12 17.5C12 17.0521 12.0535 16.6168 12.1545 16.2Z"
      />
      <path
        fill={useColor(color)}
        d="M13 17.5C13 17.773 13.0243 18.0404 13.0709 18.3H16.038C16.1402 17.8072 16.1934 17.3058 16.1986 16.8027C16.199 16.8027 16.1983 16.8027 16.1986 16.8027H16.1086C15.9796 16.8029 15.8581 16.742 15.7809 16.6386C15.7038 16.5351 15.6801 16.4013 15.7171 16.2777C15.7251 16.2515 15.7336 16.2256 15.7426 16.2H13.1906C13.0666 16.6116 13 17.048 13 17.5Z"
      />
      <path
        fill={useColor(color)}
        d="M14.0589 20.4H15.24C15.1121 20.6483 14.9595 20.879 14.786 21.0898C14.5188 20.8874 14.2749 20.6559 14.0589 20.4Z"
      />
    </IconSVGView>
  );
};
