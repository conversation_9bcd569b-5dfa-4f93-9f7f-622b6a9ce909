import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const FlashOn = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.2734 2C10.8524 2 10.476 2.2632 10.332 2.6582L9.45117 5.08398L14.0742 9.70703L16.875 3.40625C17.169 2.74525 16.6849 2 15.9609 2H11.2734ZM3.69726 2.53516C3.44139 2.53516 3.18378 2.63262 2.98828 2.82812C2.59728 3.21913 2.59728 3.85314 2.98828 4.24414L7.95312 9.20703L7.0625 11.6582C6.8245 12.3102 7.30795 13 8.00195 13H11.7461L11.9277 13.1836L8.88867 21C8.69967 21.616 8.85623 21.981 9.49023 22C9.81023 22 10.1128 21.8469 10.3008 21.5879L14.5234 15.7793L20.0742 21.3281C20.4642 21.7191 21.0973 21.7191 21.4883 21.3281C21.8793 20.9381 21.8793 20.3051 21.4883 19.9141L4.40234 2.82812C4.20734 2.63262 3.95314 2.53516 3.69726 2.53516ZM16.8828 12.5156L15.3672 11H16.0352C16.8242 11 17.2828 11.8606 16.8828 12.5156Z"
      />
    </IconSVGView>
  );
};
