import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Feedback = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 2H4.01001C2.91001 2 2.01001 2.9 2.01001 4V22L6.00001 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM13 12V14H11V12H13ZM12 10C12.55 10 13 9.55 13 9V7C13 6.45 12.55 6 12 6C11.45 6 11 6.45 11 7V9C11 9.55 11.45 10 12 10Z"
      />
    </IconSVGView>
  );
};
