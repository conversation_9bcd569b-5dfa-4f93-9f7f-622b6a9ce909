import { useColor } from '@style/theme/hooks';

import { IconSVGView } from '../styles';
import { IconSVGProps } from '../types';

export const Storm = ({ color, size }: IconSVGProps) => {
  return (
    <IconSVGView viewBox="0 0 24 24" size={size}>
      <path
        fill={useColor(color)}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.8163 9.55597C18.531 6.83293 16.9615 4.03272 14.2655 3.22135C11.5692 2.40989 8.71458 3.87914 7.808 6.54493C5.13548 6.75856 3.05895 8.96131 3.00322 11.6418C2.94749 14.3222 4.93067 16.6094 7.592 16.9339C7.65151 16.7106 7.74966 16.4994 7.882 16.3099L9.54 14.0239L10.754 12.3509C11.2765 11.6337 12.1978 11.3289 13.045 11.5928C13.8922 11.8567 14.4773 12.6309 14.5 13.5179V15.4999C15.2541 15.5177 15.9379 15.9467 16.282 16.6179C16.3395 16.7399 16.3837 16.8676 16.414 16.9989H17.25C19.2119 17.0061 20.8462 15.4969 20.995 13.5407C21.1437 11.5845 19.7565 9.84562 17.8163 9.55597ZM13 16.9999H14.433C14.6311 16.9864 14.8196 17.0868 14.9189 17.2587C15.0181 17.4307 15.0108 17.6442 14.9 17.8089L13.245 20.0999L12.031 21.7689C11.8903 21.9608 11.6455 22.0456 11.4163 21.9818C11.1871 21.918 11.0213 21.7189 11 21.4819V17.9999H9.567C9.36892 18.0135 9.18039 17.9131 9.08113 17.7411C8.98187 17.5692 8.98919 17.3557 9.1 17.1909L10.756 14.9049L11.97 13.2309C12.02 13.1651 12.0843 13.1114 12.158 13.0739C12.1748 13.0646 12.1922 13.0563 12.21 13.0489C12.2801 13.0184 12.3555 13.0018 12.432 12.9999C12.4971 13.0015 12.5616 13.0134 12.623 13.0349C12.6353 13.0388 12.6463 13.044 12.6577 13.0493C12.664 13.0522 12.6703 13.0551 12.677 13.0579C12.7181 13.0754 12.757 13.0975 12.793 13.1239C12.8106 13.1368 12.8273 13.1509 12.843 13.1659C12.8741 13.1957 12.9013 13.2293 12.924 13.2659C12.9359 13.2823 12.9462 13.2997 12.955 13.3179C12.9844 13.3805 12.9997 13.4488 13 13.5179V16.9999Z"
      />
    </IconSVGView>
  );
};
