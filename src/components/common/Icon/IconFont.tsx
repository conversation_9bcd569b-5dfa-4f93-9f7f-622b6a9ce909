import React from 'react';
import { Color } from 'styled-components';

import { useColor } from '@style/theme/hooks';

const DEFAULT_ICON_SIZE = 16;

export const IconFont: React.FC<{ name: string; size?: number; color?: Color | 'currentColor' }> = ({
  name,
  size,
  color,
}) => {
  return (
    <i
      className={`icon-${name}`.toLowerCase()}
      style={{ color: useColor(color), fontSize: size ?? DEFAULT_ICON_SIZE }}
    />
  );
};
