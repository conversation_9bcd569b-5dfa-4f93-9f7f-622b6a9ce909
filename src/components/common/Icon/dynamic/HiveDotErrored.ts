import { DefaultTheme } from 'styled-components';

import { DynamicIcon } from '@components/common/Icon/types';
import { SVG } from '@helpers/SVG';

export const HiveDotErrored: DynamicIcon<[{ theme: DefaultTheme; size: number }]> = {
  getImageURI({ theme, size }) {
    return SVG.generateCachedSvgUri(size, () => {
      return `
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="${size}" height="${size}" 
          viewBox="0 0 ${size} ${size}">
          <circle 
            r="45%" cx="50%" cy="50%" stroke="${theme.colors.contentDanger}" stroke-width="2" fill="transparent" />
        </svg>
      `;
    });
  },
};
