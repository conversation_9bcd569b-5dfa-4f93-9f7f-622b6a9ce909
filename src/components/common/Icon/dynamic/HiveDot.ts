import { DefaultTheme } from 'styled-components';

import { DynamicIcon } from '@components/common/Icon/types';
import { SVG } from '@helpers/SVG';

const DEF_COLOR = '#9432D9';

export const HiveDot: DynamicIcon<[{ theme: DefaultTheme; size: number; borderSize?: number; color?: string }]> = {
  getImageURI({ theme, size, borderSize, color }) {
    return SVG.generateCachedSvgUri(`${size}-${borderSize}-${color}`, () => {
      borderSize = borderSize ?? 4;
      const innerSize = (size - borderSize * 2) / size / 2;
      return `
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="${size}" height="${size}" 
          viewBox="0 0 ${size} ${size}">
          <circle 
            r="50%" cx="50%" cy="50%" fill="${theme.primitives.colors.white}" />
          <circle 
            r="${(100 * innerSize).toFixed(2)}%" cx="50%" cy="50%" fill="${color ?? DEF_COLOR}" />
        </svg>
      `;
    });
  },
};
