import { DynamicIcon } from '@components/common/Icon/types';
import { SVG } from '@helpers/SVG';

export const YardDot: DynamicIcon<
  [{ fill?: string; fillOpacity?: number; stroke?: string; strokeOpacity?: number; strokeWidth?: number; size: number }]
> = {
  getImageURI({ fill, fillOpacity, stroke, strokeOpacity, strokeWidth, size }) {
    const cacheKey = [fill, fillOpacity, stroke, strokeOpacity, size].map((p) => p ?? 'none').join('-');
    return SVG.generateCachedSvgUri(cacheKey, () => {
      return `
        <svg 
          xmlns="http://www.w3.org/2000/svg"
          width="${size}" height="${size}"
          viewBox="0 0 ${size} ${size}">
          <circle 
            r="${size / 2 - (strokeWidth ?? 1)}" 
            cx="50%" 
            cy="50%" 
            fill="${fill}"
            fill-opacity="${fillOpacity}" 
            stroke="${stroke}" 
            stroke-width="${stroke ? strokeWidth ?? 1 : 0}" 
            stroke-opacity="${strokeOpacity}" />
        </svg>
      `;
    });
  },
};
