import { SVGAttributes } from 'react';
import { Color } from 'styled-components';

import { BoxProps } from '@components/common/Box/types';

/**
 * Default props for <svg/> icons.
 * */
export interface IconSVGProps extends SVGAttributes<HTMLOrSVGElement> {
  size?: number;
  color?: Color | 'currentColor';
}

/**
 * Default props for <img/> icons.
 * */
export interface IconIMGProps extends BoxProps<'img'> {
  size?: number;
}

/**
 * Defines a dynamically generated icon.
 * */
export interface DynamicIcon<A extends Array<unknown>> {
  getImageURI: (...args: A) => string;
}
