import { Component, useLayoutEffect, useMemo, useRef } from 'react';
import RISC from 'react-infinite-scroll-component';
import { debounce } from 'lodash';

export type InfinityScrollProps = RISC extends Component<infer P, any> ? P : never;

const DEF_RELOAD_DEBOUNCE_DELAY = 250;

export const InfiniteScroll = ({ children, ...props }: InfinityScrollProps) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const hasData = props.dataLength > 0;
  const debouncedNext = useMemo(() => debounce(props.next, DEF_RELOAD_DEBOUNCE_DELAY), [props.next]);

  // If the initial batch of items is not enough to fill
  // the scroll height, the infinite scroll component
  // won't ask to load more. This effect will overcome
  // this problem.
  useLayoutEffect(() => {
    if (hasData && props.hasMore) {
      const scrollElement = wrapperRef.current?.querySelector<HTMLDivElement>('.infinite-scroll-component');
      if (scrollElement) {
        const scrollHeight = scrollElement.offsetHeight;
        const children = Array.from(scrollElement.children) as Array<HTMLElement>;
        const childrenHeight = children.reduce((total, child) => total + child.getBoundingClientRect().height, 0);
        const canLoadMoreItems = scrollHeight > childrenHeight;

        if (canLoadMoreItems) {
          debouncedNext();
        }
      }
    }
  }, [debouncedNext, hasData, props.hasMore]);

  return (
    <div ref={wrapperRef}>
      <RISC {...props}>{children}</RISC>
    </div>
  );
};
