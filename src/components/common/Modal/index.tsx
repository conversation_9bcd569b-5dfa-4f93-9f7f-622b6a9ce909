import React from 'react';
import { useTheme } from 'styled-components';

import { ModalBase } from '@components/common/ModalBase';
import { useModalDeferredOpenState } from '@components/common/ModalBase/hooks';
import { ModalMeerkat } from '@components/common/ModalMeerkat';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { useColor } from '@style/theme/hooks';

import { MODAL_BP_1, MODAL_BP_2 } from './constants';
import { StyledModal } from './styles';
import { ModalProps } from './types';

export * from '@components/common/ModalBase';

export const Modal: React.FCWithChildren<ModalProps> = ({
  backgroundColor: propBackgroundColor,
  useFullHeight,
  suppressAutoScroll,
  children,
  ...props
}) => {
  const theme = useTheme();
  const backgroundColor = useColor(propBackgroundColor, theme.primitives.colors.white);
  const isOpen = useModalDeferredOpenState(props.isOpen);

  return (
    <>
      <ResponsiveRender until={MODAL_BP_1}>
        <ModalMeerkat suppressAutoScroll={suppressAutoScroll} useFullHeight={useFullHeight} {...props}>
          {children}
        </ModalMeerkat>
      </ResponsiveRender>
      <ResponsiveRender from={MODAL_BP_2}>
        <ModalBase {...props}>
          <StyledModal
            $width={props.width}
            $backgroundColor={backgroundColor}
            $useFullHeight={useFullHeight}
            $suppressAutoScroll={suppressAutoScroll}
            open={isOpen}
          >
            {children}
          </StyledModal>
        </ModalBase>
      </ResponsiveRender>
    </>
  );
};
