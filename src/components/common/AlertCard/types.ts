import { BoxProps } from '@components/common/Box/types';

export interface AlertCardProps extends BoxProps<'div'> {
  /**
   * Key used to store the 'dismiss' state across components and browser sessions.
   * */
  storageKey?: string;

  /**
   * If true, shows the option to dismiss the alert.
   * */
  dismissible?: boolean;

  /**
   * If true and the alert is dismissed, it won't show up again
   * in the current device.
   * */
  permanentlyDismissible?: boolean;

  /**
   * Sets the hyperlink text and href respectively.
   * */
  linkText?: string;
  linkTo?: string;

  /**
   * Can be used instead of linkTo if a special handle is needed.
   * */
  onLinkClick?: () => void;

  /**
   * Sets the alert style. If not given, defaults to 'info' alert.
   * */
  warning?: boolean;
  error?: boolean;

  /**
   * If enabled, the alert card sticks at the top while scrolling.
   * */
  sticky?: boolean;

  onDismiss?: () => void;
}
