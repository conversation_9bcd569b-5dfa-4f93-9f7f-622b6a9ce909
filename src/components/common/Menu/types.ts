import { MouseEventHandler } from 'react';

import { TooltipProps } from '../Tooltip/types';

export interface MenuProps extends Omit<TooltipProps, 'target'> {
  items: Array<MenuItem>;
  subMenuProps?: SubMenuProps;
  target: string;
  mobileTitle?: string;
}

export interface MenuBaseProps extends Omit<TooltipProps, 'target'> {
  target: string;
  mobileTitle?: string;

  renderChildren: (closeMenu: () => void) => React.JSX.Element;
}

export type SubMenuProps = Omit<MenuProps, 'items' | 'target'>;

export interface MenuItem {
  title?: string;
  group?: string;
  icon?: any;
  checked?: boolean;
  disabled?: boolean;
  danger?: boolean;
  subItems?: Array<MenuItem>;
  onClick?: MouseEventHandler<HTMLButtonElement>;
  render?: () => React.JSX.Element;
}
