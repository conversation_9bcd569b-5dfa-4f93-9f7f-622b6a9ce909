import React, { <PERSON>Event, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { groupBy } from 'lodash';
import { v4 as uuid } from 'uuid';

import { Box } from '@components/common/Box';
import { Check, IconIMG } from '@components/common/Icon';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Tooltip } from '@components/common/Tooltip';
import { useTranslation } from '@hooks/useTranslation';
import { useWindowMatchMQ } from '@hooks/useWindowMatchMedia';
import { until } from '@style/mediaQueries';

import { ModalHeader } from '../ModalBase';
import { ModalMeerkat } from '../ModalMeerkat';
import { ResponsiveRender } from '../ResponsiveRender';
import { TooltipRef } from '../Tooltip/types';

import { MenuItemTextView, StyledMenuItemGroupView, StyledMenuItemView, StyledMenuView } from './styles';
import { MenuBaseProps, MenuItem, MenuProps, SubMenuProps } from './types';

const MenuItemButton: React.FC<{ item: MenuItem; subMenuProps?: SubMenuProps; closeMenu?: () => void }> = ({
  item,
  subMenuProps,
  closeMenu,
}) => {
  const isPhone = useWindowMatchMQ(until('phone'));
  const handleMenuClick = useCallback(
    (event: MouseEvent<HTMLButtonElement>, item: MenuItem) => {
      event.preventDefault();
      event.stopPropagation();
      item.onClick && item.onClick(event);
      closeMenu && closeMenu();
    },
    [closeMenu]
  );

  const renderIcon = useCallback((item: MenuItem) => {
    if (item.icon) {
      if (typeof item.icon === 'string') {
        return <IconIMG size={18} src={item.icon} />;
      }

      return <item.icon size={18} />;
    }
    return null;
  }, []);

  const uniqueId = useMemo(() => `${item.title}-submenu-${uuid()}`, [item.title]);

  return (
    <StyledMenuItemView
      disabled={item.disabled}
      onClick={(event) => handleMenuClick(event, item)}
      $danger={item.danger}
      id={uniqueId}
    >
      {renderIcon(item)}
      <MenuItemTextView typography={'Heading3'} weight={'600'}>
        {item.title}
      </MenuItemTextView>

      {item.checked ? (
        <Box marginLeft_025>
          <Check size={16} />
        </Box>
      ) : null}

      {item.subItems?.length ? (
        <>
          <Box marginLeft_025>
            <ChevronRight size={16} />
          </Box>
          <Menu
            trigger={'mouseenter focus click'}
            items={item.subItems}
            target={isPhone ? uniqueId : ''}
            mobileTitle={subMenuProps?.mobileTitle || item.title}
            {...subMenuProps}
          />
        </>
      ) : null}
    </StyledMenuItemView>
  );
};

export const MenuBase: React.FC<MenuBaseProps> = ({
  mobileTitle,
  renderChildren,
  placement = 'bottom',
  hideOnClick = true,
  className = '',
  ...props
}) => {
  const [isModalMenuOpen, setIsModalMenuOpen] = useState(false);
  const tooltipRef = useRef<TooltipRef>(null);

  const modalMenu = useRef<HTMLDivElement>(null);
  const modalMenuTrigger = useRef<HTMLButtonElement | null>(null);

  const t = useTranslation();

  const renderContent = useCallback((children: ReactNode) => children, []);

  const closeModalMenu = useCallback(() => {
    setIsModalMenuOpen(false);
    tooltipRef.current?.hide();
  }, [tooltipRef]);

  useEffect(() => {
    if (props.target) {
      modalMenuTrigger.current = document.getElementById(props.target) as HTMLButtonElement;
    }

    if (modalMenuTrigger.current) {
      modalMenuTrigger.current.onclick = (e) => {
        e.stopPropagation();
        setIsModalMenuOpen(true);
      };
    }
  }, [props.target]);

  return (
    <>
      <ResponsiveRender until="phone">
        <ModalMeerkat ref={modalMenu} isOpen={isModalMenuOpen} onRequestClose={closeModalMenu}>
          <ModalHeader mobileTitle={mobileTitle || t('more_options')} />
          <StyledMenuView className={className}>{renderChildren(closeModalMenu)}</StyledMenuView>
        </ModalMeerkat>
      </ResponsiveRender>

      <ResponsiveRender from="tablet">
        <Tooltip
          ref={tooltipRef}
          interactive
          hideOnClick={hideOnClick}
          placement={placement}
          renderContent={renderContent}
          {...props}
        >
          <StyledMenuView className={className}>{renderChildren(closeModalMenu)}</StyledMenuView>
        </Tooltip>
      </ResponsiveRender>
    </>
  );
};

export const Menu: React.FC<MenuProps> = ({ items, subMenuProps, ...props }) => {
  const groupedItems = useMemo(() => {
    const groupsKeys = new Set(items.map((item) => item.group ?? ''));
    const groups = groupBy(items, (item) => item.group ?? '');
    return Array.from(groupsKeys).map((key) => groups[key]);
  }, [items]);

  const renderChildren = useCallback(
    (closeMenu: () => void) => {
      return (
        <>
          <ResponsiveRender until="phone">
            {groupedItems.map((items, index) => (
              <StyledMenuItemGroupView key={index}>
                {(items || []).map((item, key) =>
                  item.render ? (
                    <React.Fragment key={key}> {item.render()} </React.Fragment>
                  ) : (
                    <MenuItemButton item={item} key={key} subMenuProps={subMenuProps} closeMenu={closeMenu} />
                  )
                )}
              </StyledMenuItemGroupView>
            ))}
          </ResponsiveRender>

          <ResponsiveRender from="tablet">
            {groupedItems.map((items, index) => (
              <StyledMenuItemGroupView key={index}>
                {(items || []).map((item, key) => (
                  <MenuItemButton item={item} key={key} subMenuProps={subMenuProps} />
                ))}
              </StyledMenuItemGroupView>
            ))}
          </ResponsiveRender>
        </>
      );
    },
    [groupedItems, subMenuProps]
  );

  return <MenuBase renderChildren={renderChildren} {...props} />;
};
