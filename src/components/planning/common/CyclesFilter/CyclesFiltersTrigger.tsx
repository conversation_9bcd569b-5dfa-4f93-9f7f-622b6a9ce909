import React, { useCallback, useState } from 'react';

import { Filter } from '@components/common/Icon/presets/Filter';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { useTranslation } from '@hooks/useTranslation';
import { useSelectedCycleFocuses } from '@redux/Planning/hooks';

import { CyclesFilter } from './CyclesFilter';
import { StyledFilterButton, StyledFilterCount } from './styles';

const CyclesFiltersTrigger: React.FC = () => {
  const { selectedFocuses } = useSelectedCycleFocuses();
  const [isFiltersModalOpen, setFiltersModalOpen] = useState(false);

  const t = useTranslation();

  const appliedFiltersCount = selectedFocuses.length;
  const isFilterCountVisible = appliedFiltersCount > 0;

  const closeFiltersModal = useCallback(() => setFiltersModalOpen(false), []);

  const openFiltersModal = useCallback(() => setFiltersModalOpen(true), []);

  return (
    <>
      <ResponsiveRender until="phone">
        <StyledFilterButton iconOnly onClick={openFiltersModal} aria-label={t('planning:filter_cycles')} type="button">
          <Filter size={24} />
          <StyledFilterCount $isVisible={isFilterCountVisible}>{appliedFiltersCount}</StyledFilterCount>
        </StyledFilterButton>
      </ResponsiveRender>
      <ResponsiveRender from="tablet">
        <StyledFilterButton primary withLeadingIcon onClick={openFiltersModal} type="button">
          <Filter />
          {t('planning:filter_cycles')}
          <StyledFilterCount $isVisible={isFilterCountVisible}>{appliedFiltersCount}</StyledFilterCount>
        </StyledFilterButton>
      </ResponsiveRender>

      <CyclesFilter isOpen={isFiltersModalOpen} onRequestClose={closeFiltersModal} />
    </>
  );
};

export default CyclesFiltersTrigger;
