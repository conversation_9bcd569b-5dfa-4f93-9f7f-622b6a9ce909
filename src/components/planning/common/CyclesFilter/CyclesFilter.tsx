import React, { useCallback, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { capitalize } from 'lodash';
import { useForm, SubmitHandler } from 'react-hook-form';

import { Box } from '@components/common/Box';
import { Loading } from '@components/common/Loading';
import { Modal, ModalFooter, ModalHeader } from '@components/common/Modal';
import { ModalProps } from '@components/common/Modal/types';
import { ModalContent } from '@components/common/ModalBase/ModalContent';
import { Text } from '@components/common/Text';
import { Form } from '@components/form/core/Form';
import { useFormDefaultValues } from '@components/form/core/Form/hooks';
import { InputCheckbox } from '@components/form/inputs/InputCheckbox';
import { CycleFocusIcon } from '@components/planning/common/CycleFocusIcon';
import { useTranslation } from '@hooks/useTranslation';
import { useSelectedCycleFocuses } from '@redux/Planning/hooks';

import {
  StyledClearFiltersButton,
  StyledClearFiltersButtonText,
  StyledFilterItem,
  StyledFilterSection,
  StyledFilterSectionContent,
  StyledFilterSectionContentInner,
  StyledFilterSectionHeader,
  StyledFiltersFormContent,
  StyledFiltersFormHeader,
} from './styles';

interface CycleFilterFormValues {
  focuses: Record<BeeFocusType, boolean>;
}

export const CyclesFilter: React.FC<Omit<ModalProps, 'children'>> = (props) => {
  const { isOpen, onRequestClose } = props;
  const { selectedFocuses, setSelectedFocuses } = useSelectedCycleFocuses();
  const t = useTranslation();
  const { isFetching } = useSelector((state) => state.planningReducer.visitList.cycles);
  const focusBranding = useSelector((state) => state.planningReducer.visitList.cycles.focusBranding ?? []);

  const getInitialFormValues = useCallback(() => {
    const initialValues: Record<BeeFocusType, boolean> = {} as Record<BeeFocusType, boolean>;

    focusBranding.forEach(branding => {
      initialValues[branding.focus] = false;
    });

    selectedFocuses.forEach(focus => {
      initialValues[focus] = true;
    });

    return { focuses: initialValues };
  }, [focusBranding, selectedFocuses]);

  const form = useForm<CycleFilterFormValues>({
    defaultValues: useFormDefaultValues(getInitialFormValues()),
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      form.reset(getInitialFormValues());
    }
  }, [isOpen, form, getInitialFormValues]);

  const onValidSubmit = useCallback<SubmitHandler<CycleFilterFormValues>>(
    (data) => {
      const newSelectedFocuses = Object.entries(data.focuses)
        .filter(([_, isSelected]) => isSelected)
        .map(([focus]) => focus as BeeFocusType);

      setSelectedFocuses(newSelectedFocuses);
      onRequestClose?.();
    },
    [onRequestClose, setSelectedFocuses]
  );

  const onFilterCancel = useCallback(() => {
    form.reset(getInitialFormValues());
    onRequestClose?.();
  }, [form, getInitialFormValues, onRequestClose]);

  const clearFilters = useCallback(() => {
    const clearedValues = { ...form.getValues() };
    Object.keys(clearedValues.focuses).forEach(key => {
      clearedValues.focuses[key as BeeFocusType] = false;
    });
    form.reset(clearedValues);
  }, [form]);

  const focusOptions = useMemo(() => {
    return focusBranding.map((branding) => ({
      focus: branding.focus,
      label: t(`common:${branding.focus}`),
    }));
  }, [focusBranding, t]);

  const clearButton = (
    <StyledClearFiltersButton type="button" onClick={clearFilters}>
      <StyledClearFiltersButtonText>{t('clear')}</StyledClearFiltersButtonText>
    </StyledClearFiltersButton>
  );

  return (
    <Modal {...props}>
      <ModalHeader mobileTitle={t('filters')} mobileLeftContent={<Box marginHorizontal_050>{clearButton}</Box>} />

      <ModalContent>
        <Form
          id="cycles-filters-form"
          form={form}
          onValidSubmit={onValidSubmit}
          column
        >
          <StyledFiltersFormHeader>
            <Text typography={'Heading2'} weight={'600'}>
              {t('planning:cycle_filters')}
            </Text>
            {clearButton}
          </StyledFiltersFormHeader>

          <StyledFiltersFormContent>
            <StyledFilterSection>
              <StyledFilterSectionHeader>
                <Box alignItems={'center'}>
                  <Text typography={'SmallParagraph'} weight={'600'}>
                    {capitalize(t('common:focus'))}
                  </Text>
                </Box>
              </StyledFilterSectionHeader>

              <StyledFilterSectionContent>
                <StyledFilterSectionContentInner>
                  {focusOptions.map(({ focus, label }) => (
                    <StyledFilterItem key={`focus-${focus}`} isDisabled={false}>
                      <Box marginRight_100>
                        <InputCheckbox
                          name={`focuses.${focus}`}
                          hiddenLabelText={label}
                        />
                      </Box>
                      <Box alignItems={'center'} gap_050>
                        <CycleFocusIcon focus={focus} />
                        <Text typography={'Paragraph'} color={'grey08'}>
                          {capitalize(label)}
                        </Text>
                      </Box>
                    </StyledFilterItem>
                  ))}
                </StyledFilterSectionContentInner>
              </StyledFilterSectionContent>
            </StyledFilterSection>
          </StyledFiltersFormContent>
        </Form>
      </ModalContent>
      <ModalFooter
        rejectText={t('cancel')}
        rejectButtonProps={{ type: 'button' }}
        acceptText={t('apply')}
        acceptButtonProps={{ type: 'submit', form: 'cycles-filters-form' }}
        onRejectClick={onFilterCancel}
      />
      <Loading visible={isFetching} whiteBackground />
    </Modal>
  );
};
