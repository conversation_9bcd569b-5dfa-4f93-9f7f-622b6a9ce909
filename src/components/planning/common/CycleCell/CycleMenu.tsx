import React, { useMemo } from 'react';
import { useHistory } from 'react-router';

import { Box } from '@components/common/Box';
import { Add } from '@components/common/Icon/presets/Add';
import { Assign } from '@components/common/Icon/presets/Assign';
import { Delete } from '@components/common/Icon/presets/Delete';
import { Edit } from '@components/common/Icon/presets/Edit';
import { Menu } from '@components/common/Menu';
import { useDynamicModal } from '@components/common/ModalBase/hooks';
import { ThreeDotsMenuTrigger } from '@components/common/ThreeDotsMenuTrigger';
import APP from '@config/constants';
import { URLUtil } from '@helpers/URL';
import { useTranslation } from '@hooks/useTranslation';

import { CreateOrUpdateCycleModal } from '../../VisitsByCrewList/CreateOrUpdateCycleModal';
import { CycleDeleteModal } from '../../VisitsByCrewList/CycleDeleteModal';
import { CycleReassignVisitsModal } from '../../VisitsByCrewList/CycleReassignVisitsModal';

import { CycleMenuLayout } from './styles';

interface CycleMenuProps {
  cycle: BeeCycle;
  selectCycle: () => void;
  selected: boolean;
  menuId: string;
}

export const CycleMenu: React.FC<CycleMenuProps> = ({ cycle, selected, selectCycle, menuId }) => {
  const t = useTranslation();
  const updateCycleModal = useDynamicModal();
  const deleteModal = useDynamicModal();
  const reassignVisitsModal = useDynamicModal();

  const history = useHistory();

  const menuItems = useMemo(
    () => [
      {
        title: t('planning:add_visit'),
        icon: Add,
        onClick: () =>
          history.push(URLUtil.buildPagePath(APP.routes.planningVisitsCreation, { queryParams: { cycle: cycle.id } })),
      },
      {
        title: t('planning:cycle_edit'),
        icon: Edit,
        onClick: () => updateCycleModal.open(),
      },
      {
        title: t('planning:cycle_reassign_all_visits'),
        icon: Assign,
        onClick: () => reassignVisitsModal.open(),
      },
      {
        title: t('planning:cycle_delete'),
        icon: Delete,
        onClick: () => deleteModal.open(),
        danger: true,
      },
    ],
    [cycle.id, deleteModal, history, reassignVisitsModal, t, updateCycleModal]
  );

  const menuKey = `cycle-menu-${menuId}-${cycle.id}`;

  return (
    <CycleMenuLayout onClick={(e) => e.stopPropagation()}>
      <Box fit justifyContent={'flex-end'} relative>
        <ThreeDotsMenuTrigger id={menuKey} />
        <Menu items={menuItems} placement={'bottom-end'} target={menuKey} />
      </Box>
      <CreateOrUpdateCycleModal cycle={cycle} modalOpenerProps={updateCycleModal} />
      <CycleReassignVisitsModal {...reassignVisitsModal.modalProps} cycle={cycle} />
      <CycleDeleteModal
        {...deleteModal.modalProps}
        cycle={cycle}
        openReassignVisitModal={reassignVisitsModal.open}
        selected={selected}
        selectCycle={selectCycle}
      />
    </CycleMenuLayout>
  );
};
