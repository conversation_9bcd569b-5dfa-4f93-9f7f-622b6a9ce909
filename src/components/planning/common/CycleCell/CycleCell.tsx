import { useMemo } from 'react';

import { Box } from '@components/common/Box';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { HiveDefault } from '@components/common/Icon/presets/HiveDefault';
import { People } from '@components/common/Icon/presets/People';
import { Yard } from '@components/common/Icon/presets/Yard';
import { ProgressBar } from '@components/common/ProgressBar';
import { Text } from '@components/common/Text';
import { Tooltip } from '@components/common/Tooltip';
import { CycleMenu } from '@components/planning/common/CycleCell/CycleMenu';
import { useDateUtil } from '@helpers/Date/hooks';
import { useTranslation } from '@hooks/useTranslation';

import { CycleFocusIcon } from '../CycleFocusIcon';

import { CycleCellLayout, CycleDataPill, CycleName, CycleNameWrapper, ProgressBarLayout } from './styles';

const CycleCell: React.FC<{
  cycle: BeeCycle;
  menuId: string;
  selectCycle?: () => void;
  isSelected?: boolean;
  showMenu?: boolean;
  isSelectable?: boolean;
  showDates?: boolean;
  showData?: boolean;
}> = ({ cycle, selectCycle, isSelected, showMenu, isSelectable, menuId, showDates, showData }) => {
  const dateUtil = useDateUtil();
  const t = useTranslation();

  const cycleDetails = useMemo(
    () => [
      {
        icon: <Yard />,
        label: t('Yards'),
        value: cycle.nbYards,
      },
      {
        icon: <HiveDefault />,
        label: t('Hives'),
        value: cycle.nbHives,
      },
      {
        icon: <People />,
        label: cycle.crews?.map((crew) => crew.name).join('\n') || t('crews'),
        value: cycle.crews?.length,
      },
    ],
    [cycle, t]
  );

  return (
    <CycleCellLayout
      onClick={() => isSelectable && selectCycle?.()}
      key={cycle.id}
      $isSelected={isSelected}
      $isSelecteAble={!!isSelectable}
    >
      <Box>
        <CycleFocusIcon focus={cycle.focus} />
        <Text typography={'Heading3'} weight={'600'} color={'contentPrimary'}>
          <CycleNameWrapper>
            <CycleName>{cycle.name}</CycleName>
          </CycleNameWrapper>
        </Text>
        {showMenu && selectCycle && (
          <CycleMenu cycle={cycle} selected={!!isSelected} selectCycle={selectCycle} menuId={menuId} />
        )}
      </Box>
      {showDates && (
        <Box alignItems={'center'} gap_025>
          <Text typography={'CaptionSmall'} color={'contentTertiary'}>
            {dateUtil.getFormattedReadableDate(cycle.startsOn)}
          </Text>
          <ChevronRight />
          <Text typography={'CaptionSmall'} color={'contentTertiary'}>
            {dateUtil.getFormattedReadableDate(cycle.endsOn)}
          </Text>
        </Box>
      )}
      <ProgressBarLayout>
        <ProgressBar progress={cycle.numberTotalVisits ? cycle.numberCompletedVisits / cycle.numberTotalVisits : 0} />
        <Text typography={'CaptionSmall'} color={'contentTertiary'}>
          {`${cycle.numberCompletedVisits} / ${cycle.numberTotalVisits}`}
        </Text>
      </ProgressBarLayout>
      {showData && (
        <Box alignItems={'center'} justifyContent="flex-end" gap_050>
          {cycleDetails.map(({ label, icon, value }) => (
            <Box key={label}>
              <CycleDataPill>
                {icon}
                <Text typography={'CaptionSmall'} weight={'600'}>
                  {value}
                </Text>
              </CycleDataPill>
              <Tooltip>
                <Text typography={'CaptionSmall'} weight={'600'}>
                  {label}
                </Text>
              </Tooltip>
            </Box>
          ))}
        </Box>
      )}
    </CycleCellLayout>
  );
};

export default CycleCell;
