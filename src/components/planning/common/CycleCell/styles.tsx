import styled from 'styled-components';
import { css } from 'styled-components';

import { Box } from '@components/common/Box';

export const CycleCellLayout = styled(Box).attrs({
  marginBottom_025: true,
  padding_050: true,
  marginLeft_050: true,
  marginRight_050: true,
  column: true,
  gap_025: true,
  borderRadius_050: true,
  borderWidth: 1,
})<{ $isSelected?: boolean; $isSelecteAble?: boolean }>(
  ({ theme, $isSelected, $isSelecteAble }) => css`
    border-color: ${$isSelected ? theme.colors.borderSecondary : theme.colors.surfaceSecondary};
    ${$isSelected && `background-color: ${theme.colors.surfaceSecondaryHover};`}
    &:hover {
      border-color: ${$isSelecteAble ? theme.colors.borderSecondary : theme.colors.surfaceSecondary};
    }
    ${$isSelecteAble && 'cursor: pointer;'}
    align-items: stretch;
    width: 100%;
  `
);

export const CycleMenuLayout = styled.div(
  ({ theme }) => css`
    position: absolute;
    right: ${theme.spacing._0625};
    margin-top: -${theme.spacing._050};
  `
);

export const CycleName = styled.div`
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export const CycleNameWrapper = styled(Box)(
  () => css`
    max-width: 210px;
  `
);

export const ProgressBarLayout = styled(Box).attrs({
  alignItems: 'center',
  gap_050: true,
})(
  () => css`
    span {
      width: 62px;
    }
  `
);

export const CycleDataPill = styled(Box).attrs({
  alignItems: 'center',
})(
  ({ theme }) => css`
    gap: ${theme.spacing._025};
    background-color: ${theme.colors.surfaceDefault};
    padding: ${theme.spacing._0125} ${theme.spacing._025};
    border-radius: ${theme.shape.paperRadius01}px;
    border: 1px solid ${theme.colors.borderSecondary};

    svg {
      width: 12px;
      height: 12px;
    }
  `
);
