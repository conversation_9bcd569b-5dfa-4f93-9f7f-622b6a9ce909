import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Box } from '@components/common/Box';
import { ChevronLeft } from '@components/common/Icon/presets/ChevronLeft';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Loading } from '@components/common/Loading';
import { Text } from '@components/common/Text';
import CycleCell from '@components/planning/common/CycleCell';
import CyclesFilters from '@components/planning/common/CyclesFilter';
import { useSelectedCycle } from '@components/planning/VisitsByCrewList/hooks';
import { useDateUtil } from '@helpers/Date/hooks';
import { useTranslation } from '@hooks/useTranslation';

import { GANTT_CHART_LABEL_WIDTH, QuarterDirection } from './constants';
import {
  CycleDayIndices,
  useCycleDayIndices,
  useCyclesData,
  useNumberOfQuarterDays,
  useQuarterDayOffsets,
  useQuarterMonths,
  useQuarterNavigation,
} from './hooks';
import {
  GanttChartBar,
  GanttChartContent,
  GanttChartContentLayout,
  GanttChartDateLine,
  GanttChartDayLabel,
  GanttChartHeader,
  GanttChartLabel,
  GanttChartQuarterButton,
  GanttChartQuarterNavigation,
  GanttChartRow,
  GanttChartTitle,
  GanttChartTodayLine,
  GanttChartTooltip,
  TodayLabel,
} from './styles';

const MS_PER_DAY = 24 * 60 * 60 * 1000;

interface HoverInfo {
  position: number;
  date: string;
}

export const CyclesGanttChart: React.FC = () => {
  const t = useTranslation();
  const dateUtil = useDateUtil();
  const ref = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [timeLineWidth, setTimeLineWidth] = useState(0);
  const [hoverInfo, setHoverInfo] = useState<HoverInfo | null>(null);
  const { selectCycle, cycleId } = useSelectedCycle();

  useEffect(() => {
    if (ref.current) {
      const { width } = ref.current.getBoundingClientRect();
      setTimeLineWidth(width);
    }
  }, [ref]);

  const { quarterDates, quarterLabel, navigateQuarter } = useQuarterNavigation();
  const { cycles, focusBrandingByType, isLoading, didInitialFetch } = useCyclesData(quarterDates);
  const daysInQuarter = useNumberOfQuarterDays(quarterDates);
  const monthsInQuarter = useQuarterMonths(quarterDates);
  const quarterDayOffsets = useQuarterDayOffsets(quarterDates);
  const dayWidth = useMemo(() => timeLineWidth / daysInQuarter, [timeLineWidth, daysInQuarter]);
  const cycleDayIndices = useCycleDayIndices(cycles, quarterDates);

  const todayPosition = useMemo(() => {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Check if today is within the current quarter
    if (todayStart >= quarterDates.startDate && todayStart <= quarterDates.endDate) {
      const dayIndex = Math.floor((todayStart.getTime() - quarterDates.startDate.getTime()) / MS_PER_DAY);
      return GANTT_CHART_LABEL_WIDTH + dayIndex * dayWidth;
    }

    return null;
  }, [quarterDates.startDate, quarterDates.endDate, dayWidth]);

  const handleChartMouseMove = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!contentRef.current) return;

      const rect = contentRef.current.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;

      // Calculate which day the mouse is over
      const dayIndex = Math.floor((mouseX - GANTT_CHART_LABEL_WIDTH) / dayWidth);

      // Ensure we're within the valid day range
      if (dayIndex >= 0 && dayIndex < daysInQuarter) {
        const position = dayIndex * dayWidth + GANTT_CHART_LABEL_WIDTH;

        // Calculate the date for this day
        const currentDate = new Date(quarterDates.startDate.getTime() + (dayIndex + 1) * MS_PER_DAY);
        const dateString = dateUtil.getFormattedISODate(currentDate);

        setHoverInfo({
          position,
          date: dateString,
        });
      } else {
        setHoverInfo(null);
      }
    },
    [dayWidth, daysInQuarter, quarterDates.startDate, dateUtil]
  );

  const handleChartMouseLeave = useCallback(() => {
    setHoverInfo(null);
  }, []);

  const renderCycleRows = (cycleDayIndices: CycleDayIndices[]) => {
    return cycleDayIndices.map(({ startDay, endDay, cycle }) => {
      const daysInCycle = endDay - startDay + 1;
      return (
        <GanttChartRow key={cycle.id}>
          <GanttChartLabel>
            <CycleCell
              cycle={cycle}
              selectCycle={() => selectCycle(cycle.id)}
              isSelected={String(cycle.id) === cycleId}
              showMenu={cycle.status !== 'completed'}
              menuId="gantt"
              showData
              fullWidth
            />
          </GanttChartLabel>

          <GanttChartBar
            style={{
              left: startDay * dayWidth,
              width: daysInCycle === daysInQuarter ? daysInCycle * dayWidth - 1 : daysInCycle * dayWidth,
            }}
            $color={focusBrandingByType[cycle.focus].color}
            key={cycle.id}
          />
        </GanttChartRow>
      );
    });
  };

  return (
    <Box column fullWidth>
      <GanttChartQuarterNavigation>
        <GanttChartQuarterButton onClick={() => navigateQuarter(QuarterDirection.PREVIOUS)}>
          <ChevronLeft size={16} />
        </GanttChartQuarterButton>

        <Text typography="Heading3" weight="600">
          {quarterLabel}
        </Text>

        <GanttChartQuarterButton onClick={() => navigateQuarter(QuarterDirection.NEXT)}>
          <ChevronRight size={16} />
        </GanttChartQuarterButton>
        <Box flexGrow={1} justifyContent="flex-end">
          <CyclesFilters />
        </Box>
      </GanttChartQuarterNavigation>

      <GanttChartHeader>
        <GanttChartTitle>{t('planning:cycles').toUpperCase()}</GanttChartTitle>

        <Box column fit ref={ref} fullHeight>
          <Box fullWidth fit>
            {monthsInQuarter.map((month) => (
              <Box key={month} fit alignItems="center" justifyContent="center">
                <Text typography="CaptionSmall" color="contentTertiary">
                  {month}
                </Text>
              </Box>
            ))}
          </Box>
          <Box fit alignItems="center">
            {quarterDayOffsets.map((dayOffset, index) => (
              <GanttChartDayLabel key={dayOffset} left={GANTT_CHART_LABEL_WIDTH + dayOffset * dayWidth}>
                {index % 2 === 0 ? '01' : '15'}
              </GanttChartDayLabel>
            ))}
          </Box>
          {todayPosition !== null && (
            <TodayLabel left={todayPosition}>
              <Text typography={'CaptionSmall'}>{t('today')}</Text>
            </TodayLabel>
          )}
        </Box>
      </GanttChartHeader>

      {cycles.length > 0 && (
        <GanttChartContent>
          <GanttChartContentLayout
            ref={contentRef}
            onMouseMove={handleChartMouseMove}
            onMouseLeave={handleChartMouseLeave}
          >
            {renderCycleRows(cycleDayIndices)}
            {todayPosition !== null && <GanttChartTodayLine left={todayPosition} />}
            {hoverInfo && (
              <>
                {todayPosition !== hoverInfo.position && <GanttChartDateLine left={hoverInfo.position} />}
                <GanttChartTooltip left={GANTT_CHART_LABEL_WIDTH + hoverInfo.position}>
                  <Text typography={'CaptionSmall'}>{dateUtil.getFormattedDate(hoverInfo.date, 'DD')}</Text>
                </GanttChartTooltip>
              </>
            )}
          </GanttChartContentLayout>
        </GanttChartContent>
      )}

      <Loading visible={!didInitialFetch || isLoading} blurBackground />
    </Box>
  );
};
