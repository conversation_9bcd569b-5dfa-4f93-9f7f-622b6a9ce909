import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Box } from '@components/common/Box';
import { ChevronLeft } from '@components/common/Icon/presets/ChevronLeft';
import { ChevronRight } from '@components/common/Icon/presets/ChevronRight';
import { Loading } from '@components/common/Loading';
import { Text } from '@components/common/Text';
import CycleCell from '@components/planning/common/CycleCell';
import CyclesFilters from '@components/planning/common/CyclesFilter';
import { useSelectedCycle } from '@components/planning/VisitsByCrewList/hooks';
import { useDateUtil } from '@helpers/Date/hooks';
import { useTranslation } from '@hooks/useTranslation';

import { GANTT_CHART_LABEL_WIDTH, QuarterDirection } from './constants';
import {
  CycleDayIndices,
  useCycleDayIndices,
  useCyclesData,
  useNumberOfQuarterDays,
  useQuarterDayOffsets,
  useQuarterMonths,
  useQuarterNavigation,
} from './hooks';
import {
  GanttChartBar,
  GanttChartContent,
  GanttChartContentLayout,
  GanttChartDateLine,
  GanttChartDayLabel,
  GanttChartHeader,
  GanttChartLabel,
  GanttChartQuarterButton,
  GanttChartQuarterNavigation,
  GanttChartRow,
  GanttChartTitle,
  GanttChartTooltip,
} from './styles';

interface HoverInfo {
  cycleId: string;
  edge: 'start' | 'end';
  position: number;
  top: number;
  date: string;
  cycleName: string;
  isWithinQuarter: boolean;
}

const HOVER_ZONE_WIDTH = 15; // pixels from each edge

export const CyclesGanttChart: React.FC = () => {
  const t = useTranslation();
  const dateUtil = useDateUtil();
  const ref = useRef<HTMLDivElement>(null);
  const [timeLineWidth, setTimeLineWidth] = useState(0);
  const [hoverInfo, setHoverInfo] = useState<HoverInfo | null>(null);
  const { selectCycle, cycleId } = useSelectedCycle();

  useEffect(() => {
    if (ref.current) {
      const { width } = ref.current.getBoundingClientRect();
      setTimeLineWidth(width);
    }
  }, [ref]);

  const { quarterDates, quarterLabel, navigateQuarter } = useQuarterNavigation();
  const { cycles, focusBrandingByType, isLoading, didInitialFetch } = useCyclesData(quarterDates);
  const daysInQuarter = useNumberOfQuarterDays(quarterDates);
  const monthsInQuarter = useQuarterMonths(quarterDates);
  const quarterDayOffsets = useQuarterDayOffsets(quarterDates);
  const dayWidth = useMemo(() => timeLineWidth / daysInQuarter, [timeLineWidth, daysInQuarter]);
  const cycleDayIndices = useCycleDayIndices(cycles, quarterDates);

  const handleBarMouseMove = useCallback(
    (
      event: React.MouseEvent<HTMLDivElement>,
      cycle: BeeCycle,
      startDay: number,
      endDay: number,
      daysInCycle: number
    ) => {
      const rect = event.currentTarget.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const barWidth = daysInCycle * dayWidth;

      // Check if hovering near start edge
      if (mouseX <= HOVER_ZONE_WIDTH) {
        const position = GANTT_CHART_LABEL_WIDTH + startDay * dayWidth - 2;
        const top = rect.top + rect.height / 2;
        const date = dateUtil.getDateFromFormattedISO(cycle.startsOn);
        const isWithinQuarter = date >= quarterDates.startDate && date <= quarterDates.endDate;
        setHoverInfo({
          cycleId: String(cycle.id),
          edge: 'start',
          position,
          top,
          date: cycle.startsOn,
          cycleName: cycle.name,
          isWithinQuarter,
        });
      }
      // Check if hovering near end edge
      else if (mouseX >= barWidth - HOVER_ZONE_WIDTH) {
        let position = GANTT_CHART_LABEL_WIDTH + (endDay + 1) * dayWidth;
        if (endDay === daysInQuarter - 1) {
          position -= 2;
        }
        const top = rect.top + rect.height / 2;
        const date = dateUtil.getDateFromFormattedISO(cycle.endsOn);
        const isWithinQuarter = date >= quarterDates.startDate && date <= quarterDates.endDate;
        setHoverInfo({
          cycleId: String(cycle.id),
          edge: 'end',
          position,
          top,
          date: cycle.endsOn,
          cycleName: cycle.name,
          isWithinQuarter,
        });
      }
      // Clear hover if in middle area
      else {
        setHoverInfo(null);
      }
    },
    [dayWidth, daysInQuarter, quarterDates.startDate, quarterDates.endDate, dateUtil]
  );

  const handleBarMouseLeave = useCallback(() => {
    setHoverInfo(null);
  }, []);

  const renderCycleRows = (cycleDayIndices: CycleDayIndices[]) => {
    return cycleDayIndices.map(({ startDay, endDay, cycle }) => {
      const daysInCycle = endDay - startDay + 1;
      return (
        <GanttChartRow key={cycle.id}>
          <GanttChartLabel>
            <CycleCell
              cycle={cycle}
              selectCycle={() => selectCycle(cycle.id)}
              isSelected={String(cycle.id) === cycleId}
              showMenu={cycle.status !== 'completed'}
              menuId="gantt"
              showData
              fullWidth
            />
          </GanttChartLabel>

          <GanttChartBar
            style={{
              left: startDay * dayWidth,
              width: daysInCycle === daysInQuarter ? daysInCycle * dayWidth - 1 : daysInCycle * dayWidth,
            }}
            $color={focusBrandingByType[cycle.focus].color}
            key={cycle.id}
            onMouseMove={(event) => handleBarMouseMove(event, cycle, startDay, endDay, daysInCycle)}
            onMouseLeave={handleBarMouseLeave}
          />
        </GanttChartRow>
      );
    });
  };

  return (
    <Box column fullWidth>
      <GanttChartQuarterNavigation>
        <GanttChartQuarterButton onClick={() => navigateQuarter(QuarterDirection.PREVIOUS)}>
          <ChevronLeft size={16} />
        </GanttChartQuarterButton>

        <Text typography="Heading3" weight="600">
          {quarterLabel}
        </Text>

        <GanttChartQuarterButton onClick={() => navigateQuarter(QuarterDirection.NEXT)}>
          <ChevronRight size={16} />
        </GanttChartQuarterButton>
        <Box flexGrow={1} justifyContent="flex-end">
          <CyclesFilters />
        </Box>
      </GanttChartQuarterNavigation>

      <GanttChartHeader>
        <GanttChartTitle>{t('planning:cycles').toUpperCase()}</GanttChartTitle>

        <Box column fit ref={ref} fullHeight>
          <Box fullWidth fit>
            {monthsInQuarter.map((month) => (
              <Box key={month} fit alignItems="center" justifyContent="center">
                <Text typography="CaptionSmall" color="contentTertiary">
                  {month}
                </Text>
              </Box>
            ))}
          </Box>
          <Box fit alignItems="center">
            {quarterDayOffsets.map((dayOffset, index) => (
              <GanttChartDayLabel key={dayOffset} left={GANTT_CHART_LABEL_WIDTH + dayOffset * dayWidth}>
                {index % 2 === 0 ? '01' : '15'}
              </GanttChartDayLabel>
            ))}
          </Box>
        </Box>
      </GanttChartHeader>

      {cycles.length > 0 && (
        <GanttChartContent>
          <GanttChartContentLayout>
            {renderCycleRows(cycleDayIndices)}
            {hoverInfo && (
              <>
                <GanttChartDateLine left={hoverInfo.position} />
                <GanttChartTooltip
                  left={GANTT_CHART_LABEL_WIDTH + hoverInfo.position}
                  top={hoverInfo.top}
                  isWithinQuarter={hoverInfo.isWithinQuarter}
                >
                  <Text typography={'CaptionSmall'}>
                    {dateUtil.getFormattedDate(hoverInfo.date, hoverInfo.isWithinQuarter ? 'DD' : 'DD/MM/YY')}
                  </Text>
                </GanttChartTooltip>
              </>
            )}
          </GanttChartContentLayout>
        </GanttChartContent>
      )}

      <Loading visible={!didInitialFetch || isLoading} blurBackground />
    </Box>
  );
};
