import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import produce from 'immer';

import { AlertCard } from '@components/common/AlertCard';
import { Box } from '@components/common/Box';
import { DragDropListProvider } from '@components/common/DragDropList';
import { DragEndEvent } from '@components/common/DragDropList/types';
import { Loading } from '@components/common/Loading';
import { RequireFlags } from '@components/common/RequireFlags/RequireFlags.tsx';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { ADD_VISIT_PRIORITY_OFFSET } from '@components/planning/VisitsByCrewList/constants';
import { useTransientCrewsListState } from '@components/planning/VisitsByCrewList/hooks';
import { StyledVisitsContainer } from '@components/planning/VisitsByCrewList/styles.ts';
import VisitsByCrewEmpty from '@components/planning/VisitsByCrewList/VisitsByCrewEmpty';
import VisitsByCrewRow from '@components/planning/VisitsByCrewList/VisitsByCrewRow';
import { Analytics } from '@helpers/Analytics';
import { AnalyticsEventType } from '@helpers/Analytics/types';
import { useDispatch } from '@helpers/Thunk/hooks';
import { useTranslation } from '@hooks/useTranslation.ts';
import { makeAssignVisitToCrewThunk } from '@redux/Planning/actions';

import { CyclesList } from './CyclesList';

export const VisitsByCrewList: React.FC = () => {
  const t = useTranslation();
  const dispatch = useDispatch();
  const { crews, setCrews } = useTransientCrewsListState();
  const { didInitialFetch, isFetching } = useSelector((state) => state.planningReducer.visitList.visits);

  const isEmpty = useMemo(() => {
    return crews.every((crew) => crew.visits.length === 0);
  }, [crews]);

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const nextCrews = produce(crews, (crews) => {
        const isMoveValid = event.fromListId !== event.toListId || event.fromIndex !== event.toIndex;
        if (!isMoveValid) return;

        const fromCrew = crews.find((crew) => crew.id === event.fromListId);
        const toCrew = crews.find((crew) => crew.id === event.toListId);
        if (!fromCrew || !toCrew) return;

        const [movedCard] = fromCrew.visits.splice(event.fromIndex, 1);
        toCrew.visits.splice(event.toIndex, 0, movedCard);

        const visitAtRight = toCrew.visits[event.toIndex + 1];
        const newPriority = visitAtRight
          ? visitAtRight.priority + ADD_VISIT_PRIORITY_OFFSET
          : -ADD_VISIT_PRIORITY_OFFSET;

        // Normalizing priorities so the view updates immediately.
        fromCrew.visits.forEach((visit, index, arr) => (visit.priority = arr.length - index - 1));
        toCrew.visits.forEach((visit, index, arr) => (visit.priority = arr.length - index - 1));

        dispatch(
          makeAssignVisitToCrewThunk({
            fromCrewId: fromCrew.id,
            toCrewId: toCrew.id,
            visitId: movedCard.id,
            newPriority,
          })
        );
        Analytics.sendEvent({ event: AnalyticsEventType.PLANNING_ASSIGN_VISIT_TO_CREW });
      });

      setCrews(nextCrews);
    },
    [crews, setCrews, dispatch]
  );

  if (isEmpty && didInitialFetch && !isFetching) {
    return <VisitsByCrewEmpty noCrews={crews?.length === 0} />;
  }

  return (
    <StyledVisitsContainer>
      <RequireFlags releaseFlags={['CYCLES']}>
        <CyclesList />
      </RequireFlags>

      <Box overflow="auto" fit stretch>
        <DragDropListProvider onDragEnd={handleDragEnd}>
          <Box fit column stretch gap_050>
            <ResponsiveRender until={'tablet'}>
              <AlertCard>{t('planning:cannot_create_visit_on_mobile_warning')}</AlertCard>
            </ResponsiveRender>
            {crews.map(({ id, name, visits, nbMembers }) => (
              <VisitsByCrewRow key={id} id={id} name={name} visits={visits} nbMembers={nbMembers} />
            ))}
          </Box>
        </DragDropListProvider>
      </Box>
      <Loading visible={!didInitialFetch || isFetching} blurBackground />
    </StyledVisitsContainer>
  );
};
