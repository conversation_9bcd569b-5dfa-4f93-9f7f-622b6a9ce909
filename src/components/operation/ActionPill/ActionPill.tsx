import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { Edit } from '@components/common/Icon/presets/Edit.tsx';
import { Text } from '@components/common/Text';
import { Form } from '@components/form/core/Form';
import { InputCheckbox } from '@components/form/inputs/InputCheckbox';
import { StyledActionPillView } from '@components/operation/ActionPill/styles';
import { ActionPillProps } from '@components/operation/ActionPill/types';
import { usePractices } from '@redux/Practices/hooks';

export const ActionPill: React.FC<ActionPillProps> = ({
  actionId,
  disabled,
  onEditClick,
  onEnableClick,
  onDisableClick,
  ...props
}) => {
  const { getAction, getActionName, getPracticeByAction } = usePractices();
  const action = getAction(actionId);
  const practice = getPracticeByAction(actionId);
  const isParentActive = practice?.isActive;
  const isActive = action?.isActive;

  const innerForm = useForm({ defaultValues: { active: isActive } });
  const isActiveInner = innerForm.watch('active');

  useEffect(() => {
    if (!action) return;
    if (isActiveInner !== isActive) {
      isActiveInner ? onEnableClick?.(action) : onDisableClick?.(action);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isActiveInner]);

  if (!action) {
    return null;
  }

  const isDefault = practice?.fieldType === 'system';

  return (
    <StyledActionPillView alignItems={'center'} gap_100 {...props}>
      <Text typography={'SmallParagraph'}>{getActionName(action.id)}</Text>

      <Box justifyContent={'flex-end'} gap_100>
        {!isDefault && (
          <Form form={innerForm}>
            <InputCheckbox name={'active'} readOnly={disabled || !isParentActive} />
          </Form>
        )}

        <Button suppressPadding onClick={() => onEditClick?.(action)}>
          <Edit color={'contentTertiary'} />
        </Button>
      </Box>
    </StyledActionPillView>
  );
};
