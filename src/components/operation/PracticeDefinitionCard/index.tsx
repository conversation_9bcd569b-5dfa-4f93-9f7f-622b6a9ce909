import React, { ComponentType, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import Fuse from 'fuse.js';

import { Box } from '@components/common/Box';
import { Card, CardContent, CardHeader } from '@components/common/Card';
import { Button } from '@components/common/CTA';
import { Add } from '@components/common/Icon/presets/Add';
import { Hide } from '@components/common/Icon/presets/Hide';
import { HiveDefault } from '@components/common/Icon/presets/HiveDefault';
import { InfoCircle } from '@components/common/Icon/presets/InfoCircle';
import { MoreVert } from '@components/common/Icon/presets/MoreVert';
import { Pill } from '@components/common/Icon/presets/Pill';
import { Search } from '@components/common/Icon/presets/Search.tsx';
import { Show } from '@components/common/Icon/presets/Show';
import { Slider } from '@components/common/Icon/presets/Slider';
import { Yard } from '@components/common/Icon/presets/Yard';
import { IconSVGProps } from '@components/common/Icon/types';
import { LoadingSkeleton } from '@components/common/LoadingSkeleton';
import { LoadingSkeletonRectangle } from '@components/common/LoadingSkeleton/LoadingSkeletonRectangle';
import { Menu } from '@components/common/Menu';
import { useDynamicModal } from '@components/common/ModalBase/hooks';
import { PracticeIcon } from '@components/common/PracticeIcon';
import { ResponsiveRender } from '@components/common/ResponsiveRender';
import { Text } from '@components/common/Text';
import { Tooltip } from '@components/common/Tooltip';
import { ActionPill, ActionPillSlider } from '@components/operation/ActionPill';
import { capitalizeFirst } from '@helpers/deprecated/capitalizeFirst';
import { Sorting } from '@helpers/Sorting';
import { makeEnableDisableActionRequestThunk, makeOpenBeekPracticesModalAction } from '@redux/deprecated/actions';
import { makePracticeDisabledThunk, makePracticeEnableThunk, makePracticesFetchThunk } from '@redux/Practices/actions';
import { usePractices } from '@redux/Practices/hooks';

import {
  StyledCategoryActions,
  StyledCategoryActionsFilter,
  StyledCategoryActionsInner,
  StyledHeaderPill,
  StyledHeaderPillWrapper,
  StyledStyledHeader,
  StyledTooltip,
  StyledTooltipAnchor,
} from './styles';
import { PracticeDefinitionCardProps } from './types';

const DEF_NON_EXTENDABLE_ACTIONS: Array<BeePracticeDefinition['fieldType'] | undefined> = ['system', 'slider'];
const DEF_FIELD_TYPE_ICONS: Record<BeePracticeDefinition['fieldType'], ComponentType<IconSVGProps> | null> = {
  system: null,
  checkbox: Pill,
  radio: Pill,
  slider: Slider,
};

export const PracticeDefinitionCard: React.FC<PracticeDefinitionCardProps> = ({ practiceDefinitionId, ...props }) => {
  const [filter, setFilter] = React.useState<string>('');

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const user = useSelector((state) => state.authReducer.user);
  const { getPractice, getPracticeByAction, getPracticeName, getActionName, isFetchingPractice } = usePractices();
  const practice = getPractice(practiceDefinitionId);
  const isSystem = practice?.fieldType === 'system';
  const canAddActions = !DEF_NON_EXTENDABLE_ACTIONS.includes(practice?.fieldType);

  const disablePracticeModal = useDynamicModal();

  const actions = useMemo(() => practice?.actions.filter((action) => !action.isDeleted) || [], [practice]);
  const sortedActions = useMemo(
    () =>
      Sorting.sort(actions, (a, b) => {
        return Sorting.alphanumericSortFunc(getActionName(a.id), getActionName(b.id));
      }),
    [actions, getActionName]
  );
  const filteredActions = useMemo(() => {
    const preFiltered = sortedActions.filter(
      ({ isActive }) => !user?.membershipPreferences.settingsPracticesHideDisabled || isActive
    );
    return filter.trim()
      ? new Fuse(preFiltered, { keys: ['name.EN'], threshold: 0.4 }).search(filter).map(({ item }) => item)
      : preFiltered;
  }, [filter, sortedActions, user?.membershipPreferences.settingsPracticesHideDisabled]);

  const isEmpty = !actions.length;

  const FieldTypeIcon = practice ? DEF_FIELD_TYPE_ICONS[practice.fieldType] : null;

  const openAddActionModal = useCallback(() => {
    practice && dispatch(makeOpenBeekPracticesModalAction({ modalType: 'create-practice', practice }));
  }, [practice, dispatch]);

  const openEditActionModal = useCallback(
    (action: BeeAction) => {
      practice && dispatch(makeOpenBeekPracticesModalAction({ modalType: 'update-practice', practice, action }));
    },
    [practice, dispatch]
  );

  const handleDisableAction = useCallback(
    (action: BeeAction) => {
      dispatch(makeEnableDisableActionRequestThunk(action.id, { is_active: false }));
      const practice = getPracticeByAction(action.id);
      practice && dispatch(makePracticesFetchThunk({ practiceId: practice.id }));
    },
    [dispatch, getPracticeByAction]
  );

  const handleEnableAction = useCallback(
    async (action: BeeAction) => {
      await dispatch(makeEnableDisableActionRequestThunk(action.id, { is_active: true }));
      const practice = getPracticeByAction(action.id);
      practice && dispatch(makePracticesFetchThunk({ practiceId: practice.id }));
    },
    [dispatch, getPracticeByAction]
  );

  const handleEnablePractice = useCallback(() => {
    if (practice) {
      dispatch(makePracticeEnableThunk(practice.id));
    }
  }, [dispatch, practice]);

  const openDisablePracticeModal = useCallback(() => {
    disablePracticeModal.open({
      headerProps: {
        title: t('practice_disable_modal_title'),
        subtitle: t(
          practice?.flag === 'retired_reason'
            ? 'practice_disable_modal_message_reason_for_deadout'
            : 'practice_disable_modal_message',
          { name: practice?.name.EN ?? '' }
        ),
      },
      footerProps: {
        acceptText: t('disable'),
        rejectText: t('cancel'),
        autoCloseOnAccept: true,
        autoCloseOnReject: true,
        onAcceptClick: () => {
          if (practice) {
            dispatch(makePracticeDisabledThunk(practice.id));
          }
        },
      },
    });
  }, [disablePracticeModal, dispatch, practice, t]);

  const renderHeader = useCallback(() => {
    const name = capitalizeFirst(getPracticeName(practice?.id));
    const tooltipId = `category-help-tooltip-${practice?.id}`;
    const menuId = `category-menu-${practice?.id}`;

    return (
      <Box fit gap_075 alignItems={'flex-start'}>
        <StyledStyledHeader fit gap_075>
          <Box fit alignItems={'center'} gap_050>
            <PracticeIcon practiceId={practice?.id} size={16} />

            <Text typography={'Heading3'} weight={'600'}>
              {name}

              {!practice?.isActive && (
                <>
                  {' '}
                  <Text htmlTag={'span'} typography={'Heading3'} weight={'600'} color={'grey06'}>
                    [{t('disabled')}]
                  </Text>
                </>
              )}
            </Text>
            {isSystem ? (
              <>
                <StyledTooltipAnchor id={tooltipId}>
                  <InfoCircle color={'contentLight'} />
                </StyledTooltipAnchor>
                <StyledTooltip target={tooltipId}>
                  <Text typography={'TooltipSmall'}>{capitalizeFirst(t('category_non_editable_tooltip'))}</Text>
                </StyledTooltip>
              </>
            ) : null}
          </Box>

          <StyledHeaderPillWrapper center>
            {!!practice && !isSystem && (
              <StyledHeaderPill borderColor={'grey05'}>
                {!!FieldTypeIcon && <FieldTypeIcon size={16} />}
                <ResponsiveRender from={'tablet'}>
                  <Text typography={'TooltipSmall'}>{t(`practice_type_${practice.fieldType}`)}</Text>
                </ResponsiveRender>
              </StyledHeaderPill>
            )}

            <StyledHeaderPill
              $color={practice?.isHiveData ? 'blue03' : 'purple01'}
              borderColor={practice?.isHiveData ? 'blue03' : 'purple01'}
            >
              {practice?.isHiveData ? <HiveDefault /> : <Yard />}
              <ResponsiveRender from={'tablet'}>
                <Text typography={'TooltipSmall'}>{t(practice?.isHiveData ? 'Hives' : 'yards_capitalized')}</Text>
              </ResponsiveRender>

              <Tooltip>
                <Text typography={'TooltipSmall'}>
                  {t(practice?.isHiveData ? 'practice_hive_data_tooltip' : 'practice_yard_data_tooltip')}
                </Text>
              </Tooltip>
            </StyledHeaderPill>
          </StyledHeaderPillWrapper>
        </StyledStyledHeader>

        {!isSystem && (
          <>
            <Button id={menuId} suppressPadding>
              <MoreVert color={'contentTertiary'} size={24} />
            </Button>
            <Menu
              items={[
                practice?.isActive
                  ? { title: t('disable'), icon: Hide, onClick: openDisablePracticeModal }
                  : { title: t('enable'), icon: Show, onClick: handleEnablePractice },
              ]}
              target={menuId}
            />
          </>
        )}
      </Box>
    );
  }, [FieldTypeIcon, getPracticeName, handleEnablePractice, isSystem, openDisablePracticeModal, practice, t]);

  const renderContent = useCallback(() => {
    return (
      <StyledCategoryActions>
        {practice?.fieldType !== 'slider' && (
          <Box alignItems={'center'} gap_050>
            <StyledCategoryActionsFilter
              placeholder={t('filter_actions')}
              onChange={(ev) => setFilter(ev.target.value)}
              value={filter}
            />
            <Search size={20} />
          </Box>
        )}

        <StyledCategoryActionsInner $fitLeft={practice?.fieldType === 'slider'}>
          {!practice ? null : practice?.fieldType === 'slider' ? (
            <ActionPillSlider>{`${practice.config.slider.min}-${practice.config.slider.max}`}</ActionPillSlider>
          ) : (
            filteredActions.map(({ id }) => (
              <ActionPill
                key={id}
                actionId={id}
                disabled={isFetchingPractice[practice.id]}
                onEditClick={openEditActionModal}
                onDisableClick={handleDisableAction}
                onEnableClick={handleEnableAction}
              />
            ))
          )}
          {canAddActions ? (
            <Button tertiary withLeadingIcon onClick={openAddActionModal}>
              <Add />
              {t('add_action')}
            </Button>
          ) : null}
        </StyledCategoryActionsInner>
      </StyledCategoryActions>
    );
  }, [
    t,
    filter,
    practice,
    filteredActions,
    canAddActions,
    openAddActionModal,
    isFetchingPractice,
    openEditActionModal,
    handleDisableAction,
    handleEnableAction,
  ]);

  if (!practice || (isEmpty && isSystem)) return null;

  return (
    <Card relative {...props}>
      <CardHeader>{renderHeader()}</CardHeader>
      <CardContent>{renderContent()}</CardContent>

      <LoadingSkeleton
        visible={isFetchingPractice[practice.id]}
        absolutelyFitParent
        backgroundColor={'overlayWhiteBackground'}
      >
        <LoadingSkeletonRectangle width={'100%'} height={'100%'} borderRadius_150 backgroundColor={'grey02'} />
      </LoadingSkeleton>

      {disablePracticeModal.content}
    </Card>
  );
};
