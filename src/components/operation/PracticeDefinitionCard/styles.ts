import styled, { Color, css } from 'styled-components';

import { Box } from '@components/common/Box';
import { Button } from '@components/common/CTA';
import { Tooltip } from '@components/common/Tooltip';
import { from } from '@style/mediaQueries';

export const StyledCategoryActions = styled(Box).attrs({ gap_100: true, column: true, stretch: true })``;

export const StyledCategoryActionsInner = styled(Box).attrs({ column: true })<{ $fitLeft: boolean }>(
  ({ theme, $fitLeft }) => css`
    border: ${theme.colors.borderSecondary} 1px solid;
    border-radius: ${theme.borderRadius._050};

    ${$fitLeft &&
    css`
      align-self: flex-start;
    `}

    & > div:not(:last-child) {
      border-bottom: ${theme.colors.borderSecondary} 1px solid;
    }
  `
);

export const StyledCategoryActionsFilter = styled.input(
  ({ theme }) => css`
    flex-wrap: wrap;
    padding: ${theme.spacing._050} ${theme.spacing._075};
    border-radius: ${theme.borderRadius._050};
    border: ${theme.colors.borderSecondary} 1px solid;
    align-self: flex-start;
    min-width: 240px;
  `
);

export const StyledPlusButton = styled(Button)(
  ({ theme }) => `
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  border: 1px solid ${theme.colors.borderSecondary};
`
);

export const StyledStyledHeader = styled(Box)(
  () => css`
    flex-direction: column-reverse;
    align-items: flex-start;

    ${from('tablet')} {
      flex-direction: row;
      align-items: center;
    }
  `
);

export const StyledHeaderPillWrapper = styled(Box)(
  ({ theme }) => css`
    gap: ${theme.spacing._050};

    ${from('tablet')} {
      gap: ${theme.spacing._100};
    }
  `
);

export const StyledHeaderPill = styled(Box).attrs({
  paddingVertical_025: true,
  paddingHorizontal_050: true,
  gap_025: true,
  borderRadius_025: true,
  borderWidth: 1,
})<{ $color?: Color }>(
  ({ theme, $color }) => css`
    background-color: ${$color ? theme.colors.alpha($color, 0.3) : 'inherit'};
  `
);

export const StyledTooltipAnchor = styled.span(
  () => css`
    cursor: pointer;
  `
);

export const StyledTooltip = styled(Tooltip)(
  () => css`
    max-width: 252px;
  `
);
