import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { makeClearSelectedCycleFocusesAction, makeSetSelectedCycleFocusesAction } from './actions';

export const useSelectedCycleFocuses = () => {
  const dispatch = useDispatch();
  const selectedFocuses = useSelector((state) => state.planningReducer.visitList.selectedCycleFocuses);

  const setSelectedFocuses = useCallback(
    (focuses: BeeFocusType[]) => {
      dispatch(makeSetSelectedCycleFocusesAction(focuses));
    },
    [dispatch]
  );

  const clearSelectedFocuses = useCallback(() => {
    dispatch(makeClearSelectedCycleFocusesAction());
  }, [dispatch]);

  return {
    selectedFocuses,
    setSelectedFocuses,
    clearSelectedFocuses,
  };
};
