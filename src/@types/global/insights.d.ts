export {};

declare global {
  /**
   * Stores info about an insight chart created by the user.
   * */
  type BeeInsight = BeeInsightText | BeeInsightDataVisualization;

  interface BeeInsightText {
    id: number | string;
    title: string;
    content: string;
    type: BeeInsightTextType;
  }

  interface BeeInsightDataVisualization {
    id: number | string;
    title: string;
    description: string;
    type: BeeInsightDataVisualizationType;
    typeForComparison: BeeInsightType | null;
    datasetKey: BeeInsightsDatasetKey;
    timeframe: BeeInsightTimeframe;
    horizontalAxisDateGranularity: BeeInsightsDateGranularity;
    horizontalAxisPropertyKey: BeeInsightsDatasetPropertyKey;
    verticalAxisPropertyKey: BeeInsightsDatasetPropertyKey;
    verticalAxisAggregation: BeeInsightsAggregationType;
    verticalAxisComparisonPropertyKey: BeeInsightsDatasetPropertyKey | null;
    verticalAxisComparisonAggregation: BeeInsightsAggregationType | null;

    breakdownPropertyKey: BeeInsightsDatasetPropertyKey | null;

    sorting: {
      propertyKey: BeeInsightsDatasetPropertyKey;
      direction: BeeInsightSortingDirection;
    } | null;

    filters: Array<BeeInsightFilter>;

    pagination: BeeInsightPagination | null;

    visibleLabels: Record<BeeInsightsDatasetPropertyKey, boolean> | null;
    visibleComparisonLabels: Record<BeeInsightsDatasetPropertyKey, boolean> | null;
  }

  interface BeeInsightPreset {
    id: number;
    insight: BeeInsightDataVisualization;
    sampleData: unknown;
    categories: Array<BeeInsightPresetCategory>;
    isRecommended: boolean;
  }

  interface BeeInsightPresetCategory {
    id: number;
    title: string;
    description: string;
  }

  interface BeeInsightsDashboard {
    id: number;
    title: string;
    description: string;
    insightsPlacements: Array<BeeInsightPlacement>;
  }

  interface BeeInsightPlacement {
    id: number;
    insightId: number;
    layout: BeeInsightPlacementLayout;
  }

  interface BeeInsightPlacementLayout {
    top: number;
    left: number;
    width: number;
    height: number;
  }

  export interface BeeInsightTimeframe {
    mode: BeeInsightTimeframeMode;
    fixed: { start: string; end: string };
    relative: { amount: number; unit: BeeInsightsDateGranularity };
    toNow: {
      start: string;
      maxEnd: string;
    };
  }

  export type BeeInsightTimeframeMode = 'fixed' | 'relative' | 'to-now';
  export type BeeInsightType = BeeInsightTextType | BeeInsightDataVisualizationType;
  export type BeeInsightTextType = 'heading' | 'text';
  export type BeeInsightDataVisualizationType =
    | 'chart-line'
    | 'chart-bar'
    | 'chart-pie'
    | 'table'
    | 'metric'
    | 'map-with-dots';
  export type BeeInsightsAggregationType = 'count' | 'sum' | 'avg' | 'max' | 'min' | 'percent';
  export type BeeInsightsDateGranularity = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'season';
  export type BeeInsightSortingDirection = 'asc' | 'desc';

  /**
   * Definition of an applied dataset filter.
   * */
  interface BeeInsightFilter {
    propertyKey: BeeInsightsDatasetPropertyKey;
    value: {
      oneOf?: Array<any>;
      betweenNumbers?: { from: number | null; to: number | null };
      betweenDates?: { from: string | null; to: string | null };
    };
  }

  /**
   * Definition of the applied chart/table pagination
   * */
  interface BeeInsightPagination {
    pageIndex: number;
    pageSize: number;
  }

  /**
   * Stores meta info about an available dataset.
   * */
  interface BeeInsightsDatasetMeta {
    /** Dataset unique identifier. */
    key: BeeInsightsDatasetKey;

    /** Dataset type. */
    type: BeeInsightsDatasetType;

    /**
     * Visual hints.
     * */
    name: string;
    description: string;

    /** Property used to uniquely identify entries. */
    idPropertyKey: string;

    /** On trend datasets, used as x axis. */
    datePropertyKey: string;

    /**
     * Says which prop can be used as a label for the respective id prop.
     * For the 'yards' dataset, for example, it would map 'id' to 'name'.
     * */
    labelPropertyKeys: Record<BeeInsightsDatasetPropertyKey, BeeInsightsDatasetPropertyKey>;

    /**
     * Spec for the dataset available props.
     * */
    properties: Record<BeeInsightsDatasetPropertyKey, BeeInsightsDatasetPropertyMeta>;
  }

  /** Stores meta info about a dataset property. */
  interface BeeInsightsDatasetPropertyMeta {
    /** Property unique identifier. Also used as property name in the real dataset. */
    key: BeeInsightsDatasetPropertyKey;

    /** The property type. */
    type: BeeInsightsDatasetPropertyType;

    /** The property readable name. */
    label: string;

    /** The property readable group name. */
    group?: string;
  }

  /** Available property types. */
  type BeeInsightsDatasetPropertyType =
    | 'id'
    | 'integer'
    | 'float'
    | 'percentage'
    | 'string'
    | 'enum'
    | 'bool'
    | 'datetime'
    | 'geolocation';

  /**
   * While 'BeeInsightsDatasetMeta' stores metadata regarding how the database
   * can be queried, this stores an actual query result.
   *
   * The meta about properties and axis is also present here, since, depending
   * on the aggregation and breakdowns, the data shape might change.
   * */
  interface BeeInsightsDatasetQuery {
    dataset: BeeInsightsAggregatedDataset;
    properties: Record<BeeInsightsDatasetPropertyKey, BeeInsightsDatasetPropertyMeta>;
    horizontalAxisPropertyKey: BeeInsightsDatasetPropertyKey;
    verticalAxisAggregations: Array<BeeInsightsVerticalAxisAggregation>;
    verticalAxisComparisonAggregations: Array<BeeInsightsVerticalAxisAggregation>;
  }

  interface BeeInsightsAggregatedDataset extends BeeInsightsDataset {
    aggregatedEntries: Array<Record<string, Array<BeeInsightsDatasetEntry>>>;
  }

  /**
   * Defines an aggregation performed on the vertical (y) axis.
   * */
  interface BeeInsightsVerticalAxisAggregation {
    /**
     * The aggregated property.
     * Ex.: hive_count, yard_type
     * */
    originPropertyKey: BeeInsightsDatasetPropertyKey;

    /**
     * The property generated by the aggregation.
     * Ex.: hive_count__sum, yard_type__pollination__count
     * */
    aggregatedPropertyKey: BeeInsightsDatasetPropertyKey;

    /**
     * This prop points to a list of entries used in this aggregation.
     * */
    aggregatedEntriesPropertyKey: BeeInsightsDatasetPropertyKey | null;

    /**
     * The aggregation type.
     * */
    aggregationType: BeeInsightsAggregationType | null;

    /**
     * The aggregated value.
     * Ex.: null, pollination, honey, wintering, ...
     * */
    aggregatedValue: BeeInsightsDatasetValue | null;

    /**
     * Whether a simple output aggregation was generated.
     * Multiple aggregations may happen when the aggregation is performed
     * on a categorical property.
     *
     * Ex.: if the aggregation is performed on yard_type, output
     * aggregations are generated for each yard type value found,
     * in which case this value will be false.
     * */
    isSimpleAggregation: boolean;
  }

  interface BeeInsightsDataset {
    entries: Array<BeeInsightsDatasetEntry>;
  }

  type BeeInsightsDatasetEntry = Record<BeeInsightsDatasetPropertyKey, BeeInsightsDatasetValue>;
  type BeeInsightsDatasetValue = string | number | Date | BeeInsightsDatasetValueGeolocation | null;
  type BeeInsightsDatasetKey = string;
  type BeeInsightsDatasetType = 'standard' | 'trend';
  type BeeInsightsDatasetPropertyKey = string;

  interface BeeInsightsDatasetValueGeolocation {
    lat: number;
    lng: number;
  }

  interface BeeInsightsPreferences {
    dashboards: {
      /**
       * Array of ids defining the order of the visible dashboards.
       * */
      orderedIds: Array<number>;
    };
  }
}
