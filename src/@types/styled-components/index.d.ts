import 'styled-components';

declare module 'styled-components' {
  /**
   * Maps the given props interface to a new type
   * with a '$' before each prop. Useful when creating
   * styled elements that take the same props as the
   * component.
   * usage:
   *   export type StyledProps = PrefixedStyledProps<{ suppressPadding: boolean }>;
   *   // StyledProps will output as { $suppressPadding: boolean } //
   * */
  type PrefixedStyledProps<Props> = {
    [P in keyof Props as `$${P}`]: Props[P];
  };

  type ColorPrimitive =
    | 'white'
    | 'background'
    | 'black'
    | 'grey01'
    | 'grey02'
    | 'grey03'
    | 'grey04'
    | 'grey05'
    | 'grey06'
    | 'grey07'
    | 'grey08'
    | 'orange01'
    | 'orange02'
    | 'orange03'
    | 'green01'
    | 'green02'
    | 'green03'
    | 'yellow01'
    | 'yellow02'
    | 'red01'
    | 'red02'
    | 'red03'
    | 'blue01'
    | 'blue02'
    | 'focus01'
    | 'focus02'
    | 'purple01'
    | 'blue03'
    | 'overlayBackground'
    | 'overlayWhiteBackground';

  type ColorGlobal =
    | 'surfaceDefault'
    | 'surfaceDisabled'
    | 'surfacePrimary'
    | 'surfacePrimaryHover'
    | 'surfacePrimaryReverse'
    | 'surfaceSecondary'
    | 'surfaceSecondaryHover'
    | 'surfaceTertiary'
    | 'surfaceTertiaryHover'
    | 'surfaceSubtle'
    | 'surfaceAccent'
    | 'surfaceWarning'
    | 'surfaceWarningLight'
    | 'surfaceDanger'
    | 'surfaceDangerHover'
    | 'surfaceDangerLight'
    | 'surfacePositive'
    | 'surfacePositiveLight'
    | 'surfaceInfo'
    | 'surfaceInfoLight'
    | 'surfaceComponentDisabled'
    | 'contentDisabled'
    | 'contentPrimary'
    | 'contentPrimaryHover'
    | 'contentPrimaryReverse'
    | 'contentSecondary'
    | 'contentTertiary'
    | 'contentLight'
    | 'contentHighlight'
    | 'contentHighlightBright'
    | 'contentPositive'
    | 'contentDanger'
    | 'contentInfo'
    | 'borderPrimary'
    | 'borderPrimaryHover'
    | 'borderSecondary'
    | 'borderSecondaryStateHover'
    | 'borderPrimaryReverse'
    | 'borderTertiary'
    | 'borderDisabled'
    | 'borderDanger'
    | 'borderDangerHover'
    | 'borderPositive'
    | 'borderWarning'
    | 'borderInfo'
    | 'borderFocus'
    | 'borderFocusLight'
    | 'yardStatusActive'
    | 'yardStatusInactive'
    | 'yardStatusUnloaded'
    | 'yardStatusEmptied';

  export type ColorPrimitiveMap = { [C in ColorPrimitive]: string };

  type ValuePrimitive =
    | '_0125'
    | '_025'
    | '_050'
    | '_0625'
    | '_075'
    | '_0875'
    | '_100'
    | '_125'
    | '_150'
    | '_200'
    | '_250'
    | '_300'
    | '_350'
    | '_600';
  type ValuePrimitiveMap = { [V in ValuePrimitive]: number };

  export interface Primitives {
    colors: ColorPrimitiveMap;
    /**
     * Values in REM
     * @example _050: 0.5
     */
    rem: ValuePrimitiveMap;
    /**
     * Values in px
     * @example _050: 8
     */
    px: ValuePrimitiveMap;
  }

  type ColorGlobalMap = { [CT in ColorGlobal]: string };

  export interface Globals {
    colors: ColorGlobalMap;
  }

  type Color = ColorPrimitive | ColorGlobal;

  type BorderRadiusMap = {
    /**
     * 4px
     */
    _025: string;
    /**
     * 8px
     */
    _050: string;
    /**
     * 16px
     */
    _100: string;
  };

  type SizeIconMap = {
    /**
     * 0.5rem (8px)
     */
    _050: string;
    /**
     * 1rem (16px)
     */
    _100: string;
    /**
     * 1.5rem (24px)
     */
    _150: string;
  };

  type LineHeightMap = {
    /**
     * 1rem (16px)
     */
    _100: string;
    /**
     * 1.5rem (24px)
     */
    _150: string;
  };

  type ValueGlobalMap = {
    _0: string;
    /**
     * 2px
     */
    _0125: string;
    /**
     * 4px
     */
    _025: string;
    /**
     * 8px
     */
    _050: string;
    /**
     * 10px
     */
    _0625: string;
    /**
     * 12px
     */
    _075: string;
    /**
     * 14px
     */
    _0875: string;
    /**
     * 16px
     */
    _100: string;
    /**
     * 20px
     */
    _125: string;
    /**
     * 24px
     */
    _150: string;
    /**
     * 32px
     */
    _200: string;
    /**
     * 40px
     */
    _250: string;
    /**
     * 48px
     */
    _300: string;
    /**
     * 56px
     */
    _350: string;
    /**
     * 96px
     */
    _600: string;
  };

  type BorderRadius = keyof BorderRadiusMap;
  type SizeIcon = keyof SizeIconMap;
  type LineHeight = keyof LineHeightMap;
  type Spacing = keyof ValueGlobalMap;

  type PaperRadius = 'paperRadius01' | 'paperRadius02' | 'paperRadius03';
  type PaperRadiusNumberMap = Record<PaperRadius, number>;

  type Shadow = 'boxShadow01' | 'boxShadow02' | 'boxShadow03' | 'boxShadow04';
  type ShadowStringMap = Record<Shadow, string>;

  type ModalWidth = 'base' | 'trends' | 'small' | 'gallery' | 'imageView';
  type ModalWidthMap = Record<ModalWidth, number>;

  type zIndexItems =
    | 'base'
    | 'content'
    | 'justAboveContent' // todo: replace with something more semantic
    | 'table'
    | 'tableBody'
    | 'tableCell'
    | 'tableHeaderLeft'
    | 'tableHeaderTop'
    | 'tableHeaderTopLeft'
    | 'tableShadow'
    | 'tableOverlay'
    | 'tabs'
    | 'stickyAlert'
    | 'siteHeader'
    | 'sidenavOverlay'
    | 'sidenav'
    | 'highLevelIndex' // todo: replace with something more semantic
    | 'modal'
    | 'modalContent'
    | 'modalFooter'
    | 'modalHeader'
    | 'modalMobileHeader'
    | 'modalOverlay'
    | 'loading'
    | 'tooltip'
    | 'snackbar';

  interface DefaultTheme {
    /**
     * @todo: move primitive object out of theme
     * when all primitives have been converted to globals
     */
    primitives: Primitives;

    colors: ColorGlobalMap & {
      getPrimitiveOrGlobalColor: (color: Color) => string;
      filter: (color: Color) => string;
      alpha: (color: Color, alpha: number) => string;
    };

    borderRadius: BorderRadiusMap;
    sizeIcon: SizeIconMap;
    lineHeight: LineHeightMap;
    spacing: ValueGlobalMap;
    fontSize: ValueGlobalMap;
    shadows: ShadowStringMap;
    shape: PaperRadiusNumberMap;

    font: {
      family: string;
      // todo: remove this font size property when we fix StyleTextBtn in src/scenes/admin/Settings/UserProfile/styles.ts
      heading3FontSize: string;
    };

    navigation: {
      headerHeight: number;
      headerHeightMobile: number;
      tabBarHeight: number;
      tabBarHeightMobile: number;
    };

    modals: {
      width: ModalWidthMap;
    };

    layout: {
      siteHeaderHeight: number;
      pageHeaderHeight: number;
      tabBarHeight: number;
      sideNavCollapsedWidth: string;
      sideNavExpandedWidth: string;
      sideNavExpandedWidthMobile: string;
    };

    zIndexes: zIndexItems[];
    getZIndexOf: (item: zIndexItems) => number;

    animations: {
      durationFast: number;
      durationMedium: number;
      durationLong: number;
      easing: string;
      transitionFast: (...props) => string;
      transitionMedium: (...props) => string;
      transitionLong: (...props) => string;
    };
  }
}
