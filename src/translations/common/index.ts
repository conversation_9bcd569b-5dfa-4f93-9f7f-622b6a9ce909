import CreateTranslationJson from '@translations/CreateTranslationJson';

const tool = new CreateTranslationJson();

tool.add('avg_hive_movement_description', {
  en: 'Average of times hives were moved in the last 30 days.',
});
tool.add('pollination_total_acres', {
  en: 'Total acres',
  es: 'Acres totales',
});
tool.add('temperature', {
  en: 'Temperature',
});
tool.add('pollination_pois_management_hint_part1', {
  en: 'No points of interest yet. Click on <br/>the ',
});
tool.add('yellow_gold', {
  en: 'yellow gold ',
});
tool.add('remove_hive_success', {
  en: '1 hive was removed from your operation',
});
tool.add('logout', {
  en: 'Logout',
});
tool.add('moved_hives', {
  en: 'Moved hives',
  es: 'Colmenas movida',
});
tool.add('phone_number', {
  en: 'Phone number',
});
tool.add('pollination_cost_per_hive', {
  en: 'Cost per hive',
});
tool.add('not_available', {
  en: 'NA',
});
tool.add('feedback_text', {
  en: `Want to see something different?
Let us know how we can improve`,
});
tool.add('cleared_out', {
  en: 'cleared out',
  es: 'vaciado',
});
tool.add('login_slide_1_text', {
  en: "Utilize the score to gain insights into the probability of your hives' survival after winter. The health score takes into account hive movements, visits, and environmental factors, assigning a score from 1 to 10. Higher scores indicate better odds of survival.",
});
tool.add('no_lost_hives', {
  en: 'No lost hives yet',
});
tool.add('alerts_new_yard_sms', {
  en: 'Enable new yard text notifications',
});
tool.add('pollination_bees_out', {
  en: 'Bees out',
  es: 'Abejas fuera',
});
tool.add('export_data', {
  en: 'Export Data',
});
tool.add('yard_flags', {
  en: 'Yard flags',
});
tool.add('clearing_out', {
  en: 'Clearing out',
});
tool.add('yard_management_restore', {
  en: 'Reset all changes to this yard',
});
tool.add('chart', {
  en: 'Chart',
});
tool.add('show_metrics', {
  en: 'Show metrics',
});
tool.add('pollination_contracts_archived_tab', {
  en: 'Archived',
});
tool.add('view_all', {
  en: 'View all',
});
tool.add('add_field_notes', {
  en: 'Add field notes',
});
tool.add('pollination_contracts', {
  en: 'Pollination contracts',
});
tool.add('pollination_pois_management_hint_part2', {
  en: ' icon on the top bar to start creating your points of interest.',
});
tool.add('metric_query_error', {
  en: 'Metric trend computation failed.',
});
tool.add('map_error_message', {
  en: "Can't load the map.",
});
tool.add('remove_hives_success', {
  en: '{{count}} hives were removed from your operation',
});
tool.add('error_message_max_length', {
  en: 'The value must not exceed {{ max }} characters',
});
tool.add('pop', {
  en: 'Pop',
});
tool.add('map_type', {
  en: 'Map type',
});
tool.add('health_score_no_hives_tooltip', {
  en: 'In order to calculate the health <br/> score, yards must contain hives.',
});
tool.add('last_visit_range_upper_limit', {
  en: 'More than {{from}} days or no visits',
});
tool.add('lost_hive', {
  en: 'Lost hive',
  es: 'Colmena perdida',
});
tool.add('responsive_feature_not_available', {
  en: 'This feature is only available on desktop browsers. For optimal use, access it from your computer.',
});
tool.add('pollination_drop_name_value', {
  en: 'Drop {{name}}',
});
tool.add('all', {
  en: 'All',
});
tool.add('beetrack_alerts', {
  en: 'Email & SMS notifications',
});
tool.add('map_recenter', {
  en: 'Recenter map',
});
tool.add('map_zoom_in', {
  en: 'Zoom in',
});
tool.add('map_zoom_out', {
  en: 'Zoom out',
});
tool.add('pollination_grower_name', {
  en: 'Grower name',
});
tool.add('login_slide_2_title', {
  en: 'Create your pollination contracts',
});
tool.add('crop_type_filter_help_text', {
  en: 'Crop type can be set in the<br/>yard page.',
});
tool.add('reminder_queenless', {
  en: 'Reminder: Queenless Hives',
});
tool.add('remove_hives_desc', {
  en: 'Those hives and their history will be removed from your operation. However, they will not be reflected in the overall count of deadouts for the season. Note that this action is irreversible.',
});
tool.add('edit_contract', {
  en: 'Edit contract',
});
tool.add('view_details', {
  en: 'View details',
});
tool.add('alerts_hive_moved_sms', {
  en: 'Enable hive movement text notifications',
});
tool.add('view_last_occurrence', {
  en: 'Last occurrence',
});
tool.add('name', {
  en: 'Name',
});
tool.add('cancel_changes', {
  en: 'Cancel changes',
});
tool.add('practice_spanish_exists', {
  en: 'Sorry, this Spanish practice already exists for {{detail}}.',
});
tool.add('error_message_max', {
  en: 'The value must be equal to or less than {{ max }}',
});
tool.add('no_yards', {
  en: 'No yards created yet',
});
tool.add('yard_alerts', {
  en: 'Yard alerts',
});
tool.add('no_lost_hives_msg', {
  en: 'Any hives that have been cleared out of yards will be displayed on this page.',
});
tool.add('map_pollination_blocks', {
  en: 'Blocks',
});
tool.add('tooltip_requiring_attention', {
  en: 'Visited over 2 weeks ago.',
});
tool.add('help', {
  en: 'Help',
});
tool.add('clear_out_x_yards_title', {
  en: 'Clear out {{count}} yard?',
});
tool.add('honey', {
  en: 'Honey yard',
});
tool.add('no_members', {
  en: 'No members',
});
tool.add('no_members_added_yet', {
  en: 'No members added yet',
});
tool.add('new_yard_report', {
  en: 'New Yard Inspection Report',
});
tool.add('small_captions', {
  en: 'Small captions',
});
tool.add('tooltip_critical_status', {
  en: 'At least 30% of hives are queenless or dead',
});
tool.add('yard_hive_issues', {
  en: 'Yards with hive issues',
});
tool.add('column_view', {
  en: 'Column view',
});
tool.add('event_beecon', {
  en: "Hive {{hive}}'s beecon is ",
});
tool.add('daily_recap', {
  en: 'Daily recap',
});
tool.add('restore_default', {
  en: 'Restore to default',
});
tool.add('clear_out_and_renew', {
  en: 'Clear out and renew',
});
tool.add('closed', {
  en: 'Closed',
});
tool.add('confirm', {
  en: 'Confirm',
});
tool.add('member_section_description', {
  en: 'Every account gets access to the mobile app and is ready to collect beekeeping data.',
});
tool.add('no_pollination_contracts_message', {
  en: 'To start viewing information on your contracts, you must add a new contract within your operation.',
});
tool.add('removed_hives', {
  en: 'Removed hives',
});
tool.add('move_to_the_left', {
  en: 'Move to the left',
});
tool.add('pollination_pois_hint_part1', {
  en: 'No points of interest yet. Click on <br/>the ',
});
tool.add('manage_columns_header', {
  en: 'Available columns',
});
tool.add('hive_structure_frames_per_honey_supers__other', {
  en: 'Other',
});
tool.add('secondary_big_dark_cta', {
  en: 'Secondary big dark cta',
});
tool.add('timeline_applied_action_tooltip', {
  en: 'Practice was applied to {{count}} hives.',
  es: 'La práctica se aplicó en {{count}} colmenas.',
});
tool.add('keep_editing', {
  en: 'Keep editing',
});
tool.add('updated', {
  en: 'updated',
  es: 'actualizado',
});
tool.add('weekly_recap', {
  en: 'Weekly recap',
});
tool.add('yard_info', {
  en: 'Yard info',
});
tool.add('login_slide_1_title', {
  en: 'View your hives health score',
});
tool.add('yard_successfully_assigned_to_group', {
  en: 'Yards successfully assigned to group',
});
tool.add('show_street_names', {
  en: 'Show street names',
});
tool.add('hive_structure_brood_boxes__three', {
  en: 'Three',
});
tool.add('assign_groups', {
  en: 'Assign to groups',
});
tool.add('annotation_deadout', {
  en: '(Deadout)',
  es: '(Muerta)',
});
tool.add('hive_inspections', {
  en: 'Hive inspections',
});
tool.add('alerts_hive_moved_mail', {
  en: 'Enable hive movement email notifications',
});
tool.add('yards_selected_header', {
  en: '{{number}} yards selected.',
});
tool.add('clear_out_x_yards_alert_single', {
  en: '1 of the selected yards is already inactive. It will be ignored.',
});
tool.add('snack_account_changed_msg', {
  en: 'Your account has been updated',
});
tool.add('yards_deselect', {
  en: 'Clear selection.',
});
tool.add('pollination_drops_management_hint_part1', {
  en: 'No drops yet. Click on the ',
});
tool.add('select_operation_message', {
  en: "Choose the operation you'd like to work on today. <br/> If you're assigned to multiple operations, you can <br/> easily switch between them while logged in.",
});
tool.add('greeting', {
  en: 'Hi {{ username}}!',
});
tool.add('beetrack_alerts_desc_1', {
  en: 'Set up your notification preferences and be updated of all important events happening within your operation. Notification settings apply to each operations so you can customize them for each operations you work with.',
});
tool.add('tooltips_hover', {
  en: 'Tooltips (hover only)',
});
tool.add('clear_out_yards', {
  en: 'Clear out yards',
});
tool.add('pollination_contracts_ongoing_tab', {
  en: 'Ongoing',
});
tool.add('edit_yard', {
  en: 'Edit yard',
});
tool.add('action_exists', {
  en: 'Sorry, this action already exists.',
});
tool.add('no_groups', {
  en: 'No groups added yet',
});
tool.add('clear', {
  en: 'Clear',
});
tool.add('alerts_new_hives', {
  en: '{{number}} hives added to {{where}}',
});
tool.add('with_queenless', {
  en: 'With queenless',
});
tool.add('recovered_bulk_queenless', {
  en: '{{number}} hives are back to ',
});
tool.add('yards_generic_error_msg', {
  en: 'Please correct all issues and try again.',
});
tool.add('start_date', {
  en: 'Start date',
});
tool.add('snack_password_invalid', {
  en: 'Incorrect password',
});
tool.add('deadouts', {
  en: 'Deadouts',
});
tool.add('map_view', {
  en: 'Map view',
});
tool.add('outdoor wintering', {
  en: 'Outdoor wintering',
});
tool.add('pollination_poi_category_road', {
  en: 'Road',
  es: 'Carretera',
});
tool.add('click_to_remove_filter', {
  en: 'Click to remove this filter',
});
tool.add('snack_refresh', {
  en: 'Refresh',
});
tool.add('active_yards', {
  en: 'Active yards',
});
tool.add('renewing', {
  en: 'Renewing',
});
tool.add('tooltip_hive_avg_pop_trend', {
  en: 'Population trend : increasing, decreasing, steady',
});
tool.add('new', {
  en: 'New',
});
tool.add('delete_active_yard_warning', {
  en: 'This yard is still active. Before deleting a yard from your operation, it must be cleared out.',
});
tool.add('last_report', {
  en: 'Last report',
});
tool.add('in', {
  en: 'in',
});
tool.add('no_result', {
  en: 'No result',
});
tool.add('profile_change_name_last_name_placeholder', {
  en: 'Enter last name',
});
tool.add('all_right_devices', {
  en: 'All devices look good',
});
tool.add('reports_elap', {
  en: 'ELAP',
});
tool.add('unselected', {
  en: 'Unselected',
});
tool.add('current_season_trend', {
  en: 'Current season',
});
tool.add('view_on_map', {
  en: 'View on map',
});
tool.add('view_sum_since_season_start', {
  en: 'Sum since season start',
});
tool.add('snack_network_error', {
  en: 'It seems that you are offline, please reconnect and refresh',
});
tool.add('last_visit_range_upper_limit_split1', {
  en: 'More than',
});
tool.add('login_subtitle', {
  en: 'Sign in to your account below.',
});
tool.add('practice_disable_modal_title', {
  en: 'Disable category',
});
tool.add('pollination_hives_required_header_hint', {
  en: 'Hives required to fulfill<br/> the contract.',
});
tool.add('tooltip_no_email_msg', {
  en: 'Contact us to update your email',
});
tool.add('thursday', {
  en: 'Thu',
});
tool.add('remove_action_instructions_cont', {
  en: ' will be removed from the worker’s Nectar mobile app but will remain visible in your reporting dashboard.',
});
tool.add('not_available_description', {
  en: 'Not enough data to display this metric.',
});
tool.add('view_occurrences_since_season_start_short', {
  en: 'Occurrences since season start',
});
tool.add('unsaved_changes_exit_modal_accept', {
  en: 'Yes, cancel changes',
});
tool.add('error_message_number', {
  en: 'Please enter a valid numeric value.',
});
tool.add('switch_operation', {
  en: 'Switch operation',
});
tool.add('not_transmitting_beecons_error_msg', {
  en: 'No beecons are transmitting',
});
tool.add('hive_structure_bottom_boards__other', {
  en: 'Other',
});
tool.add('operation_profile_help_text', {
  en: 'Filling your operation profile will help set reports up and our support team to better serve you. Long term, it also improves Beetrack.',
});
tool.add('abnormal', {
  en: 'Abnormal',
});
tool.add('tooltip_good_status', {
  en: 'All hives are queenright and alive',
});
tool.add('pollination_hives_on_site_header', {
  en: 'On site',
});
tool.add('tooltip_cannot_delete_active_drop', {
  en: 'Cannot delete drops <br/> with hives.',
});
tool.add('last_inspected_by', {
  en: 'Last inspected by',
});
tool.add('login_bottom_message_link', {
  en: 'Learn more',
});
tool.add('mobile', {
  en: 'Mobile',
});
tool.add('pollination_drop_name', {
  en: 'Drop name',
});
tool.add('recovered_bulk_beecon_low_battery', {
  en: '{{number}} beecon batteries are ',
});
tool.add('renew_contract_success', {
  en: 'Contract has been renewed',
});
tool.add('select_operation_title', {
  en: 'Choose an operation',
});
tool.add('this_week', {
  en: 'this week',
});
tool.add('yard_deleted_warning', {
  en: 'This yard was deleted on {{date}}. You cannot edit details or view a deleted yard in the whiteboard or the app.',
});
tool.add('empty', {
  en: 'Empty',
});
tool.add('alerts_hive_status_mail', {
  en: 'Enable hive status email notifications',
});
tool.add('pollination_pdf_template_version', {
  en: 'Template Nectar V {{version}}',
  es: 'Plantilla de Nectar V {{version}}',
});
tool.add('no_transmission', {
  en: 'not transmitting',
});
tool.add('renew_contract_details_only', {
  en: 'Contract details ONLY',
});
tool.add('expand', {
  en: 'Expand',
});
tool.add('create_new_password', {
  en: 'Create a new password',
});
tool.add('practice_english', {
  en: 'Practice in English',
});
tool.add('workers_that_visited_in_past_30_days', {
  en: 'Workers that visited in the last 30 days',
});
tool.add('yards', {
  en: 'yards',
});
tool.add('dashboard', {
  en: 'Dashboard',
});
tool.add('hive_reassigned_to', {
  en: '<strong>Hive reassigned</strong> to',
  es: '<strong>Colmena reassigned</strong> al',
});
tool.add('income_year_hint', {
  en: `Planned income of your upcoming <br/>pollination season. To view your accurate <br/>income, you must enter the "Bees in" date <br/>when creating a contract.`,
});
tool.add('error_message_min_length', {
  en: 'The value must be at least {{ min }} characters long',
});
tool.add('worker_deactivated', {
  en: 'Worker successfully deactivated',
});
tool.add('skip_to_previous_page', {
  en: 'Skip to previous page',
});
tool.add('remove_hives', {
  en: 'Remove hives',
});
tool.add('hives_selected_count', {
  en: '{{count}} hives selected',
});
tool.add('winter_mortality_description', {
  en: 'Total number of hives flagged as deadout right after winter.',
});
tool.add('alerts_yard_inspection', {
  en: 'Applied to all hives at {{where}}',
});
tool.add('pollination_drops_hint_part2', {
  en: ' icon to <br/> start filling up your contract map.',
});
tool.add('last_treatment', {
  en: 'Last treatment',
});
tool.add('pollination_contract_form_create_success', {
  en: 'New contract created',
});
tool.add('delete_x_yards_modal_alert', {
  en: '{{count}} of the selected yards are still active. Yards that are active will not be deleted.',
});
tool.add('recovered_deadout', {
  en: 'Hive {{hive}} is recovered from ',
});
tool.add('changes_have_been_saved', {
  en: 'Your changes have been saved',
});
tool.add('archive_contract_success', {
  en: 'Contract archived successfully.',
});
tool.add('health_score_no_score_tooltip', {
  en: 'Not enough data is available to display the health <br/> score. Continue using BeeTrack and log more <br/> information about this hive. <br/> <br/> <strong>BETA</strong>',
});
tool.add('current_password', {
  en: 'Current password',
});
tool.add('activity', {
  en: 'Activity',
});
tool.add('crop_type_input_label', {
  en: 'Type of crop',
});
tool.add('error_type_phone_number', {
  en: 'Use international format: +1 ************',
});
tool.add('hum', {
  en: 'Hum.',
});
tool.add('clear_selection', {
  en: 'Clear selection',
});
tool.add('total_hives', {
  en: 'Total hives',
});
tool.add('friday', {
  en: 'Fri',
});
tool.add('last_visit_weeks', {
  en: 'Last visit over 2 weeks ago.',
});
tool.add('not_transmitting_reason', {
  en: 'Not transmitting',
});
tool.add('hive_structure_queen_excluder__plastic', {
  en: 'Plastic',
});
tool.add('delete_x_yards_modal_alert_single', {
  en: '1 of the selected yards is still active. Yards that are active will not be deleted.',
});
tool.add('enable', {
  en: 'Enable',
});
tool.add('new_hive_report', {
  en: 'New Hive Inspection Report',
});
tool.add('activities', {
  en: 'Activities',
});
tool.add('information', {
  en: 'Information',
});
tool.add('one_dot_one_hive', {
  en: 'One dot, one hive',
});
tool.add('of', {
  en: 'of',
  es: 'de',
});
tool.add('clear_out_archive_contract_desc', {
  en: 'Clearing out drops will flag their hives as lost until they are scanned again. After archiving the contract, these drops will be automatically archived. Note that archiving a drop is an irreversible action.',
});
tool.add('login_no_operation_error', {
  en: 'You must be part of at least one operation.',
});
tool.add('tooltip_create_yard', {
  en: 'Create new yard',
});
tool.add('map_bee_flight_radius', {
  en: 'Bee flight radius',
});
tool.add('snack_timeout_error', {
  en: 'The server took too long to respond, please refresh or retry later',
});
tool.add('toast', {
  en: 'Toast',
});
tool.add('bullet', {
  en: 'Bullet',
});
tool.add('activities_up_to_date', {
  en: 'Activities up to date',
});
tool.add('forgot_password_success_message_1', {
  en: 'If an existing account is linked to the email address <br/> <strong>{{email}}</strong>, instructions to reset your <br/> password will be sent to that email.',
});
tool.add('queenless_abnormals', {
  en: 'queenless / abnormals',
});
tool.add('terrain_view', {
  en: 'Terrain view',
});
tool.add('new_hives', {
  en: 'New hives',
});
tool.add('close', {
  en: 'Close',
  es: 'Cerrar',
});
tool.add('pollination_create_poi', {
  en: 'Create a new point of interest',
});
tool.add('view_occurrences_since_season_start', {
  en: 'Occurrences since season start',
});
tool.add('lost_hives_page_info_part1', {
  en: 'After emptying a yard from the manager’s portal, hives that were at that location are now flagged as “Lost”.',
});
tool.add('action_updated', {
  en: 'Practice successfully updated',
});
tool.add('yard_legend_yellow_gold', {
  en: ' halo represents the yard boudaries.',
});
tool.add('show_disabled_categories', {
  en: 'Show disabled categories and practices',
});
tool.add('phone_int_placeholder', {
  en: 'e.g. +1 ************',
});
tool.add('added', {
  en: 'added',
  es: 'agregada',
});
tool.add('select_operation_search_placeholder', {
  en: 'Search operation',
});
tool.add('pollination_hives_on_map', {
  en: 'Hives on map',
});
tool.add('tooltip_no_phone_msg', {
  en: 'Contact us to update your phone number',
});
tool.add('pdf_fail', {
  en: "Can't generate the PDF file",
});
tool.add('event_bulk_beehub_new_yard', {
  en: '{{number}} yards are added to your operation.',
});
tool.add('field_notes_instructions', {
  en: 'This section will be visible on the Nectar mobile app. Field notes could include phone numbers, directions, or tasks.',
});
tool.add('add_phone', {
  en: 'To add a phone number, please ',
});
tool.add('saturday', {
  en: 'Sat',
});
tool.add('pollination_contract_name', {
  en: 'Contract name',
});
tool.add('tooltip_hive_low_data_rate', {
  en: 'Low data-rate',
});
tool.add('all_yards', {
  en: 'All Yards',
});
tool.add('assign_group', {
  en: 'Assign to group',
});
tool.add('contact', {
  en: 'Contact',
  es: 'Contacto',
});
tool.add('hive_status_desc', {
  en: `Receive an alert when a hive is reported deadout, queenless, requeened or when a new hive is replacing a deadout. 
Reminders for unresolved deadout and queenless hives will also be sent after 4 weeks.`,
});
tool.add('tooltip_hs_above_threshold', {
  en: 'Hive strength is above the set threshold of {{hs_threshold}}',
});
tool.add('annotation_fixed', {
  en: '(Fixed)',
  es: '(Arreglada)',
});
tool.add('flag_1_hive_as_deadout', {
  en: 'Flag 1 hive as deadout?',
});
tool.add('map_street_names', {
  en: 'Street names',
});
tool.add('details', {
  en: 'Details',
});
tool.add('show_last_visit', {
  en: 'Show last visits',
});
tool.add('hive_inspection_by', {
  en: '<strong>Hive inspection</strong> by {{name}}.',
});
tool.add('group', {
  en: 'Group',
});
tool.add('back', {
  en: 'Back',
});
tool.add('export_contract_desc', {
  en: "Select the contract type you'd like to export. Both contract types include the pollination map with its drops, blocks, and points of interest (POIs).",
});
tool.add('hive_structure_frame_base', {
  en: 'Frame base',
});
tool.add('skip_to_last_page', {
  en: 'Skip to last page',
});
tool.add('worker_invite_429_error', {
  en: 'Too Many Requests',
});
tool.add('hover', {
  en: 'Hover',
});
tool.add('pollination_income_details', {
  en: 'Income details',
});
tool.add('yesterday', {
  en: 'Yesterday',
  es: 'Ayer',
});
tool.add('assign_contract_warning_title', {
  en: '{{ amount }} yards are already assigned',
});
tool.add('more_dates', {
  en: 'more dates',
});
tool.add('yard_legend_red', {
  en: 'Critical. At least 30% of hives are queenless or dead.',
});
tool.add('snack_wrong_old_password', {
  en: 'Wrong current password',
});
tool.add('created_on', {
  en: 'Created on',
});
tool.add('removed', {
  en: 'removed',
  es: 'removida',
});
tool.add('clear_out_x_yards_alert', {
  en: '{{count}} of the selected yards are already inactive. They will be ignored.',
});
tool.add('pollination_poi_category_access', {
  en: 'Access',
  es: 'Acceso',
});
tool.add('yard_management_view_next_issue', {
  en: 'View next issue',
});
tool.add('worker_invite_modal_title_1', {
  en: 'Add worker',
});
tool.add('phone', {
  en: 'Phone',
  es: 'Teléfono',
});
tool.add('field_notes', {
  en: 'Field notes',
});
tool.add('hive_structure_frames_per_honey_supers__eight', {
  en: 'Eight frames',
});
tool.add('recovered_beehub', {
  en: 'Beehub {{beehub}} is ',
});
tool.add('welcome_back', {
  en: 'Welcome back',
});
tool.add('pollination_contract_update_success', {
  en: 'Contract updated',
});
tool.add('yard_management_one_errors_found', {
  en: '1 issue',
});
tool.add('clear_out_contract_link', {
  en: 'Learn about clearing out yards',
});
tool.add('pollination_amount_of_blocks', {
  en: 'Amount of blocks',
  es: 'Cantidad de bloques',
});
tool.add('status', {
  en: 'Status',
});
tool.add('hive', {
  en: 'Hive',
  es: 'colmena',
});
tool.add('pollination_hives_on_map_header_hint', {
  en: 'Hives placed in a drop,<br/> on the map.',
});
tool.add('inspection_at', {
  en: '<strong>Inspection</strong> at',
});
tool.add('last_visits', {
  en: 'Last visits',
});
tool.add('remove_hive_desc', {
  en: 'This hive and its history will be removed from your operation. However, it will not be reflected in the overall count of deadouts for the season. Note that this action is irreversible.',
});
tool.add('critical_alerts_desc', {
  en: 'Receive instant notifications that need immediate attention.',
});
tool.add('whiteboard_empty_text', {
  en: 'To view information about your yards, start by creating yards and<br/>tracking hives through the mobile app',
});
tool.add('view_average_last_thirty_days', {
  en: 'Avg last 30 days',
});
tool.add('retired', {
  en: 'Retired',
});
tool.add('selected', {
  en: 'Selected',
});
tool.add('select_all', {
  en: 'Select all',
});
tool.add('select_matches', {
  en: 'Select matches',
});
tool.add('hive_structure_frames_per_honey_supers', {
  en: 'Frames per honey supers',
});
tool.add('timeline_applied_action_tooltip_singular', {
  en: 'Practice was applied to 1 hive.',
  es: 'La práctica se aplicó en 1 colmena.',
});
tool.add('heading2', {
  en: 'Heading 2',
});
tool.add('invite', {
  en: 'Invite',
});
tool.add('delete_x_yards_modal_text', {
  en: 'Do you want to permanently delete these yards from your operation? Note that deleting a yard is an irreversible action.',
});
tool.add('hive_x_hives_selected', {
  en: '{{number}} hives selected.',
});
tool.add('queenless', {
  en: 'Queenless',
});
tool.add('choose_one', {
  en: 'Choose one',
});
tool.add('feeding_and_treatment', {
  en: 'Feeding and treatment',
});
tool.add('yards_list', {
  en: 'Yard list',
});
tool.add('deadout_short', {
  en: 'DO',
});
tool.add('contact_us_at', {
  en: 'Contact us at ',
});
tool.add('last_feed', {
  en: 'Last feeding',
});
tool.add('profile_change_name_first_name', {
  en: 'First name',
});
tool.add('operation', {
  en: 'Operation',
});
tool.add('pollination_new_block_name', {
  en: 'New block',
});
tool.add('queenright', {
  en: 'Queenright',
});
tool.add('practices', {
  en: 'Practices',
});
tool.add('export_msg_instruction', {
  en: "All your hives' data in a {{where}} file that you can use in Excel or Google Spreadsheet. For specific data needs, ",
});
tool.add('previous_yard', {
  en: 'Previous yard',
});
tool.add('security', {
  en: 'Security',
});
tool.add('pollination_block_delete_title', {
  en: 'Delete block?',
});
tool.add('default_columns', {
  en: 'Default columns',
});
tool.add('login_password_placeholder', {
  en: '**********',
});
tool.add('create_password_confirmation_label', {
  en: 'Confirm password',
});
tool.add('reassigned_hives', {
  en: 'Reassigned hives',
});
tool.add('more_date', {
  en: 'more date',
});
tool.add('varroa_description', {
  en: 'Average of the {{sample}} samplings done the last 30 days.',
});
tool.add('toast_icon', {
  en: 'Toasts with icon',
});
tool.add('yard_management', {
  en: 'Yard management',
});
tool.add('support', {
  en: 'Support',
});
tool.add('last_visits_edit_modal_trigger', {
  en: 'Edit range',
});
tool.add('pollination_new_drop_name', {
  en: 'New drop: {{name}}',
});
tool.add('were', {
  en: 'were',
  es: 'fue',
});
tool.add('see_less', {
  en: 'See less',
});
tool.add('total_yards_description', {
  en: 'Total number of yards/drops across your operation.',
});
tool.add('empty_activity_message', {
  en: 'No actions performed',
});
tool.add('yellow', {
  en: 'Yellow symbol. ',
});
tool.add('flagged_as_deadout_bold_single', {
  en: '<strong>flagged</strong> as <strong>deadout</strong>',
  es: '<strong>marcado</strong> como <strong>muerta</strong>',
});
tool.add('primary_md_cta', {
  en: 'Primary medium cta',
});
tool.add('hive_select_all_x_hives', {
  en: 'Select all {{number}} hives.',
});
tool.add('apiary', {
  en: 'Apiary',
});
tool.add('create_password_hint', {
  en: 'Create a password with at least 12 characters.',
});
tool.add('assign_contract', {
  en: 'Assign to a contract',
});
tool.add('pollination_required_hives_placeholder', {
  en: 'e.g. 100',
});
tool.add('worker_phone_number_error', {
  en: 'Invalid phone number',
});
tool.add('pollination_new_block_hint', {
  en: 'Use Block to identify a pollination zone managed in a distinct way. Then assign and manage drop from Manager Portal or from the app.',
});
tool.add('go_back', {
  en: 'Go back',
});
tool.add('list', {
  en: 'List',
});
tool.add('exported_on', {
  en: 'Exported on {{date}}',
  es: 'Exportado el {{date}}',
});
tool.add('temp', {
  en: 'Temp.',
});
tool.add('delete', {
  en: 'Delete',
});
tool.add('hive_structure_frame_base__plastic', {
  en: 'Plastic',
});
tool.add('health_score', {
  en: 'Health score',
});
tool.add('grading', {
  en: 'Grading',
});
tool.add('notes', {
  en: 'Notes',
  es: 'Notas',
});
tool.add('contact_us_phone', {
  en: 'Please, contact us to set up your phone number',
});
tool.add('pollination_create_contract', {
  en: 'Create contract',
});
tool.add('practice_yard_data_tooltip', {
  en: 'Practices or values apply to the yard only.',
});
tool.add('renew_contract_link', {
  en: 'Learn more about contracts',
});
tool.add('pollination_required_hives_error', {
  en: 'Contracts must be created with a defined number of hives.<br/> That number can be changed at anytime.',
});
tool.add('snack_unable_login', {
  en: 'Unable to login with the provided credentials',
});
tool.add('pollination_poi_category_hazard', {
  en: 'Hazard',
  es: 'Riesgo',
});
tool.add('text_btn', {
  en: 'Text button',
});
tool.add('sunday', {
  en: 'Sun',
});
tool.add('whiteboard_empty_title', {
  en: 'No yards yet',
});
tool.add('per_hive_amount', {
  en: '{{amount}} per hive',
});
tool.add('unknown_yard', {
  en: 'unknown yard',
  es: 'Apiario desconocido',
});
tool.add('nuc', {
  en: 'Total nucs',
});
tool.add('yard_selected', {
  en: 'Yard selected',
});
tool.add('team_desc', {
  en: 'Accounts linked to this operation. To add more team members, please ',
});
tool.add('renew', {
  en: 'Renew',
});
tool.add('pollination_batch_success', {
  en: 'Map items have been updated',
});
tool.add('beetrack', {
  en: 'Beetrack',
});
tool.add('medium', {
  en: 'Medium',
});
tool.add('pollination_hives_on_map_underflow_hint', {
  en: 'Contract map is missing<br/> hives.',
});
tool.add('last_inspection', {
  en: 'Last inspection',
});
tool.add('no_notifications', {
  en: 'No notifications yet',
});
tool.add('region', {
  en: 'Region',
});
tool.add('yard_card', {
  en: 'Yard card',
});
tool.add('pollination_create_blocks', {
  en: 'Create blocks',
});
tool.add('keyboard_navigation_instructions', {
  en: 'Use <strong>Arrows</strong> to navigate, <strong>Enter</strong> to select, <strong>Escape</strong> to cancel',
});
tool.add('default', {
  en: 'Default',
});
tool.add('disabled', {
  en: 'Disabled',
});
tool.add('strong', {
  en: 'Strong',
});
tool.add('format', {
  en: 'YYYY-MM-DD',
});
tool.add('create_new_practice_instructions', {
  en: 'Choose a category and add any action or practice you would like to be recorder and tracked.',
});
tool.add('loading', {
  en: 'Loading',
});
tool.add('total_hives_description', {
  en: "Total number of hives alive across your operation. Keep that number accurate, flag deadouts as soon as possible. This number doesn't include lost hives.",
});
tool.add('pollination_required_hives', {
  en: 'Required hives',
  es: 'Colmenas requeridas',
});
tool.add('days', {
  en: 'days',
});
tool.add('click_to_apply_filter', {
  en: 'Click to apply this filter',
});
tool.add('no_hives_msg', {
  en: 'Currently, no hive is tracked at {{yard}}.',
});
tool.add('paragraph_small', {
  en: 'Paragraph - small',
});
tool.add('secondary_big_light_cta', {
  en: 'Secondary big light cta',
});
tool.add('pollination_poi_name_label', {
  en: 'Name of point of interest',
});
tool.add('tablet', {
  en: 'Tablet',
});
tool.add('row_numbers', {
  en: 'Rows, Numbers - small',
});
tool.add('timeline_yard_nb_hives_during_visit_tooltip_singular', {
  en: 'On {{date}} this yard had 1 hive at<br/> the end of the day.',
  es: 'El {{date}} este apiario tenía 1 colmena<br/>al final del día.',
});
tool.add('at', {
  en: 'at',
  es: 'en',
});
tool.add('assign_contract_warning_amount_single', {
  en: 'is already assigned  to a contract. It will be detached from it and renamed.',
});
tool.add('pollination_blocks', {
  en: 'Blocks',
  es: 'Bloques',
});
tool.add('exit_fullscreen', {
  en: 'Exit fullscreen',
});
tool.add('fullscreen', {
  en: 'Fullscreen',
});
tool.add('event_beecon_low_battery', {
  en: "Hive {{hive}}'s beecon ",
});
tool.add('worker_invite_modal_title_2', {
  en: 'Name worker',
});
tool.add('pollination_total_price_placeholder', {
  en: 'e.g. $5000',
});
tool.add('summary', {
  en: 'Summary',
});
tool.add('alerts_hive_status_sms', {
  en: 'Enable hive status text notifications',
});
tool.add('map_change_options', {
  en: 'Change options',
});
tool.add('tooltip_yard_population_same', {
  en: 'Population steady',
});
tool.add('unauthorized', {
  en: 'Unauthorized',
});
tool.add('yard_inspection', {
  en: 'Yard inspection',
  es: 'Inspección del apiario',
});
tool.add('assign_contract_warning_title_single', {
  en: '1 yard is already assigned',
});
tool.add('hive_moved_to', {
  en: '<strong>Hive moved</strong> to',
  es: '<strong>Colmena movida</strong> al',
});
tool.add('yard_management_view_prev_issue', {
  en: 'View previous issue',
});
tool.add('hive_structure_bottom_boards__screen', {
  en: 'Screen',
});
tool.add('contract_name_input_placeholder', {
  en: 'Select existing contract',
});
tool.add('your_manager', {
  en: 'Your manager',
});
tool.add('hive_structure_honey_super_type__medium', {
  en: 'Medium',
});
tool.add('hardware_issue', {
  en: 'Hardware issue',
});
tool.add('weather_tooltip', {
  en: 'Weather tooltip',
});
tool.add('renew_contract_desc', {
  en: 'Select the elements from this contract that you want the new contract to inherit.',
});
tool.add('creation_date', {
  en: 'Creation date',
});
tool.add('filters', {
  en: 'Filters',
});
tool.add('no_yards_map_msg', {
  en: 'Install your beehubs to locate them here on a map.',
});
tool.add('tooltip_queenless', {
  en: 'Detected queenless 3 days ago',
});
tool.add('yard_cleared_out', {
  en: 'Yard has been cleared out',
});
tool.add('create_password_confirmation_error', {
  en: 'Password does not match',
});
tool.add('event_bulk_beehub', {
  en: '{{number}} beehubs are ',
});
tool.add('pollination_drops_hint_part1', {
  en: 'No drops yet. Click on the ',
});
tool.add('worker_invite_confirm', {
  en: 'Confirm',
});
tool.add('clear_out_yard_accept', {
  en: 'Clear out',
});
tool.add('alert_label_hive_status', {
  en: 'When a hive status has been changed',
});
tool.add('cluster', {
  en: 'Cluster',
});
tool.add('pollination_blocks_management_hint_part1', {
  en: 'No blocks yet. Click on the ',
});
tool.add('yard_visit_click_to_open_tooltip', {
  en: 'Click to see the visit details.',
});
tool.add('reporting_users', {
  en: 'Reporting users',
});
tool.add('crop_type', {
  en: 'Crop type',
  es: 'Tipo de cultivo',
});
tool.add('beek_practices', {
  en: 'Beekeeping practices',
});
tool.add('recent', {
  en: 'Recent',
});
tool.add('no_workers_msg', {
  en: 'Add workers to your operation so they can start collecting data.',
});
tool.add('login_button', {
  en: 'Sign in',
});
tool.add('show_less', {
  en: 'Show less',
});
tool.add('alerts_new_yard', {
  en: '{{yard}} has been created around {{where}} with {{number}} hives',
});
tool.add('pollination_blocks_hint_part2', {
  en: ' icon to <br/> start filling up your contract map.',
});
tool.add('sms', {
  en: 'SMS',
});
tool.add('contact_title', {
  en: 'How can we help you?',
});
tool.add('view_last_occurrence_short', {
  en: 'Last occurrence',
});
tool.add('timeline_update_alert_part3', {
  en: ' your page.',
});
tool.add('reports_elap_description_list_title', {
  en: 'This report will include the following data:',
});
tool.add('yard_visit', {
  en: 'Yard visit',
  es: 'Apiario visita',
});
tool.add('practice_type_checkbox', {
  en: 'Multi choice',
});
tool.add('dates', {
  en: 'Dates',
});
tool.add('pollination_new_poi_name', {
  en: 'New point of interest',
});
tool.add('password', {
  en: 'Password',
});
tool.add('view_last_occurrence_date', {
  en: 'Last occurrence date',
});
tool.add('yard_status', {
  en: 'Yard status',
});
tool.add('alerts_new_yard_mail', {
  en: 'Enable new yard email notifications',
});
tool.add('fahrenheit', {
  en: 'Fahrenheit',
});
tool.add('income_year', {
  en: 'Income {{year}}',
});
tool.add('snack_default_msg', {
  en: 'Something went wrong, please refresh or retry later{{status}}',
});
tool.add('member_sms_resent', {
  en: 'SMS successfully re-sent',
});
tool.add('login_bottom_message', {
  en: 'Interested in using Nectar?',
});
tool.add('not_charging', {
  en: 'not charging correctly',
});
tool.add('flag_hives_deadout_success', {
  en: '{{count}} hives were flagged as deadouts',
});
tool.add('hive_data_last_update', {
  en: 'Hive data updated {{ time }} ago',
});
tool.add('password_changed', {
  en: 'New password has been saved',
});
tool.add('pollination_poi_es_desc_label', {
  en: 'Description in Spanish',
});
tool.add('pollination_block_name', {
  en: 'Block {{name}}',
});
tool.add('low_frame_bees', {
  en: 'Low frame of bees',
});
tool.add('keep_adding', {
  en: 'Keep adding',
});
tool.add('on_before_lose_changes_prompt', {
  en: 'Are you sure you want to cancel your latest changes? if yes, all changes will be lost.',
});
tool.add('red', {
  en: 'Red symbol. ',
});
tool.add('remove_group_instructions_having_yard', {
  en: 'Removing group <strong>{{name}}</strong> will un-assign one yard using it.',
});
tool.add('timeline_hives_possibly_removed_tooltip', {
  en: 'Some hives might have been moved out of<br/> this yard during that day.',
  es: 'Es posible que algunas colmenas hayan sido<br/>trasladadas fuera de este apiario durante ese día.',
});
tool.add('year', {
  en: 'Year',
});
tool.add('csv_export_desc', {
  en: 'All your hives data in a CSV file compatible with Excel and Google Spreadsheet. For specific data needs, ',
});
tool.add('archived', {
  en: 'Archived',
});
tool.add('go_to_home_page', {
  en: 'Go to home page',
});
tool.add('remove_hives_success_all', {
  en: 'All hives were removed from your operation',
});
tool.add('give_feedback', {
  en: 'Give feedback',
});
tool.add('renew_contract', {
  en: 'Renew contract',
});
tool.add('toast_action', {
  en: 'Toasts with action',
});
tool.add('lang_spanish', {
  en: 'Spanish',
});
tool.add('alerts', {
  en: 'Alerts',
});
tool.add('your_msg', {
  en: 'Your message',
});
tool.add('unable_create_yard', {
  en: 'Please select at least 3 points to create a yard.',
});
tool.add('pollination_pois', {
  en: 'Points of interest',
  es: 'Puntos de interés',
});
tool.add('assign_contract_confirmation_title', {
  en: 'Are you sure?',
});
tool.add('event_bulk_detected', {
  en: '{{number}} hives detected ',
});
tool.add('create_password_invalid_token_error_3', {
  en: 'again or contact support.',
});
tool.add('create_password_message', {
  en: 'Enter your new password below.',
});
tool.add('disable', {
  en: 'Disable',
});
tool.add('assign_contract_confirmation_desc', {
  en: 'Assigning yards to a contract will automatically rename them.',
});
tool.add('category_tooltip', {
  en: '{{category_name}} category cannot be edited',
});
tool.add('hive_structure_queen_excluder__none', {
  en: 'None',
});
tool.add('latest_used', {
  en: 'Latest used',
});
tool.add('refresh_screen', {
  en: 'Refresh screen',
});
tool.add('auto_refresh_disabled', {
  en: 'Auto-refresh disabled',
});
tool.add('auto_refresh_screen_every_hour', {
  en: 'Auto-refresh every 1 hour',
});
tool.add('auto_refresh_screen_every_x_hours', {
  en: 'Auto-refresh every {{interval}} hours',
});
tool.add('every_hour', {
  en: '1 hour',
});
tool.add('every_x_hours', {
  en: '{{interval}} hours',
});
tool.add('refresh_to_update', {
  en: 'Refresh to update',
});
tool.add('reports_elap_description', {
  en: `Whether it's for your personal insurance or programs like <a href={{linkToElap}} target="_blank" rel="noreferrer">ELAP</a>, this comprehensive <br/> report provides you with all the essential information required for your documentation.`,
});
tool.add('tooltip_deadout', {
  en: 'Detected deadout 3 days ago',
});
tool.add('white', {
  en: 'white',
});
tool.add('no_beehub_data', {
  en: 'Waiting for beehub location data',
});
tool.add('queenless_short', {
  en: 'QL',
});
tool.add('clear_out_yard_text', {
  en: 'Clearing out this yard will flag its hives as lost until they are scanned again. The yard will appear inactive.',
});
tool.add('add', {
  en: 'Add',
});
tool.add('timeline_yard_nb_hives_during_visit_tooltip', {
  en: 'On {{date}} this yard had {{count}} hives at<br/> the end of the day.',
  es: 'El {{date}} este apiario tenía {{count}} colmenas<br/>al final del día.',
});
tool.add('weather', {
  en: 'weather',
});
tool.add('yard_error_msg', {
  en: "An issue has been detected with the yard's beehub, this data may not be up-to-date.",
});
tool.add('update_group', {
  en: 'Edit group',
});
tool.add('hive_clear_selection', {
  en: 'Clear selection.',
});
tool.add('worker_invite_modal_message_2', {
  en: 'Enter the worker’s full name, ensuring it is unique. This name will be displayed when that person completes activities.',
});
tool.add('no_groups_msg', {
  en: 'Use groups to identify a region, a team or yards managed in a distinct way. Then assign it from the Yard List.',
});
tool.add('disable_practice', {
  en: 'Disable {{category}}?',
});
tool.add('weather_coming_soon', {
  en: 'Weather Coming Soon',
});
tool.add('pollination_pois_hint_part2', {
  en: ' icon to start filling up your contract map.',
});
tool.add('inverse', {
  en: 'Inverse',
});
tool.add('toasts_with_action', {
  en: 'Toasts with Action',
});
tool.add('yards_capitalized', {
  en: 'Yards',
});
tool.add('archive_contract_name', {
  en: 'Archive {{contract}}?',
});
tool.add('yards_all_select', {
  en: 'Select all {{number}} yards.',
});
tool.add('cancel_changes_instructions', {
  en: 'Are you sure you want to cancel your latest changes? If yes, all changes will be lost and  yards will revert to their previous shape.',
});
tool.add('no_visits_for_90_days', {
  en: 'No visit in the last 90 days',
});
tool.add('focus', {
  en: 'Focus',
});
tool.add('no_hives_transmitting_msg', {
  en: 'No hives transmitting',
});
tool.add('tag_management', {
  en: 'Tag management',
});
tool.add('hive_pop_trend', {
  en: 'Hive population trend',
});
tool.add('invalid_email', {
  en: 'Invalid email',
});
tool.add('tooltip_merge_yards', {
  en: 'Merge yards',
});
tool.add('pollination_hives_on_map_header', {
  en: 'On map',
});
tool.add('worker_added', {
  en: '{{name}} was added to your operation',
});
tool.add('last_inspection_all', {
  en: 'All inspections',
});
tool.add('avg_visit', {
  en: 'Avg visit',
});
tool.add('remove_yard_instructions_cont', {
  en: ' will remove the yard from your operation in the platform and the app with its past history.',
});
tool.add('feedback', {
  en: 'Feedback',
});
tool.add('weak', {
  en: 'Weak',
});
tool.add('assign_contract_disabled_no_contracts', {
  en: "You don't have any contracts",
});
tool.add('unsaved_changes_exit_modal_message', {
  en: 'Are you sure you want to cancel your unsaved changes?',
});
tool.add('change', {
  en: 'Change',
});
tool.add('hive_structure_brood_boxes__other', {
  en: 'Other',
});
tool.add('snack_password_changed_msg', {
  en: 'Password successfully updated',
});
tool.add('error', {
  en: 'Error',
});
tool.add('inactive_since', {
  en: 'Inactive since',
});
tool.add('hive_grading', {
  en: 'Hive grading',
});
tool.add('deadout_hives', {
  en: 'Deadout hives',
});
tool.add('pollination_drops_empty_create', {
  en: 'Create drops',
});
tool.add('pollination_grower_info', {
  en: 'Grower info',
});
tool.add('changes_saved', {
  en: 'Changes saved',
});
tool.add('hs_above_threshold', {
  en: 'Under {{hs_threshold}}',
});
tool.add('try_again', {
  en: 'Try again',
});
tool.add('last_year', {
  en: 'last year',
});
tool.add('reminder_deadouts', {
  en: 'Reminder: Deadouts',
});
tool.add('pollination_poi_category_storage', {
  en: 'Storage',
  es: 'Almacenamiento ',
});
tool.add('hive_structure_frame_base__wax', {
  en: 'Wax',
});
tool.add('practice_hive_data_tooltip', {
  en: 'Practices or values apply to hives.',
});
tool.add('Since_date', {
  en: 'Since {{ date }}',
});
tool.add('critical_alerts', {
  en: 'Critical alerts',
});
tool.add('Hives', {
  en: 'Hives',
});
tool.add('mortality', {
  en: 'Mortality {{year}}',
});
tool.add('introduced_short', {
  en: 'INT',
});
tool.add('yard_successfully_assigned_to_group_single', {
  en: 'Yard successfully assigned to group',
});
tool.add('beecons_installed', {
  en: 'Beecons installed in {{where}}.',
});
tool.add('remove_action_instructions', {
  en: 'Disabling ',
});
tool.add('invalid_metric', {
  en: 'Metric trend chart not supported',
});
tool.add('group_name', {
  en: 'Group name',
});
tool.add('last_inspections', {
  en: 'Last inspections',
});
tool.add('new_yard_placeholder_name', {
  en: 'New yard',
});
tool.add('color', {
  en: 'Color',
});
tool.add('inspections_reports_empty', {
  en: 'No inspection reported from the field yet',
});
tool.add('now', {
  en: 'now',
});
tool.add('now_transmitting', {
  en: 'now transmitting',
});
tool.add('inactive_tooltip', {
  en: 'Inactive.',
});
tool.add('primary_cta', {
  en: 'Primary CTA',
});
tool.add('snack_password_reset_email_sent', {
  en: 'Your request has been sent to {{email}}',
});
tool.add('pollination_pdf_export', {
  en: 'PDF Export',
});
tool.add('help_menu_title', {
  en: 'Help and resources',
});
tool.add('search_close', {
  en: 'Close search',
});
tool.add('day_ago', {
  en: '{{number}} day ago',
});
tool.add('action_added', {
  en: 'Action successfully added',
});
tool.add('last_month', {
  en: 'last month',
});
tool.add('not_transmitting_beehub_msg', {
  en: 'BeeHub is not transmitting',
});
tool.add('trend_schema_caption', {
  en: 'Based on hives temperature data',
});
tool.add('avg_visits', {
  en: 'Average amount of days between<br/> yard and/or hive reports.',
});
tool.add('assign_contract_success', {
  en: 'Yards successfully assigned to {{name}}',
});
tool.add('last_updated', {
  en: 'Last updated',
});
tool.add('typeface', {
  en: 'Typeface',
});
tool.add('forgot_password_success_message_2', {
  en: 'You should receive this email within the next five <br/> minutes. Please also check your spam folder.',
});
tool.add('per_page', {
  en: 'Per page',
});
tool.add('inactive', {
  en: 'Inactive',
});
tool.add('monitored_yards_desc', {
  en: 'Number of beecons installed and spread across {{number}}.',
});
tool.add('operation_average_unavailable', {
  en: "Operation average is unavailable because<br/> your yards were visited more than 90 days<br/> ago or weren't visited.",
});
tool.add('edit_drop', {
  en: 'Edit drop',
});
tool.add('location', {
  en: 'Location',
  es: 'Ubicación',
});
tool.add('feeding', {
  en: 'Feeding',
});
tool.add('create_new_practice', {
  en: 'Create new {{category}}',
});
tool.add('total_lost_hives_description', {
  en: 'Total number of hives marked as lost across your operation.',
});
tool.add('save_view', {
  en: 'Save view',
});
tool.add('pollination_details', {
  en: 'Pollination details',
});
tool.add('avg_visits_column_header', {
  en: 'Avg visit',
});
tool.add('low_data_rate', {
  en: 'not transmitting well',
});
tool.add('check_phone_input', {
  en: 'Check phone input',
});
tool.add('alerts_retired', {
  en: '{{number}} hives retired at {{where}}',
});
tool.add('change_password_instructions', {
  en: 'At least 12 characters long.',
});
tool.add('timeline_yard_nb_hives_today_tooltip_singular', {
  en: 'This yard currently have 1 hive.',
  es: 'Este apiario tiene actualmente 1 colmena.',
});
tool.add('yard_boundaries_updated', {
  en: 'The boundaries have been updated.',
  es: 'Los límites han sido actualizados.',
});
tool.add('pollination_hives_on_map_row_hint', {
  en: 'Contract map is missing<br/> hives.',
});
tool.add('since', {
  en: 'since',
});
tool.add('colours', {
  en: 'Colours',
});
tool.add('alert_label_new_yard', {
  en: 'When a new yard is created',
});
tool.add('recovered_queenless', {
  en: 'Hive {{hive}} is back to ',
});
tool.add('contract_and_name', {
  en: 'Contract: {{name}}',
});
tool.add('hide_yard_map_content_card', {
  en: 'Hide {{ content }}',
});
tool.add('add_group', {
  en: 'Add group',
});
tool.add('tooltip_active_yards', {
  en: 'Yards with hives.',
});
tool.add('tooltip_inactive_yards', {
  en: 'Yards without hives.',
});
tool.add('tooltip_unloaded_yards', {
  en: 'Yards marked as unloaded.',
});
tool.add('tooltip_emptied_yards', {
  en: 'Yards marked as emptied.',
});
tool.add('my_account', {
  en: 'My Account',
});
tool.add('tooltip_hive_low_battery', {
  en: 'Battery 25% or less',
});
tool.add('added_to', {
  en: '<strong>added</strong> to',
});
tool.add('hive_structure_queen_excluder', {
  en: 'Queen excluder',
});
tool.add('error_message_positive', {
  en: 'The value should be positive',
});
tool.add('open', {
  en: 'Open',
});
tool.add('login_forgot_password', {
  en: 'Forgot password?',
});
tool.add('view_last_visit_affected_hives_short', {
  en: 'Hives inspected in last visit',
});
tool.add('pollination_cost_per_hive_placeholder', {
  en: 'e.g. $50',
});
tool.add('warning', {
  en: 'Warning',
});
tool.add('next', {
  en: 'Next',
});
tool.add('clear_out_contract_warn_archive', {
  en: 'Some drops are still active. Before archiving a contract, drops must be cleared out.',
});
tool.add('profile_change_name_first_name_placeholder', {
  en: 'Enter first name',
});
tool.add('get_in_touch', {
  en: 'get in touch',
});
tool.add('temperature_unit', {
  en: 'Temperature unit',
});
tool.add('pollination_dropdowns', {
  en: 'Dropdowns',
});
tool.add('yards_map', {
  en: 'Yard map',
});
tool.add('emptied', {
  en: 'Emptied',
});
tool.add('emptied_yards', {
  en: 'Emptied yards',
});
tool.add('skip_to_next_page', {
  en: 'Skip to next page',
});
tool.add('celsius', {
  en: 'Celsius',
});
tool.add('timeline_update_alert_part2', {
  en: 'refresh',
});
tool.add('varroa', {
  en: 'Varroa level',
});
tool.add('remove_yard', {
  en: 'Delete yard?',
});
tool.add('remove_group', {
  en: 'Remove group',
});
tool.add('yard_not_all_hives_included_error_msg', {
  en: 'Please include all hives inside the yard boundaries.',
});
tool.add('renew_contract_details_map_desc', {
  en: 'Use this option if you want the new contract to inherit the contract details, as well as the locations of all map components (blocks, drops, and POIs). The previous contract, along with its drops, blocks and POIs will be archived. Please note that an archived contract cannot be unarchived.',
});
tool.add('last_week', {
  en: 'last week',
});
tool.add('edit_contract_yard_warning', {
  en: 'Assigning a yard to a contract turns it into a drop. Once saved, the drop must always be assigned to a contract.',
});
tool.add('tooltip_low_data_rate', {
  en: 'Beecons showing low data rate',
});
tool.add('movement_detected', {
  en: 'Movement detected',
});
tool.add('forgot_password_message', {
  en: 'Fill in your email, we will send you instructions <br/> on how to reset your password.',
});
tool.add('timeline_mismatch_timezone_alert', {
  en: 'You are seeing activities in the timezone <b>{{userTimezone}}</b>, but your browser is set to <b>{{browserTimezone}}</b>. You can change it in',
});
tool.add('pollination_add_contract', {
  en: 'Add contract',
});
tool.add('manage_yards', {
  en: 'Manage yards',
});
tool.add('hive_grading_weak', {
  en: 'Weak',
});
tool.add('last_visit_range', {
  en: 'Between {{from}} and {{to}} days',
});
tool.add('apply', {
  en: 'Apply',
});
tool.add('over_the_season', {
  en: '<strong>{{action}}</strong> over the season <br/>{{date}} - now',
});
tool.add('clear_out_x_drops', {
  en: 'Clear out {{count}} drops?',
});
tool.add('change_password_missing_fields', {
  en: 'One or more password fields is missing',
});
tool.add('hive_all_x_hives_are_selected', {
  en: 'All {{number}} hives are selected.',
});
tool.add('error_type_mismatch', {
  en: 'Please enter a valid {{ label }}',
});
tool.add('enabled', {
  en: 'Enabled',
});
tool.add('low_batteries', {
  en: 'batteries are low',
});
tool.add('pollination_hives_on_site_underflow_hint', {
  en: 'All hives are yet to be<br/> scanned at the orchard.',
});
tool.add('pollination_drops_empty', {
  en: 'No drops yet',
});
tool.add('pollination_contract_archived_warning', {
  en: 'This contract was archived on {{date}}. You will not be able to add or edit any items on the map.',
});
tool.add('edit_color', {
  en: 'Edit color',
});
tool.add('groups', {
  en: 'Groups',
});
tool.add('type_yard', {
  en: 'Type of yard',
});
tool.add('view_number_of_occurrences_since_season_start', {
  en: '# occurrences since season’s start',
});
tool.add('connectivity', {
  en: 'connectivity',
});
tool.add('email_recap_desc', {
  en: 'Receive summaries by email at {{email}} and stay up to date on all activity.',
});
tool.add('info', {
  en: 'info',
});
tool.add('practice_exists', {
  en: 'Sorry, this English practice already exists.',
});
tool.add('health_score_hive_modal_header_tooltip', {
  en: 'The higher the health score the higher the probability of your hives surviving until the next season begins.<br/><br/>The health score is influenced by the number of visits, movements and location.<br/><br/> <strong>BETA</strong>',
});
tool.add('nav_bars', {
  en: 'Nav Bars',
});
tool.add('tooltip_text_message', {
  en: '{{manager}} invites you to join Nectar’s hive tracking mobile app. Click the link below, download the app, and create your account using this phone number.',
});
tool.add('flag_count_hives_as_deadout', {
  en: 'Flag {{count}} hives as deadout?',
});
tool.add('login_title_p1', {
  en: 'Welcome back<br/> to',
});
tool.add('move_to_the_right', {
  en: 'Move to the right',
});
tool.add('remove_worker', {
  en: 'Remove worker',
});
tool.add('remove_manager', {
  en: 'Remove manager',
});
tool.add('yard_management_orphan_hive', {
  en: 'Unassigned hives found!',
});
tool.add('unassigned', {
  en: 'Unassigned',
});
tool.add('manager', {
  en: 'Manager',
});
tool.add('total', {
  en: 'Total',
});
tool.add('edit_map', {
  en: 'Edit map',
});
tool.add('edit_yard_info', {
  en: 'Edit yard info',
});
tool.add('edit_yard_boundaries', {
  en: 'Edit yard boundaries',
});
tool.add('event_bulk_beecon_low_battery', {
  en: "{{number}} beecons' ",
});
tool.add('forgot_password_title', {
  en: 'Forgot password',
});
tool.add('remove_column', {
  en: 'Remove column',
});
tool.add('export_contract', {
  en: 'Export contract',
});
tool.add('on', {
  en: 'On',
});
tool.add('pollination_drop_delete_title', {
  en: 'Delete drop?',
});
tool.add('worker_invite_modal_message_1', {
  en: 'New workers will receive a text message including a link to download the mobile app.',
});
tool.add('input', {
  en: 'Input',
});
tool.add('assign_contract_warning_amount', {
  en: 'are already assigned  to a contract. They will be detached from it and renamed.',
});
tool.add('paragraph_normal', {
  en: 'Paragraph - normal',
});
tool.add('pollination_hives_required', {
  en: 'Hives required',
});
tool.add('open_google_maps', {
  en: 'Open in google maps',
});
tool.add('pollination_hives_on_site_header_hint', {
  en: 'Hives tracked in a drop,<br/> at the orchard.',
});
tool.add('tooltip_unknown', {
  en: 'Unknown : too little data received in the last 24h to be able to confirm',
});
tool.add('lost_hives', {
  en: 'Lost hives',
  es: 'Colmenas perdidas',
});
tool.add('open_hover', {
  en: 'Open + Hover',
});
tool.add('hive_structure_frames_per_honey_supers__nine', {
  en: 'Nine frames',
});
tool.add('practice_spanish', {
  en: 'Practice in Spanish',
});
tool.add('map_details', {
  en: 'Map details',
});
tool.add('unloaded', {
  en: 'Unloaded',
});
tool.add('unloaded_yards', {
  en: 'Unloaded yards',
});
tool.add('email_clipboard', {
  en: 'Email copied to clipboard',
});
tool.add('event_bulk_beecon', {
  en: '{{number}} beecons are ',
});
tool.add('avg_visits_nodata', {
  en: '<strong>Average visits</strong> is only available for yards with<br/> at least 2 visits within the last 90 days.',
});
tool.add('reports_elap_description_list_item_3', {
  en: 'Bi-weekly hive inventory',
});
tool.add('lang_english', {
  en: 'English',
});
tool.add('sensors', {
  en: 'Sensors',
});
tool.add('yard_included', {
  en: 'Yard included',
  es: 'Apiario incluido',
});
tool.add('between', {
  en: 'Between',
});
tool.add('yard_type_tooltip', {
  en: 'Yard type can be assigned in the<br/>edit yard modal.',
});
tool.add('practice_type_radio', {
  en: 'Single choice',
});
tool.add('unable_remove_yard_vertex', {
  en: 'Yard should have at least 3 points.',
});
tool.add('alerts_reminder_deadouts', {
  en: 'At {{where}}, {{number}} hives have been flagged as deadout for {{when}}',
});
tool.add('delete_x_yards_modal_text_single', {
  en: 'Do you want to permanently delete this yard from your operation? Note that deleting a yard is an irreversible action.',
});
tool.add('total_lost_hives', {
  en: 'Lost hives',
});
tool.add('hive_structure_bottom_boards__solid', {
  en: 'Solid',
});
tool.add('alerts_queenless', {
  en: '{{number}} queenless hives flagged at {{where}}',
});
tool.add('pollination_block_name_label', {
  en: 'Block name and/or number',
});
tool.add('hide_street_names', {
  en: 'Hide street names',
});
tool.add('error_message_required', {
  en: 'This field is required',
});
tool.add('hive_selected_1', {
  en: '1 hive selected',
});
tool.add('pollination_notes', {
  en: 'Notes',
});
tool.add('tooltips_hover_tap', {
  en: 'Active Links (hover + tap)',
});
tool.add('archive_contract_desc', {
  en: "Are you certain you want to archive this contract? All drops, blocks, and POI currently assigned to this contract will be archived from your operation. You'll still be able to view their placements but editing, moving, or adding any items will not be possible. <br/><b>Note that you cannot renew or unarchive an archived contract.</b>",
});
tool.add('off', {
  en: 'Off',
});
tool.add('team', {
  en: 'Team',
});
tool.add('view', {
  en: 'View',
});
tool.add('assign_x_yards_contract', {
  en: 'Assign {{number}} yards to a contract',
});
tool.add('practice_disable_modal_message', {
  en: "Are you sure you want to disable the '{{name}}' category? Once disabled, it won't appear in the mobile app and can't be used in inspections. Note that you can always re-enable it later.",
});
tool.add('new_yards', {
  en: 'New yards',
});
tool.add('worker', {
  en: 'Worker',
});
tool.add('yards_all_selected_header', {
  en: 'All {{number}} yards are selected.',
});
tool.add('archived_since', {
  en: 'Archived since',
});
tool.add('contact_text', {
  en: 'In a hurry? Check-out our ',
});
tool.add('error_message_money', {
  en: 'Please enter a valid monetary value',
});
tool.add('export', {
  en: 'Export',
});
tool.add('renew_contract_details_only_desc', {
  en: 'Use this option only if you want the contract details to remain the same, but you do not want the pollination map items (blocks, drops, and POIs) to be created at the same location. The previous contract, along with its blocks, drops, and POIs, will be archived. Please note that an archived contract cannot be unarchived.',
});
tool.add('check_email_input', {
  en: 'Check email input',
});
tool.add('activated', {
  en: 'Activated',
});
tool.add('nuc_description', {
  en: "All active hives flagged with practice 'Nuc'. Removing the hive or applying the practice 'Nuc Transferred' will remove the nuc from the count.",
});
tool.add('shadow_levels', {
  en: 'Shadow levels',
});
tool.add('health_score_header_tooltip', {
  en: 'The average health score of your hives in that yard.  The higher the health score the higher the probability of your hives surviving until the next season begins.<br/><br/> The health score is influenced by the number of visits, movements and location.<br/><br/> <strong>BETA</strong>',
});
tool.add('login_slide_2_text', {
  en: "Stay organized by creating your pollination contracts within the manager's portal. Easily draw your map with blocks, drops, and points of interest. Print the map to share with your team or use the app's GPS for convenient navigation to these locations.",
});
tool.add('user_profile', {
  en: 'User Profile',
});
tool.add('hive_structure_honey_super_type__shallow', {
  en: 'Shallow',
});
tool.add('profile_change_name_modal_message', {
  en: 'When updating your name, your new name will be displayed across all the operations you are assigned to.',
});
tool.add('new_yard_message', {
  en: 'Yard created less than 2 weeks ago.',
});
tool.add('embedded_timeline_error', {
  en: "Can't load the timeline.",
});
tool.add('enter_fullscreen', {
  en: 'Enter fullscreen',
});
tool.add('pollination_grower_name_placeholder', {
  en: 'e.g. Westwood Acres',
});
tool.add('heading3', {
  en: 'Heading 3',
});
tool.add('pollination_contract_detail_instruction', {
  en: 'Enter information when editing your contract.',
});
tool.add('hive_structure_brood_boxes', {
  en: 'Brood boxes',
});
tool.add('toasts_with_icon', {
  en: 'Toasts with Icon',
});
tool.add('sound_frequency', {
  en: 'Sound frequency',
});
tool.add('grading_description', {
  en: 'Summary of the {{sample}} grading report performed the last 30 days.',
});
tool.add('tooltip_worrying_status', {
  en: 'At least 15% of hives are queenless or dead',
});
tool.add('group_added', {
  en: 'New group created successfully',
});
tool.add('now_charging', {
  en: 'now charging',
});
tool.add('contracts', {
  en: 'contracts',
  es: 'contratos',
});
tool.add('manage_columns_link', {
  en: 'Learn how to customize your whiteboard',
});
tool.add('profile', {
  en: 'Profile',
});
tool.add('no_yards_overview_msg', {
  en: "Seems like we aren't collecting data yet. Let's connect to get you started! ",
});
tool.add('no_transmission_since_days', {
  en: 'not transmitting since 2 days',
});
tool.add('whiteboard_view_saved_message', {
  en: 'Whiteboard view saved',
});
tool.add('not_available_with_slash', {
  en: 'N/A',
});
tool.add('pollination_blocks_hint_part1', {
  en: 'No blocks yet. Click on the ',
});
tool.add('treatment', {
  en: 'Treatment',
});
tool.add('archive_contract', {
  en: 'Archive contract?',
});
tool.add('pollination_drops_management_hint_part2', {
  en: ' icon<br/> on the top bar to start creating your<br/> drops.',
});
tool.add('satellite_view', {
  en: 'Satellite view',
});
tool.add('beek_practices_desc', {
  en: 'Define what is important to track. Add your own beekeeping practices to make it available on the mobile app hive and yard reports.',
});
tool.add('submit_button', {
  en: 'Submit',
});
tool.add('create_new_group_instructions', {
  en: 'Use groups to identify a region, a team or yards managed in a distinct way. Then assign it from the Yard List.',
});
tool.add('active_since', {
  en: 'Active since',
});
tool.add('reports_elap_description_list_item_2', {
  en: 'Summary of deadouts by yard',
});
tool.add('grower', {
  en: 'Grower',
  es: 'Agricultor',
});
tool.add('link_clipboard', {
  en: 'Link copied to clipboard',
});
tool.add('monitored_hives', {
  en: 'monitored hives',
});
tool.add('yard_deleted_only', {
  en: 'Yard has been deleted from your operation',
});
tool.add('whiteboard', {
  en: 'Whiteboard',
});
tool.add('deadout', {
  en: 'Deadout',
  es: 'Muerta',
});
tool.add('alerts_new_practice', {
  en: '{{action}} now available in inspection reports under {{category}}',
});
tool.add('create_password_title', {
  en: 'Create new password',
});
tool.add('pollination_poi_en_desc_label', {
  en: 'Description in English',
});
tool.add('hive_inspection_at', {
  en: '<strong>Hive inspection</strong> at',
  es: '<strong>Inspección de colmena</strong> en',
});
tool.add('clear_out_yard_loading', {
  en: 'Clearing out the yard...',
});
tool.add('tag_management_empty', {
  en: 'No hive ID replacement yet',
});
tool.add('directions', {
  en: 'directions',
});
tool.add('changes_saved_update_delay', {
  en: 'Changes saved. The update may take a few minutes.',
});
tool.add('create_password_invalid_token_error_2', {
  en: 'Forgot Password',
});
tool.add('flag_hive_deadout_desc', {
  en: 'When a hive is flagged as deadout, it is removed from the operation and reflected on the overall deadout counts for the season. Its tag can be re-used. Note that this action is irreversible.',
});
tool.add('autosize_this_column', {
  en: 'Autosize this column',
});
tool.add('check_practice', {
  en: 'Check practice name',
});
tool.add('create_password_label', {
  en: 'New password',
});
tool.add('contact_desc', {
  en: 'For support and suggestions drop us a line at ',
});
tool.add('remove_1_hive', {
  en: 'Remove 1 hive?',
});
tool.add('died', {
  en: 'Died',
});
tool.add('no_pollination_contracts_yet', {
  en: 'No pollination contract yet',
});
tool.add('yards_intersecting_error_msg', {
  en: 'Please edit boundaries to not overlap with other yards.',
});
tool.add('clear_out_and_delete_yard_title', {
  en: 'Clear out and delete?',
});
tool.add('category', {
  en: 'Category',
});
tool.add('no_results_simple', {
  en: 'No results',
});
tool.add('open_focus', {
  en: 'Open + Focus',
});
tool.add('pollination_drops_empty_hint', {
  en: 'No drops were created under this contract. You can create drops<br/> on the pollination map of this contract.',
});
tool.add('delete_yard_title', {
  en: 'Delete yard?',
});
tool.add('terrain', {
  en: 'Terrain',
});
tool.add('yard_management_duplicate', {
  en: 'Duplicate yard',
});
tool.add('duplicate', {
  en: 'Duplicate',
});
tool.add('pollination_map', {
  en: 'Pollination map',
});
tool.add('clear_filters_cont', {
  en: ' or change your criteria.',
});
tool.add('remove_worker_instructions', {
  en: 'Disabling account <b>{{ name }}</b> will log the worker off the mobile app and prevent further use.',
});
tool.add('remove_manager_instructions', {
  en: 'Managers cannot be removed from an operation. Please contact support if you want to remove a manager.',
});
tool.add('save', {
  en: 'Save',
});
tool.add('collapse', {
  en: 'Collapse',
});
tool.add('flag_hive_deadout_success', {
  en: '1 hive was flagged as deadout',
});
tool.add('pollination_block_acres_count', {
  en: '{{count}} acres',
});
tool.add('practice_type_slider', {
  en: 'Slider',
});
tool.add('result', {
  en: 'result',
});
tool.add('clear_out_yard_title', {
  en: 'Clear out yard?',
});
tool.add('last_visit_range_upper_limit_split2', {
  en: 'days or no visits',
});
tool.add('recovered_bulk_beecon', {
  en: '{{number}} beecons are ',
});
tool.add('today', {
  en: 'Today',
  es: 'Hoy',
});
tool.add('clear_out_1_drop', {
  en: 'Clear out 1 drop?',
});
tool.add('hive_movements', {
  en: 'Hive movements',
});
tool.add('pollination_poi_category_security', {
  en: 'Security',
  es: 'Seguridad',
});
tool.add('show_more', {
  en: 'Show more ({{results}})',
});
tool.add('primary_big_cta', {
  en: 'Primary big cta',
});
tool.add('satellite', {
  en: 'Satellite',
});
tool.add('beetrack_alerts_desc_2', {
  en: 'Modifications to the settings will apply only to the following operation: <strong>{{name}}</strong>',
});
tool.add('last_visits_max_days_info', {
  en: 'You can customize the date ranges for your Last Visits map. Any changes made will be immediately reflected in the Last Visits map view for all managers.',
});
tool.add('visits_in_past_30_days', {
  en: 'Visits in the past 30 days',
});
tool.add('view_period_since_last_occurrence_short', {
  en: '# days since last',
});
tool.add('error_message_integer', {
  en: 'The value cannot contain any decimal points',
});
tool.add('yard_management_intersecting_yards', {
  en: 'Intersecting yards found!',
});
tool.add('yard_map_content_card_hide', {
  en: 'Hide {{ content }}',
});
tool.add('recent_searches', {
  en: 'Recent searches',
});
tool.add('group_updated', {
  en: 'Group successfully updated',
});
tool.add('snack_logout_msg', {
  en: 'You have been logged out',
});
tool.add('by', {
  en: 'by',
  es: 'por',
});
tool.add('yard', {
  en: 'yard',
  es: 'apiario',
});
tool.add('unsaved_changes_exit_warning', {
  en: 'You have unsaved changes. Exit anyway?',
});
tool.add('yard_inspection_by', {
  en: '<strong>Yard inspection</strong> by {{name}}.',
});
tool.add('yard_legend_green', {
  en: 'Good. All hives are queenright and alive.',
});
tool.add('timeline_bottom_go_to_top', {
  en: 'Go to top',
  es: 'Ir arriba',
});
tool.add('remove_hive', {
  en: 'Remove hive',
});
tool.add('yard_legend_white', {
  en: ' halo represents a {{beeFlyZoneWithUnits}} bee flying zone.',
});
tool.add('manage_columns_subheader', {
  en: 'Select default columns or add your own custom columns.',
});
tool.add('yard_management_fit_hives', {
  en: 'Fit yard boundaries to its hives',
});
tool.add('page_not_found_message', {
  en: 'The page you are trying to access could not be found.',
});
tool.add('pollination_poi_delete_message', {
  en: 'Deleting <strong>{{name}}</strong> will remove the point of interest from your operation in the platform and the app with its past history.',
});
tool.add('worker_already_active', {
  en: 'Worker with this phone number is already part of your operation',
});
tool.add('alerts_reminder_queenless', {
  en: 'At {{where}}, {{number}} hives have been flagged as queenless for {{when}}',
});
tool.add('mating', {
  en: 'Mating yard',
});
tool.add('change_password_match', {
  en: 'Passwords do not match',
});
tool.add('updated_by', {
  en: 'Updated by',
});
tool.add('pollination_block_delete_message', {
  en: 'Deleting <strong>{{name}}</strong> will remove the Block from your contract and on the map.',
});
tool.add('yard_deleted', {
  en: '{{yard_name}} successfully deleted.',
});
tool.add('season_help_text', {
  en: 'Knowing your season start date allow us to display the right metrics (eg. number of deadouts) at the operation and yard level.',
});
tool.add('notifications', {
  en: 'Notifications',
});
tool.add('reassigned', {
  en: 'reassigned',
  es: 'reasignada',
});
tool.add('action_activated', {
  en: 'Practice enabled successfully',
});
tool.add('create_groups', {
  en: 'Create groups',
});
tool.add('login_title_p2', {
  en: 'Nectar',
});
tool.add('done', {
  en: 'Done',
});
tool.add('no_yards_simple', {
  en: 'No yards',
});
tool.add('last_visit_range_tooltip', {
  en: 'The last visit was between {{from}} and {{to}} days',
});
tool.add('pollination_pdf_worker_contract', {
  en: 'Worker contract',
});
tool.add('pollination_create_drops', {
  en: 'Create drops',
});
tool.add('edit_contract_drop_error', {
  en: 'A drop must always be assigned to a contract.',
});
tool.add('tuesday', {
  en: 'Tue',
});
tool.add('view_worker_last_occurrence', {
  en: 'Worker last occurrence',
});
tool.add('worker_invite_name_name_placeholder', {
  en: 'Enter name',
});
tool.add('clear_out_yard', {
  en: 'Clear out yard',
});
tool.add('remove_group_instructions', {
  en: 'Remove <strong>{{name}}</strong>?',
});
tool.add('hive_grading_medium', {
  en: 'Medium',
});
tool.add('autosize_all_columns', {
  en: 'Autosize all columns',
});
tool.add('avg_hive_movement', {
  en: 'Avg. hive movement',
});
tool.add('health_score_value_tooltip', {
  en: 'Only {{ ignored }} out of {{ total }} hives were used due to low<br/> confidence predictions.<br/><br/> <strong>BETA</strong>',
});
tool.add('hive_structure_brood_boxes__two', {
  en: 'Two',
});
tool.add('page_of_pages', {
  en: 'Page {{current}} of {{total}}',
  es: 'Página {{current}} de {{total}}',
});
tool.add('settings', {
  en: 'Settings',
  es: 'Configuraciones',
});
tool.add('more_options', {
  en: 'More options',
});
tool.add('contract_name_input_label', {
  en: 'Contract',
});
tool.add('pollination_pois_empty', {
  en: 'Place points of interest when editing your contract map.',
});
tool.add('recommended', {
  en: 'Recommended',
});
tool.add('deadout_since', {
  en: '<strong>Deadout</strong> since start of season <br/> {{date}} - now',
});
tool.add('delete_yard_loading', {
  en: 'Deleting the yard...',
});
tool.add('delete_x_yards_success_snackbar', {
  en: 'Yards have been deleted from your operation',
});
tool.add('edit_practice_instructions', {
  en: 'If you wish to change this action, we suggest creating a new one. Editing the following might false some reporting results.',
});
tool.add('hives_in_stock_hint', {
  en: 'Alive tracked hives<br/> in your operation.',
});
tool.add('pdf_success', {
  en: 'PDF successfully generated',
});
tool.add('lost_hives_page_info_part2', {
  en: 'You can flag those hives as Deadout or remove them from your operation.',
});
tool.add('primary_md', {
  en: 'Primary Medium',
});
tool.add('view_average_of_last_visit', {
  en: 'Avg of last visit',
});
tool.add('yard_name', {
  en: 'Yard name',
});
tool.add('address', {
  en: 'Address',
});
tool.add('pollination_poi_en_desc_title', {
  en: 'English description',
});
tool.add('timeline_update_alert_part1', {
  en: 'An update for your activities is available, ',
});
tool.add('hive_structure_bottom_boards', {
  en: 'Bottom boards',
});
tool.add('yard_management_invalid_yard', {
  en: 'Yard with an invalid shape found!',
});
tool.add('hide_metrics', {
  en: 'Hide metrics',
});
tool.add('flag_as_deadout', {
  en: 'Flag as deadout',
});
tool.add('create_password_error', {
  en: 'Create a password with at least 12 characters.',
});
tool.add('hive_structure_honey_super_type__other', {
  en: 'Other',
});
tool.add('overview', {
  en: 'Overview',
});
tool.add('clear_out_renew_contract_desc', {
  en: 'Clearing out drops will flag their hives as lost until they are scanned again. After renewing the contract, these drops will be automatically archived. Note that archiving a drop is an irreversible action.',
});
tool.add('create_new_group', {
  en: 'Create new group',
});
tool.add('heading1', {
  en: 'Heading 1',
});
tool.add('map', {
  en: 'Map',
});
tool.add('recovered_bulk_beehub', {
  en: '{{number}} beehubs are ',
});
tool.add('hive_grading_strong', {
  en: 'Strong',
});
tool.add('for', {
  en: 'for',
});
tool.add('population', {
  en: 'Population',
});
tool.add('wednesday', {
  en: 'Wed',
});
tool.add('tooltip_hum_out_of_range', {
  en: 'Out of {{season}} humidity normal range of {{from}}-{{to}}%',
});
tool.add('battery', {
  en: 'battery',
});
tool.add('online_knowledge_base', {
  en: 'online knowledge base',
});
tool.add('yard_created', {
  en: 'Yard created',
});
tool.add('days_ago', {
  en: '{{number}} days ago',
});
tool.add('no_problematic_yards_msg', {
  en: 'No problematic yards in the operation.',
});
tool.add('reports_elap_description_list_item_1', {
  en: 'Summary of yards per county',
});
tool.add('csv_export', {
  en: 'CSV export',
});
tool.add('hardware', {
  en: 'Hardware',
});
tool.add('renew_contract_details_map', {
  en: 'Contract details and map',
});
tool.add('clear_out_and_delete_yard_button', {
  en: 'Clear out and delete',
});
tool.add('search_location', {
  en: 'Search location',
});
tool.add('tooltip_pending_status', {
  en: 'App not installed or account not created',
});
tool.add('open_menu', {
  en: 'Open the menu',
});
tool.add('pollination_contract_details', {
  en: 'Contract details',
  es: 'Detalles del contrato',
});
tool.add('snack_group_changed_msg', {
  en: 'This yard has been assigned to group(s)',
});
tool.add('yard_legend', {
  en: 'Yard legend',
});
tool.add('hives_structure', {
  en: 'Hive Structure',
});
tool.add('button_group', {
  en: 'Button group',
});
tool.add('assign_contract_success_single', {
  en: 'Yard successfully assigned to {{name}}',
});
tool.add('deleted', {
  en: 'deleted',
  es: 'borrado',
});
tool.add('flagged_as_deadout_bold', {
  en: '<strong>flagged</strong> as <strong>deadouts</strong>',
  es: '<strong>marcado</strong> como <strong>muerta</strong>',
});
tool.add('error_message_min', {
  en: 'The value must be equal to or greater than {{ min }}',
});
tool.add('pollination_new_poi_hint', {
  en: 'Use points of interest to identify additional information localized in a distinct way.',
});
tool.add('radio_buttons', {
  en: 'Radio buttons',
});
tool.add('population_trend', {
  en: 'Population trend',
});
tool.add('pollination_poi_delete_title', {
  en: 'Delete point of interest?',
});
tool.add('remove_count_hives', {
  en: 'Remove {{count}} hives?',
});
tool.add('member', {
  en: 'Member',
});
tool.add('members', {
  en: 'Members',
});
tool.add('pollination_blocks_management_hint_part2', {
  en: ' icon<br/> on the top bar to start creating your<br/> blocks.',
});
tool.add('create_password_invalid_token_error_1', {
  en: 'The link has expired, select',
});
tool.add('clear_out_x_yards_text', {
  en: 'Clearing out these yards will flag their hives as lost until they are scanned again. The yards will appear inactive.',
});
tool.add('total_contracts', {
  en: 'Total contracts',
});
tool.add('queenless_hives', {
  en: 'Queenless Hives',
});
tool.add('timeline_yard_nb_hives_today_tooltip', {
  en: 'This yard currently has {{count}} hives.',
  es: 'Este apiario tiene actualmente {{count}} colmenas.',
});
tool.add('created', {
  en: 'created',
  es: 'crear',
});
tool.add('delete_x_yards_modal_title_single', {
  en: 'Delete 1 yard?',
});
tool.add('hives_included', {
  en: 'Hives included',
  es: 'Colmenas incluida',
});
tool.add('pollination_hives_on_site_overflow_hint', {
  en: 'The number of hives scanned<br/> at the orchard is greater than<br/> the required amount.',
});
tool.add('this_month', {
  en: 'this month',
});
tool.add('archiving', {
  en: 'Archiving',
});
tool.add('hive_structure_frames_per_honey_supers__ten', {
  en: 'Ten frames',
});
tool.add('yard_updated', {
  en: '{{yard_name}} successfully updated.',
});
tool.add('open_settings_for', {
  en: 'Open settings for {{ content }}',
});
tool.add('cost', {
  en: 'Cost',
});
tool.add('yard_self_crossing_error_msg', {
  en: 'Please adjust the yard shape so that it does not intersect itself.',
});
tool.add('hht_yard_legend_description', {
  en: 'Yards are created by scanning a group of hives with Nectar’s mobile app.',
});
tool.add('pollination_drops', {
  en: 'Drops',
  es: 'Apiarios de polinización',
});
tool.add('more', {
  en: 'more',
});
tool.add('yards_all', {
  en: 'All yards',
});
tool.add('select_yards', {
  en: 'Select yards',
});
tool.add('bulk_edit_x_yards', {
  en: 'Bulk edit {{count}} yards',
});
tool.add('draw_selection', {
  en: 'Draw your selection on the map',
});
tool.add('profile_change_name_last_name', {
  en: 'Last name',
});
tool.add('introduced', {
  en: 'Introduced',
});
tool.add('type_crop', {
  en: 'Type of crop',
});
tool.add('lost_since', {
  en: 'Lost since',
});
tool.add('alerts_label_daily_report_mail', {
  en: 'Daily summary email',
});
tool.add('alerts_label_yard_report_mail', {
  en: 'When a yard report is submitted',
});
tool.add('clear_out_x_yards_text_single', {
  en: 'Clearing out this yard will flag its hives as lost until they are scanned again. The yard will appear inactive.',
});
tool.add('event_beehub', {
  en: 'Beehub {{beehub}} is ',
});
tool.add('add_groups', {
  en: 'Add groups',
});
tool.add('per_hive', {
  en: 'per hive',
});
tool.add('export_your_data', {
  en: 'Export your data',
});
tool.add('new_password', {
  en: 'New password',
});
tool.add('see_more', {
  en: 'See more',
});
tool.add('clear_out_x_yards_success_snackbar', {
  en: 'Yards have been cleared out',
});
tool.add('pollination_new_drop_hint', {
  en: 'Use drop to identify a pollination yard localized in a distinct way. Then assign and manage completion from Manager Portal or from the app.',
});
tool.add('contract', {
  en: 'Contract',
  es: 'Contrato',
});
tool.add('new_yard_created_successfully', {
  en: 'New yard created successfully',
});
tool.add('tooltip_delete_yard', {
  en: 'Delete yard',
});
tool.add('alerts_hives_moved', {
  en: '{{number}} hives moved to {{where}}',
});
tool.add('hives', {
  en: 'hives',
  es: 'colmenas',
});
tool.add('holding', {
  en: 'Holding yard',
});
tool.add('yard_modal_name_input_placeholder', {
  en: 'Enter name',
});
tool.add('all_entities', {
  en: 'All {{what}}',
});
tool.add('delete_and_clear_out_yard_message', {
  en: 'Clearing out this yard will flag its hives as lost until they are scanned again. Deleting it will remove the yard from your operation. Note that deleting a yard is irreversible. If the yard has visits, they will be archived.',
});
tool.add('polygon_right_click_to_delete', {
  en: 'Right click to delete',
});
tool.add('operation_name', {
  en: 'Operation name',
});
tool.add('timeline_empty_message', {
  en: "You don't have any recorded activities yet. Start scanning or doing inspections<br/>with the app to view your operation daily activities.",
  es: 'Aún no tienes ninguna actividad registrada. Comience a escanear o realizar inspecciones<br/>con la aplicación para ver las actividades diarias de su operación.',
});
tool.add('view_last_visit_affected_hives', {
  en: 'Hives inspected in last visit',
});
tool.add('through_managers_portal', {
  en: 'through <strong>Managers Portal</strong>',
  es: 'a través del portal de gestores',
});
tool.add('pollination_block', {
  en: 'Block',
});
tool.add('error_message_less_than', {
  en: 'Must be less than {{ less }}.',
});
tool.add('hives_in_stock', {
  en: 'Hives in stock',
});
tool.add('yard_legend_description', {
  en: 'Each dot on represents represents one of your Beecon connected yards. The white halo maps a {{beeFlyZoneWithUnits}} bee flying zone.',
});
tool.add('inspections_reports', {
  en: 'Inspection reports',
});
tool.add('delete_yard_text', {
  en: 'Do you want to delete this yard from your operation? Note that deleting a yard is an irreversible action. If the yard has visits, they will be archived.',
});
tool.add('create', {
  en: 'Create',
});
tool.add('email', {
  en: 'Email',
  es: 'Correo electrónico',
});
tool.add('practice_disable_modal_message_reason_for_deadout', {
  en: "Are you sure you want to disable the '{{name}}' category? Once disabled, workers will no longer be prompted to provide the reason for a deadout when scanning a dead hive. Note that you can always re-enable it later.",
});
tool.add('login_email_placeholder', {
  en: 'Enter email address',
});
tool.add('tooltip_low_battery', {
  en: 'Beecons with 25% battery or less',
});
tool.add('no_hives_msg_deadout', {
  en: 'No deadout hives have been reported at yard {{yard}}.',
});
tool.add('yard_map_content_card_show', {
  en: 'Show {{ content }}',
});
tool.add('hive_activity', {
  en: 'Hive activity',
});
tool.add('day', {
  en: 'day',
});
tool.add('last_visit', {
  en: 'Last visit',
});
tool.add('skip_to_first_page', {
  en: 'Skip to first page',
});
tool.add('tabs', {
  en: 'Tabs',
});
tool.add('tooltip_yard_population_decr', {
  en: 'Population decreasing',
});
tool.add('requiring_attention', {
  en: 'requiring attention',
});
tool.add('last_season', {
  en: 'last season',
});
tool.add('map_view_active_status', {
  en: 'Active status',
});
tool.add('pollination_total_acres_placeholder', {
  en: 'e.g. 0.4047',
});
tool.add('season', {
  en: 'Season',
});
tool.add('send', {
  en: 'Send',
});
tool.add('recovered_beecon', {
  en: "Hive {{hive}}'s beecon is ",
});
tool.add('view_period_since_last_occurrence', {
  en: 'Period since last occurrence',
});
tool.add('clear_out_contract_warn_renew', {
  en: 'Some drops are still active. Before renewing a contract, drops must be cleared out.',
});
tool.add('delete_x_yards_modal_title', {
  en: 'Delete {{count}} yards?',
});
tool.add('all_yards_selected', {
  en: 'All yards selected',
});
tool.add('phone_int', {
  en: 'Phone number (international format)',
});
tool.add('hive_structure_brood_boxes__one', {
  en: 'One',
});
tool.add('yes_cancel', {
  en: 'Yes, cancel',
});
tool.add('to', {
  en: 'to',
  es: 'al',
});
tool.add('set_as', {
  en: 'set as',
});
tool.add('unknown_position', {
  en: 'Unknown position',
});
tool.add('was', {
  en: 'was',
  es: 'fue',
});
tool.add('time-zone', {
  en: 'Time-zone',
});
tool.add('contact_text_cont', {
  en: ' you might find an answer to your problem.',
});
tool.add('contact_us', {
  en: 'contact us',
});
tool.add('lost_hives_learn_more', {
  en: 'You can flag those hives as Deadout or remove them from your operation.',
});
tool.add('forgot_password_success_title', {
  en: 'Instructions sent!',
});
tool.add('clear_out_x_yards_title_single', {
  en: 'Clear out 1 yard?',
});
tool.add('last_visit_weeks_short', {
  en: 'Visit 2 weeks ago',
});
tool.add('pollination_poi_es_desc_title', {
  en: 'Spanish description',
});
tool.add('mobile_summary_warning', {
  en: 'The yard metrics tab is currently not available on smaller screen. Please, login from a tablet or your computer',
});
tool.add('worker_invite_429_error_action', {
  en: 'Contact us',
});
tool.add('number_of_hives', {
  en: 'Number of hives',
});
tool.add('hive_structure_honey_super_type', {
  en: 'Honey super type',
});
tool.add('last_season_trend', {
  en: 'Last season',
});
tool.add('yard_management_orphan_hive', {
  en: 'Unassigned hives found.',
});
tool.add('yard_management_over_parented_hive', {
  en: "A hive can't belong to multiple yards. It will automatically be assigned to one of them.",
});
tool.add('rename', {
  en: 'rename',
});
tool.add('new_practice_created', {
  en: 'New Practice',
});
tool.add('charged', {
  en: 'charged',
});
tool.add('yard_type', {
  en: 'Yard type',
});
tool.add('text_invitation', {
  en: 'text invitation',
});
tool.add('assign_contract_disabled_all_yards', {
  en: "You can't assign a contract to all yards at once, in your operation.",
});
tool.add('assign_contract_confirmation_submit', {
  en: 'Continue',
});
tool.add('action_bar', {
  en: 'Action bar',
});
tool.add('alert_healthy', {
  en: 'Hive {{which}} has been detected as queenright again',
});
tool.add('pollination_hives_on_site', {
  en: 'Hives on site',
});
tool.add('clear_out_yard_only_button', {
  en: 'Clear out only',
});
tool.add('map_view_last_visits', {
  en: 'Last visits',
});
tool.add('yard_type_alt_1', {
  en: 'Type of yard',
});
tool.add('assign_yard_contract', {
  en: 'Assign 1 yard to a contract',
});
tool.add('hive_inspection', {
  en: 'Hive inspection',
  es: 'Inspección de colmena',
});
tool.add('winter_mortality', {
  en: 'Winter mortality {{year}}',
});
tool.add('clear_filters', {
  en: 'Clear all filters',
});
tool.add('pollination_hives_on_map_complete_hint', {
  en: 'Contract map is complete.',
});
tool.add('at_time', {
  en: 'at',
});
tool.add('reason_of_deadout', {
  en: 'Reason of deadout',
});
tool.add('pollination_bees_in_greater_than_bees_out', {
  en: 'The bees in date must be before the bees out date',
});
tool.add('resend_invite', {
  en: 'Resend invite',
});
tool.add('assign', {
  en: 'Assign',
});
tool.add('text_message', {
  en: 'text message',
});
tool.add('view_last_occurrence_date_short', {
  en: 'Last occurrence date',
});
tool.add('profile_change_name_modal_title', {
  en: 'Edit name',
});
tool.add('notify_by_email_to', {
  en: 'By email to {{email}}',
});
tool.add('action_deactivated', {
  en: 'Practice disabled successfully',
});
tool.add('email_recap', {
  en: 'Email recap',
});
tool.add('error_message_email', {
  en: 'Please enter a valid email',
});
tool.add('pollination_block_acres_count_abr', {
  en: '{{acres}} Ac.',
});
tool.add('hive_status_empty', {
  en: 'No change of hive status yet',
});
tool.add('pollination', {
  en: 'Pollination',
});
tool.add('remove_yard_instructions', {
  en: 'Deleting ',
});
tool.add('login_slide_3_title', {
  en: 'Stay up to date with activities',
});
tool.add('help_menu_intercom', {
  en: 'Chat with support',
});
tool.add('search', {
  en: 'Search',
});
tool.add('view_worker_last_occurrence_short', {
  en: 'Worker last occurrence',
});
tool.add('phone_number_already_taken', {
  en: 'Phone number is already part of the operation',
});
tool.add('hive_structure_honey_super_type__deep', {
  en: 'Deep',
});
tool.add('pollination_drops_hives_count', {
  en: '{{count}} hives',
  es: '{{count}} colmenas',
});
tool.add('group_removed', {
  en: 'Group successfully removed',
});
tool.add('green', {
  en: 'Green symbol. ',
});
tool.add('pollination_blocks_number', {
  en: '# of blocks',
});
tool.add('snack_non_field_errors', {
  en: 'Something went wrong',
});
tool.add('devices_in_need', {
  en: 'Devices in the following yards may require your attention',
});
tool.add('humidity', {
  en: 'Humidity',
});
tool.add('create_new_yard', {
  en: 'Create new yard',
});
tool.add('recovered_beecon_low_battery', {
  en: "Hive {{hive}}'s beecon battery is ",
});
tool.add('pollination_contact_name', {
  en: 'Contact name',
});
tool.add('pollination_hives_on_map_overflow_hint', {
  en: 'The number of hives on<br/> the map is greater than<br/> the required amount.',
});
tool.add('last_visits_max_days_learn_more', {
  en: 'Learn more about last visit map',
});
tool.add('added_at', {
  en: 'added at',
});
tool.add('manage_columns', {
  en: 'Manage columns',
});
tool.add('pollination_total_price', {
  en: 'Total price',
});
tool.add('change_password', {
  en: 'Change my password',
});
tool.add('renew_contract_name', {
  en: 'Renew {{contract}}',
});
tool.add('search_open', {
  en: 'Open search',
});
tool.add('indoor wintering', {
  en: 'Indoor wintering',
});
tool.add('trend', {
  en: 'Trend',
});
tool.add('action', {
  en: 'Action',
});
tool.add('hive_structure_frame_base__other', {
  en: 'Other',
});
tool.add('remove', {
  en: 'Remove',
});
tool.add('yard_legend_yellow', {
  en: 'Worrying. At least 15% of hives are queenless or dead.',
});
tool.add('pollination_pdf_manager_contract', {
  en: 'Manager contract',
});
tool.add('error_message_more_than', {
  en: 'Must be more than {{ more }}.',
});
tool.add('mortality_description', {
  en: 'Total number of hives flagged as deadout during the season.',
});
tool.add('delete_yard', {
  en: 'Delete yard',
});
tool.add('practices_empty', {
  en: 'No practices.',
});
tool.add('yard_inspections', {
  en: 'Yard inspections',
});
tool.add('submit', {
  en: 'Submit',
});
tool.add('view_sum_of_last_visit', {
  en: 'Sum of last visit',
});
tool.add('clear_out_yard_help_link', {
  en: 'Learn about clearing out yards',
});
tool.add('check_input', {
  en: 'Check input',
});
tool.add('activity_empty', {
  en: 'No activities yet',
  es: 'El historial esta vacío',
});
tool.add('unknown', {
  en: 'Unknown',
});
tool.add('map_search_placeholder', {
  en: 'Search by Address, City, GPS...',
});
tool.add('pollination_block_acres_label', {
  en: 'Acres',
});
tool.add('save_changes', {
  en: 'Save changes',
});
tool.add('inactive_yards', {
  en: 'Inactive yards',
});
tool.add('new_hive', {
  en: 'New hives tracked',
});
tool.add('toast_map_error', {
  en: 'We could not load the map, please retry later.',
});
tool.add('low_battery', {
  en: 'battery is low',
});
tool.add('alerts_hive_inspection', {
  en: 'Applied to {{number}} of the {{total}} hives at {{where}}',
});
tool.add('pollination_drop', {
  en: 'Drop',
});
tool.add('yard_filters', {
  en: 'Yard filters',
});
tool.add('active', {
  en: 'Active',
});
tool.add('no_hives', {
  en: 'Empty yard',
});
tool.add('precipitation', {
  en: 'Precipitation',
});
tool.add('remove_group_instructions_having_yards', {
  en: 'Removing group <strong>{{name}}</strong> will un-assign all <strong>{{nbYards}}</strong> yards using it.',
});
tool.add('change_password_twelve_characters', {
  en: 'New password should contain at least 12 characters',
});
tool.add('feature_unavailable', {
  en: 'Feature unavailable',
});
tool.add('login_slide_3_text', {
  en: "Stay informed with real-time updates whenever your team completes tasks, whether it's yard or hive inspections, or simply relocating hives.",
});
tool.add('notify_by_phone_to', {
  en: 'By SMS to {{phone_number}} (sent from 8:00 to 20:00)',
});
tool.add('type_styles', {
  en: 'Type styles',
});
tool.add('confirm_password', {
  en: 'Confirm new password',
});
tool.add('flag_hives_deadout_success_all', {
  en: 'All hives were flagged as deadouts',
});
tool.add('yard_management_recenter_to_yard', {
  en: 'Recenter map to this yard',
});
tool.add('address_placeholder', {
  en: 'Enter address',
});
tool.add('pollination_drop_delete_message', {
  en: 'Deleting <strong>{{name}}</strong> will remove the drop from your operation in the platform and the app with its past history.',
});
tool.add('lost_hives_page_title', {
  en: 'Lost hives {{ lost_hives}}',
});
tool.add('timeline_bottom_message', {
  en: 'No other activities recorded',
  es: 'No se registraron otras actividades',
});
tool.add('view_drop_page', {
  en: 'View drop page',
});
tool.add('view_number_of_occurrences_since_season_start_short', {
  en: '# since season start',
});
tool.add('custom_columns', {
  en: 'Custom Columns',
});
tool.add('recovered_bulk_deadout', {
  en: '{{number}} hives recovered from ',
});
tool.add('clear_out_and_delete_yard_message', {
  en: 'Clearing out this yard will flag its hives as lost until they are scanned again. The yard will appear inactive.<br/>Deleting it will remove the yard from your operation. Note that deleting a yard is irreversible. If the yard has visits, they will be archived.',
});
tool.add('no_hives_deadout', {
  en: 'No deadout hives',
});
tool.add('yard_inspection_at', {
  en: '<strong>Yard inspection</strong> at',
  es: '<strong>Inspección del apiario</strong> en',
});
tool.add('category_non_editable_tooltip', {
  en: 'This category and its practices are non-editable and cannot be disabled, as they are essential for the proper functioning of the Nectar system.',
});
tool.add('unsaved_changes_exit_modal_title', {
  en: 'Cancel changes',
});
tool.add('alerts_deadouts', {
  en: '{{number}} deadouts flagged at {{where}}',
});
tool.add('tooltips', {
  en: 'Tooltips',
});
tool.add('assign_all', {
  en: 'Assign all yards',
});
tool.add('edit', {
  en: 'Edit',
});
tool.add('price', {
  en: 'Price',
});
tool.add('filter', {
  en: 'Filter',
});
tool.add('last_visit_range_upper_limit_tooltip', {
  en: 'The last visit was more than {{from}} days ago',
});
tool.add('pollination_contact_name_placeholder', {
  en: 'e.g. Stanley Hawkins',
});
tool.add('crop_type_input_placeholder', {
  en: 'e.g. Watermelon',
});
tool.add('low_frame_bees_desc', {
  en: 'Set the maximum frame of bees to be considered low.',
});
tool.add('delete_yards', {
  en: 'Delete yards',
});
tool.add('create_visits', {
  en: 'Create visits',
});
tool.add('edit_yard_type', {
  en: 'Edit yard type',
});
tool.add('cancel', {
  en: 'Cancel',
});
tool.add('edit_boundaries', {
  en: 'Edit boundaries',
});
tool.add('alert_label_hive_moved', {
  en: 'When a hive has been moved',
});
tool.add('email_placeholder', {
  en: 'e.g. <EMAIL>',
});
tool.add('about', {
  en: 'About',
});
tool.add('pollination_bees_in', {
  en: 'Bees in',
  es: 'Abejas dentro',
});
tool.add('yards_selected', {
  en: '{{number}} selected',
});
tool.add('clear_out_and_archive', {
  en: 'Clear out and archive',
});
tool.add('lost_hives_help_center', {
  en: 'Learn more about lost hives',
});
tool.add('tooltip_yard_population_incr', {
  en: 'Population increasing',
});
tool.add('unsaved_changes_exit_modal_reject', {
  en: 'No, keep me here',
});
tool.add('monday', {
  en: 'Mon',
});
tool.add('pollination_create', {
  en: 'Create',
});
tool.add('yard_management_x_errors_found', {
  en: '{{count}} issues',
});
tool.add('operation_average', {
  en: 'Operation average',
});
tool.add('pollination_drops_number', {
  en: '# of drops',
});
tool.add('operation_profile_tab_title', {
  en: 'OP Profile',
});
tool.add('timeline_bottom_hive_message', {
  en: 'No other activities recorded for this hive',
  es: 'No se registraron otras actividades para esta colmena',
});
tool.add('checkbox', {
  en: 'Checkbox',
});
tool.add('archive', {
  en: 'Archive',
});
tool.add('filter_yards', {
  en: 'Filter yards',
});
tool.add('fixing_method', {
  en: 'Fixing method',
});
tool.add('tooltip_cannot_drag_active_drop', {
  en: 'Cannot drag drops <br/> with hives.',
});
tool.add('all_status', {
  en: 'All status',
});
tool.add('all_activity', {
  en: 'All activity',
});
tool.add('yard_visit_by', {
  en: '<strong>Yard visit</strong> by {{name}}',
});
tool.add('total_yards', {
  en: 'Total yards',
});
tool.add('export_contract_worker_desc', {
  en: '(exclude the grower and pricing information)',
});
tool.add('pollination_hives_required_header', {
  en: 'Required',
});
tool.add('reports', {
  en: 'Reports',
});
tool.add('welcome', {
  en: 'Welcome',
});
tool.add('arrived', {
  en: 'Arrived',
});
tool.add('track_more', {
  en: 'Track more',
});
tool.add('hive_created_at', {
  en: '<strong>Hive created</strong> at',
});
tool.add('event_detected', {
  en: 'Hive {{hive}} is now detected ',
});
tool.add('and', {
  en: 'and',
  es: 'y',
});
tool.add('error_message_range', {
  en: 'The value must be between {{ min }} and {{ max }}',
});
tool.add('tooltip_temp_out_of_range', {
  en: 'Out of {{season}} temperature normal range of {{from}}-{{to}}°{{units}}',
});
tool.add('new_yard_created', {
  en: 'New Yard',
});
tool.add('add_worker', {
  en: 'Add worker',
});
tool.add('operation_profile', {
  en: 'Operation profile',
});
tool.add('snack_wrong_new_password', {
  en: 'The new password is either too common or too similar to your current password',
});
tool.add('no_pollination_archived_contracts_yet', {
  en: 'No archived contracts',
});
tool.add('total_drops', {
  en: 'Total drops',
});
tool.add('edit_practice', {
  en: 'Edit {{category}}',
});
tool.add('pollination_hives_operations', {
  en: 'Hives operations',
});
tool.add('fixing_method_details', {
  en: 'This hive was created by <br/>fixing a deadout hive.',
});
tool.add('error_value_missing', {
  en: '{{ label}} is required',
});
tool.add('hive_added_to_yard', {
  en: 'Hives Moved',
});
tool.add('event_beehub_new_yard', {
  en: 'A new yard is added to your operation!',
});
tool.add('hive_structure_queen_excluder__metal', {
  en: 'Metal',
});
tool.add('no_yards_msg', {
  en: 'No yard matches your search.',
});
tool.add('weather_icons', {
  en: 'Weather icons',
});
tool.add('no_results', {
  en: `No results for "{{searchValue}}"`,
});
tool.add('member_list_first_name', {
  en: 'First Name',
});
tool.add('member_list_last_name', {
  en: 'Last Name',
});
tool.add('member_list_phone_number', {
  en: 'Phone Number',
});
tool.add('member_list_status', {
  en: 'Status',
});
tool.add('member_list_crew_name', {
  en: 'Crew name',
});
tool.add('member_list_last_report', {
  en: 'Last Report',
});
tool.add('member_list_pending', {
  en: 'Pending',
});
tool.add('member_list_active', {
  en: 'Active',
});
tool.add('member_list_assign_crew', {
  en: 'Assign to crew',
});
tool.add('member_list_manager', {
  en: 'Manager',
});
tool.add('member_list_worker', {
  en: 'Worker',
});
tool.add('member_list_tooltip_manager', {
  en: 'Managers cannot be removed from an operation',
});
tool.add('members_selected_count', {
  en: '{{count}} members selected',
});
tool.add('member_selected_1', {
  en: '1 member selected',
});
tool.add('member_list_role', {
  en: 'Role',
});
tool.add('crews', {
  en: 'Crews',
});
tool.add('no_crews', {
  en: 'No crews added yet',
});
tool.add('no_crews_msg', {
  en: 'Add crews to your operation so you can start planning visits.',
});
tool.add('crew_section_description', {
  en: 'Create crews and assign members to them. Crew members will also see visits that were assigned to their crew via the mobile app.',
});
tool.add('crew_create_button', {
  en: 'Create crews',
});
tool.add('crew_modal_title_create', {
  en: 'Create crew',
});
tool.add('crew_modal_subtitle_create', {
  en: 'Crews are groups of workers that can be assigned to visits.',
});
tool.add('crew_modal_title_update', {
  en: 'Edit crew',
});
tool.add('crew_modal_input_name', {
  en: 'Crew name',
});
tool.add('crew_modal_input_placeholder', {
  en: 'Enter a crew name',
});
tool.add('crew_modal_name_unique_error', {
  en: 'This crew name is already assigned to another crew.',
});
tool.add('crew_added_snackbar_success', {
  en: '{{name}} was added to your operation',
});
tool.add('crews_list_name', {
  en: 'Crew name',
});
tool.add('crew_edit', {
  en: 'Edit crew ',
});
tool.add('crew_delete', {
  en: 'Delete crew ',
});
tool.add('crew_reassign_all_visits', {
  en: 'Reassign all visits',
});
tool.add('crews_autocomplete_input_label', {
  en: 'Crews',
});
tool.add('crews_autocomplete_input_placeholder', {
  en: 'Select a crew ',
});
tool.add('crew_assign_modal_title_count', {
  en: 'Assign {{count}} members to a crew',
});
tool.add('crew_assign_modal_title_1', {
  en: 'Assign 1 member to a crew',
});
tool.add('crew_assign_modal_subtitle', {
  en: 'Members can only be assigned to 1 crew at a time. They will automatically be unassigned to a previously assigned crew.',
});
tool.add('crew_assign_success_other', {
  en: '{{count}} members assigned to {{crewName}}',
});
tool.add('crew_assign_success_one', {
  en: '{{count}} member assigned to {{crewName}}',
});
tool.add('crew_unassign_success_other', {
  en: '{{count}} members unassigned from crew',
});
tool.add('crew_unassign_success_one', {
  en: '{{count}} member unassigned from crew',
});
tool.add('crew_updated_snackbar_success', {
  en: '{{name}} has been updated',
});
tool.add('crew_delete_snackbar_success', {
  en: '{{name}} has been deleted',
});
tool.add('crew_modal_title_delete', {
  en: 'Delete {{name}}?',
});
tool.add('crew_modal_subtitle_delete', {
  en: 'Deleting a crew cannot be undone. If the crew has active visits, they will be deleted. You can choose to reassign visits to another crew to prevent their deletion.',
});
tool.add('crew_reassign_all_visits_title', {
  en: 'Reassign all visits',
});
tool.add('crew_reassign_all_visits_subtitle', {
  en: 'All active visits assigned to {{name}} will be reassigned to the other selected crew.',
});
tool.add('crew_reassign_all_visits_to_selected_crew', {
  en: 'Select crew to reassign all visits to',
});
tool.add('crew_reassign_all_visits_snackbar_success', {
  en: 'All visits have been reassigned from {{fromCrewName}} to {{toCrewName}}',
});
tool.add('crew_reassign_visits', {
  en: 'Reassign visits',
});
tool.add('resources_help_center', {
  en: 'Help Center',
});
tool.add('resource_hardware_store', {
  en: 'Hardware store',
});
tool.add('resources_eula', {
  en: 'EULA',
});
tool.add('resources_privacy', {
  en: 'Privacy',
});
tool.add('title', {
  en: 'Title',
});
tool.add('start', {
  en: 'Start',
});
tool.add('end', {
  en: 'End',
});
tool.add('splitting', {
  en: 'Splitting',
});
tool.add('supering', {
  en: 'Supering',
});
tool.add('winterization', {
  en: 'Winterization',
});
tool.add('honey_harvest', {
  en: 'Honey harvest',
});
tool.add('queen_management', {
  en: 'Queen management',
});
tool.add('general', {
  en: 'General',
});
tool.add('update', {
  en: 'Update',
});
tool.add('view_media', {
  en: 'View media',
});
tool.add('audio_media', {
  en: 'Audio',
});
tool.add('media_not_found', {
  en: 'The image or audio file is currently not available. Please try again later.',
});
tool.add('filter_actions', {
  en: 'Filter actions',
});
tool.add('add_action', {
  en: 'Add action',
});

export default tool.getTranslationJson();
