<?xml version="1.0" encoding="UTF-8"?>
<svg width="203px" height="80px" viewBox="0 0 203 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
    <title>illu</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="13.9130435" cy="13.9130435" r="13.9130435"></circle>
        <filter x="-57.5%" y="-57.5%" width="215.0%" height="215.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.83   0 0 0 0 0.83   0 0 0 0 0.83  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-4" cx="13.9130435" cy="13.9130435" r="13.9130435"></circle>
        <filter x="-57.5%" y="-57.5%" width="215.0%" height="215.0%" filterUnits="objectBoundingBox" id="filter-6">
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.83   0 0 0 0 0.83   0 0 0 0 0.83  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-7" cx="13.9130435" cy="13.9130435" r="13.9130435"></circle>
        <filter x="-57.5%" y="-57.5%" width="215.0%" height="215.0%" filterUnits="objectBoundingBox" id="filter-9">
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.83   0 0 0 0 0.83   0 0 0 0 0.83  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Mocks" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="MODAL-LEGEND(MOB)" transform="translate(-48.000000, -192.000000)">
            <g id="illu" transform="translate(48.000000, 192.000000)">
                <g id="Map/dot-zone">
                    <circle id="zone" fill-opacity="0.75" fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
                    <g id="Colors/bothersome" stroke-width="1" transform="translate(26.086957, 26.086957)">
                        <mask id="mask-2" fill="white">
                            <use xlink:href="#path-1"></use>
                        </mask>
                        <g id="mask">
                            <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-1"></use>
                            <circle stroke="#FFFFFF" stroke-width="4" cx="13.9130435" cy="13.9130435" r="15.9130435"></circle>
                        </g>
                        <g id="Colors/good" mask="url(#mask-2)" fill="#A4D96C">
                            <g transform="translate(-1.739130, -1.739130)" id="Rectangle">
                                <rect x="0" y="0" width="31.3043478" height="31.3043478"></rect>
                            </g>
                        </g>
                        <text id="code" mask="url(#mask-2)" font-family="OpenSans-Bold, Open Sans" font-size="10" font-weight="bold" letter-spacing="-0.25"></text>
                    </g>
                </g>
                <g id="Map/dot-zone" transform="translate(61.000000, 0.000000)">
                    <circle id="zone" fill-opacity="0.75" fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
                    <g id="Colors/bothersome" stroke-width="1" transform="translate(26.086957, 26.086957)">
                        <mask id="mask-5" fill="white">
                            <use xlink:href="#path-4"></use>
                        </mask>
                        <g id="mask">
                            <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-4"></use>
                            <circle stroke="#FFFFFF" stroke-width="4" cx="13.9130435" cy="13.9130435" r="15.9130435"></circle>
                        </g>
                        <g id="Colors/worrysome" mask="url(#mask-5)" fill="#FFCF80">
                            <g transform="translate(-1.739130, -1.739130)" id="Rectangle">
                                <rect x="0" y="0" width="31.3043478" height="31.3043478"></rect>
                            </g>
                        </g>
                        <text id="code" mask="url(#mask-5)" font-family="OpenSans-Bold, Open Sans" font-size="10" font-weight="bold" letter-spacing="-0.25"></text>
                    </g>
                </g>
                <g id="Map/dot-zone" transform="translate(123.000000, 0.000000)">
                    <circle id="zone" fill-opacity="0.75" fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
                    <g id="Colors/bothersome" stroke-width="1" transform="translate(26.086957, 26.086957)">
                        <mask id="mask-8" fill="white">
                            <use xlink:href="#path-7"></use>
                        </mask>
                        <g id="mask">
                            <use fill="black" fill-opacity="1" filter="url(#filter-9)" xlink:href="#path-7"></use>
                            <circle stroke="#FFFFFF" stroke-width="4" cx="13.9130435" cy="13.9130435" r="15.9130435"></circle>
                        </g>
                        <g id="Colors/critical" mask="url(#mask-8)" fill="#D9766D">
                            <g transform="translate(-1.739130, -1.739130)" id="Rectangle">
                                <rect x="0" y="0" width="31.3043478" height="31.3043478"></rect>
                            </g>
                        </g>
                        <text id="code" mask="url(#mask-8)" font-family="OpenSans-Bold, Open Sans" font-size="10" font-weight="bold" letter-spacing="-0.25"></text>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>