<?xml version="1.0" encoding="UTF-8"?>
<svg width="60px" height="60px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59.1 (86144) - https://sketch.com -->
    <title>marker-dynamic-grey</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="16" cy="16" r="16"></circle>
        <filter x="-50.0%" y="-50.0%" width="200.0%" height="200.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="0" dy="0" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.83   0 0 0 0 0.83   0 0 0 0 0.83  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="marker-dynamic-grey" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#FFFFFF" opacity="0" x="0" y="0" width="60" height="60"></rect>
        <g id="Map/dot-Copy-5" transform="translate(14.000000, 14.000000)">
            <g id="Colors/bothersome">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <g id="mask">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-1"></use>
                    <circle stroke="#FFFFFF" stroke-width="4" cx="16" cy="16" r="18"></circle>
                </g>
                <g id="Colors/disabled" mask="url(#mask-2)" fill="#D4D4D4">
                    <g transform="translate(-2.000000, -2.000000)" id="Rectangle">
                        <rect x="0" y="0" width="36" height="36"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>