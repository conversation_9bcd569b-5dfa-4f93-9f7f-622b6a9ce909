import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

import { Loading } from '@components/common/Loading';
import { isEmptyObject } from '@helpers/deprecated/isEmptyObject';
import { makeFetchRequestThunk } from '@redux/deprecated/actions';

/**
 *
 * inject fetchedData into Component props
 * usage
 *  withFetchedDataFromUrl('api url')(Component);
 *  withFetchedDataFromUrl('api url', 0, 'propertyName')(Component);
 *
 * @param url
 * @param cacheForSeconds
 * @param injectedPropName (default to fetchedData)
 * @returns {function(*)}
 */
export const withFetchedDataFromUrl = (url, cacheForSeconds = 0, injectedPropName = 'fetchedData') => {
  // console.log("injectedPropName",injectedPropName)
  return (WrappedComponent) => {
    // create a new abstract container component
    class FetchData extends React.Component {
      componentDidMount() {
        const { dispatch } = this.props;
        dispatch(makeFetchRequestThunk(url, cacheForSeconds, injectedPropName));
      }
      /**
       *
       *  render the wrapped component with fresh data:
       *  @note we pass through any additional props
       *  @returns {*} Wrapped
       */

      render() {
        const { fetchedData, ...passThroughProps } = this.props;

        if (isEmptyObject(fetchedData)) {
          return <Loading />;
        }
        return <WrappedComponent {...{ [injectedPropName]: fetchedData }} {...passThroughProps} />;
      }
    }

    // easy debugging:
    FetchData.displayName = `withFetchedData(${WrappedComponent.displayName})`;
    const mapStateToProps = (state) => ({
      fetchedData: url in state.fetchReducer.fetched ? state.fetchReducer.fetched[url].data : {},
    });

    FetchData.propTypes = {
      fetchedData: PropTypes.oneOfType([PropTypes.object, PropTypes.array]).isRequired,
      dispatch: PropTypes.func.isRequired,
    };

    return connect(mapStateToProps, null)(FetchData);
  };
};
